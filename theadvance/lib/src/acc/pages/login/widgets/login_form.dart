// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import 'package:theadvance/src/core/nd_progresshud/nd_progresshud.dart';
import 'package:theadvance/src/core/utils/nd_utils.dart';
import 'package:theadvance/src/data/models/nd_models.dart';
import '../../../../core/params/request_params.dart';
import '../../../../presentation/widgets/widgets.dart';
import '../../../widgets/base_button.dart';
import '../../authentication/bloc/index.dart';
import '../../tabbar/ui/tab_bar.dart';
import '../bloc/bloc.dart';
import 'text_field_login.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({Key? key}) : super(key: key);

  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  List<OrganizationModel> dropDownListValue = [];
  String keyDropDown = 'none';
  int? index;

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  bool isEmptyPhoneNumber = true;

  @override
  void initState() {
    super.initState();
  }

  void _actionLogin() {
    context.read<LoginBloc>().add(
      LoginStarted(
        username: _usernameController.text.trim(),
        password: _passwordController.text.trim(),
        idOrganizationId: index != null
            ? dropDownListValue[index!].id ?? 'none'
            : 'none',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocListener<LoginBloc, LoginState>(
        listener: (context, state) {
          if (state is LoginLoadOrganizationSuccess) {
            dropDownListValue = state.data?.data?.listOrganization ?? [];
          }
          if (state is LoginLoadFailure) {
            Alert.showAlert(
              AlertParams(
                context,
                state.error,
                acceptButton: context.l10n.close,
              ),
            );
          }
          if (state is LoginLoadOrganizationFailure) {
            if (state.code != ErrorCodes.tokenExpired)
              Alert.showAlertConfirm(
                AlertConfirmParams(
                  context,
                  message: state.error ?? context.l10n.unknown,
                  cancelButton: context.l10n.back,
                  confirmText: context.l10n.tryAgain,
                  onPressedCancel: () => Navigator.of(context).pop(),
                  onPressed: () {
                    context.read<LoginBloc>().add(LoginLoadedOrganization());
                    Navigator.of(context).pop();
                  },
                ),
              );
          }
          if (state is LoginLoadSuccess) {
            showTabBar(context: context, loginModel: state.loginModel);
            context.read<AccAuthenticationBloc>().add(
              AuthenticationLoggedIn(state.loginModel),
            );
          }
        },
        child: BlocBuilder<LoginBloc, LoginState>(
          builder: (context, state) {
            return ProgressHud(
              isShowLoading: state is LoginLoadInProgress,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 55.0,
                    left: 30.0,
                    right: 30.0,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: _buildBodyNew(context, state),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildBodyNew(BuildContext context, LoginState state) {
    return <Widget>[
      const SizedBox(height: 10),
      Center(
        child: index != null
            ? EzCachedNetworkImage(
                height: MediaQuery.sizeOf(context).width / 3.5,
                fit: BoxFit.fill,
                imageUrl: dropDownListValue[index!].logo ?? '',
              )
            : Container(),
      ),
      Container(
        margin: const EdgeInsets.only(top: 40),
        child: TextFieldLoginCustom(
          controller: _usernameController,
          keyboardType: TextInputType.emailAddress,
          hintText: context.l10n.accountLogin,
          onChangedText: (text) {
            setState(() {});
          },
        ),
      ),
      Container(
        margin: const EdgeInsets.only(top: 20),
        child: TextFieldLoginCustom(
          controller: _passwordController,
          obscureText: true,
          hintText: context.l10n.password,
          onChangedText: (text) {
            setState(() {});
          },
        ),
      ),
      Container(
        margin: const EdgeInsets.only(top: 40),
        child: _buildDropdownMenu(),
      ),
      const SizedBox(height: 20),
      Container(
        margin: const EdgeInsets.symmetric(vertical: 20),
        child: buildButtonApp(
          context: context,
          onPressedCallBack: _actionLogin,
          titleButton: context.l10n.login.toUpperCase(),
        ),
      ),
    ];
  }

  Widget _buildDropdownMenu() {
    return GestureDetector(
      onTap: () => print('object'),
      child: DropdownButtonFormField(
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w400,
          color: Theme.of(context).primaryColorDark,
        ),
        hint: Text(context.l10n.branchSelectionStep),
        initialValue: index != null ? dropDownListValue[index!].name : null,
        onChanged: (text) {
          for (var item in dropDownListValue) {
            if (item.name == text) {
              index = dropDownListValue.indexOf(item);
              setState(() {
                keyDropDown = item.name ?? '';
              });
            }
          }
        },
        items: dropDownListValue.map((item) {
          return DropdownMenuItem(
            value: item.name,
            child: Text(item.name ?? ''),
          );
        }).toList(),
      ),
    );
  }

  //  bool _emailValidate() {
  //    final email = _usernameController.text;
  //    if (email.isNotEmpty) {
  //      if (email.contains(' ')) {
  //        showAlert(context, context.l10n.emailHaveWhiteSpace);
  //        return false;
  //      }
  //      if (!email.emailValidator()) {
  //        showAlert(context, context.l10n.emailWrongFormat);
  //        return false;
  //      }
  //    } else {
  //      showAlert(context, context.l10n.emailEmpty);
  //      return false;
  //    }
  //    return true;
  //  }
}
