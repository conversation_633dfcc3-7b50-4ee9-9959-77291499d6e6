// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../product_confirm/widgets/base_layout.dart';

@RoutePage()
class FontSizePage extends StatefulWidget {
  const FontSizePage({super.key, required this.scale});
  final double scale;
  @override
  State<FontSizePage> createState() => _FontSizePageState();
}

class _FontSizePageState extends State<FontSizePage> {
  final ValueNotifier<int> selectedIndex = ValueNotifier(1);
  List<FontSizeItem> fontSizeList(final BuildContext context) => [
    FontSizeItem(context.l10n.smallText, 0.8),
    FontSizeItem(context.l10n.defaultText, 1.0),
    FontSizeItem(context.l10n.mediumText, 1.2),
    FontSizeItem(context.l10n.largeText, 1.4),
    FontSizeItem(context.l10n.superLargeText, 1.6),
  ];

  @override
  Widget build(final BuildContext context) {
    selectedIndex.value = fontSizeList(
      context,
    ).indexWhere((final e) => e.scale == widget.scale);

    return BaseLayout(
      title: ValueListenableBuilder(
        valueListenable: selectedIndex,
        builder: (final context, final vSelectIndex, final child) {
          final item = fontSizeList(context)[vSelectIndex];
          return Text(
            context.l10n.fontSize,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 20 * item.scale,
            ),
          );
        },
      ),
      leading: IconButton(
        onPressed: () {
          context.router.popForced(
            fontSizeList(context)[selectedIndex.value].scale,
          );
        },
        icon: EZResources.image(
          ImageParams(
            name: AppIcons.icBack,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ValueListenableBuilder(
              valueListenable: selectedIndex,
              builder: (final context, final vIndex, final child) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  //  spacing: 10,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...List.generate(fontSizeList(context).length, (final i) {
                      final item = fontSizeList(context)[i];
                      return GestureDetector(
                        onTap: () {
                          selectedIndex.value = i;
                        },
                        child: _buildInfoField(
                          item.title,
                          context.l10n.appName,
                          item.scale,
                          vIndex == i,
                          i == (fontSizeList(context).length) - 1,
                        ),
                      );
                    }),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoField(
    final String title,
    final String value,
    final double size, [
    final bool isForward = false,
    final bool isEndField = false,
  ]) {
    return ColoredBox(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.only(left: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Row(
                spacing: 16,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 15,
                    ),
                  ),
                  if (value.isNotEmpty)
                    Expanded(
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          value,
                          textAlign: TextAlign.start,
                          maxLines: 1,
                          style: Theme.of(context).textTheme.labelLarge
                              ?.copyWith(
                                fontSize: 14 * size,
                                color: Colors.black45,
                              ),
                        ),
                      ),
                    )
                  else
                    Expanded(
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          context.l10n.notUpdated,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                fontStyle: FontStyle.italic,
                                color: Colors.black54,
                              ),
                        ),
                      ),
                    ),
                  if (isForward)
                    EZResources.image(
                      ImageParams(
                        name: AppIcons.icSelectFont,
                        size: const ImageSize.square(24),
                      ),
                    )
                  else
                    const SizedBox(width: 24),
                ],
              ),
            ),
            const SizedBox(height: 16),
            if (!isEndField) const Divider(thickness: .35),
          ],
        ),
      ),
    );
  }
}

class FontSizeItem {
  const FontSizeItem(this.title, this.scale);
  final String title;
  final double scale;
}
