// Flutter imports:

// Flutter imports:
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../../core/params/alert_params.dart';
import '../../../../core/utils/alert.dart';
import '../../../account/widgets/account_field.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import 'setting_screen.dart';

@RoutePage()
class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});
  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final ValueNotifier<UpdatePassWordParam> passwordVN = ValueNotifier(
    const UpdatePassWordParam(),
  );
  final ValueNotifier<bool> isShowCurrentPassword = ValueNotifier(true);
  final ValueNotifier<bool> isShowNewPassword = ValueNotifier(false);
  final ValueNotifier<bool> isShowConfirmPassword = ValueNotifier(false);

  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      title: Text(
        context.l10n.changePassword,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 20,
        ),
      ),
      actions: [
        ValueListenableBuilder(
          valueListenable: passwordVN,
          builder: (final context, final vPass, final child) {
            return ValueListenableBuilder(
              valueListenable: _currentPasswordController,
              builder: (final _, final vCurrent, final child) {
                return ValueListenableBuilder(
                  valueListenable: _newPasswordController,
                  builder: (final _, final vNew, final child) {
                    return TextButton(
                      onPressed: vCurrent.text.isEmpty || (vNew.text.isEmpty)
                          ? null
                          : () {
                              if (_newPasswordController.text !=
                                  _confirmPasswordController.text) {
                                Alert.showAlert(
                                  AlertParams(
                                    context,
                                    context.l10n.wrongConfirmPass,
                                    acceptButton: context.l10n.accept,
                                    onPressOK: () {},
                                  ),
                                );
                              } else {
                                Navigator.of(context).pop(vPass);
                              }
                            },
                      child: Text(context.l10n.save),
                    );
                  },
                );
              },
            );
          },
        ),
      ],
      body: Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(12),
                child: Column(
                  spacing: 12,
                  children: [
                    ValueListenableBuilder(
                      valueListenable: _currentPasswordController,
                      builder: (final context, final vCurrent, final child) {
                        return ValueListenableBuilder(
                          valueListenable: isShowCurrentPassword,
                          builder: (final context, final vIsShow, final child) {
                            return AccountField(
                              maxLines: 1,
                              isShowPass: vIsShow,
                              isOnlyReady: false,
                              controller: _currentPasswordController,
                              label: context.l10n.currentPassword,
                              hintText: '',
                              onChanged: (final value) {
                                passwordVN.value = passwordVN.value.copyWith(
                                  currentPassword: value,
                                );
                              },
                              iconRight: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (vCurrent.text.isNotEmpty)
                                    Row(
                                      children: [
                                        IconButton(
                                          onPressed: () {
                                            _currentPasswordController.clear();
                                          },
                                          icon: const Icon(Icons.cancel),
                                        ),
                                        ColoredBox(
                                          color: Colors.grey.withValues(
                                            alpha: .3,
                                          ),
                                          child: const SizedBox(
                                            width: 1,
                                            height: 24,
                                          ),
                                        ),
                                      ],
                                    ),

                                  IconButton(
                                    onPressed: () {
                                      isShowCurrentPassword.value =
                                          !isShowCurrentPassword.value;
                                    },
                                    icon: vIsShow
                                        ? EZResources.image(
                                            ImageParams(
                                              name: AppIcons.icEyeOff,
                                              size: const ImageSize.square(24),
                                            ),
                                          )
                                        : EZResources.image(
                                            ImageParams(
                                              name: AppIcons.icEyeOn,
                                              size: const ImageSize.square(24),
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    ValueListenableBuilder(
                      valueListenable: _newPasswordController,
                      builder: (final context, final vNewPass, final child) {
                        return ValueListenableBuilder(
                          valueListenable: isShowNewPassword,
                          builder: (final context, final vIsShow, final child) {
                            return AccountField(
                              isShowPass: vIsShow,
                              maxLines: 1,
                              isOnlyReady: false,
                              controller: _newPasswordController,
                              label: context.l10n.newPassword,
                              hintText: '',
                              onChanged: (final value) {
                                passwordVN.value = passwordVN.value.copyWith(
                                  newPassword: value,
                                );
                              },
                              iconRight: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (vNewPass.text.isNotEmpty)
                                    Row(
                                      children: [
                                        IconButton(
                                          onPressed: () {
                                            _newPasswordController.clear();
                                          },
                                          icon: const Icon(Icons.cancel),
                                        ),
                                        ColoredBox(
                                          color: Colors.grey.withValues(
                                            alpha: .3,
                                          ),
                                          child: const SizedBox(
                                            width: 1,
                                            height: 24,
                                          ),
                                        ),
                                      ],
                                    ),

                                  IconButton(
                                    onPressed: () {
                                      isShowNewPassword.value =
                                          !isShowNewPassword.value;
                                    },
                                    icon: vIsShow
                                        ? EZResources.image(
                                            ImageParams(
                                              name: AppIcons.icEyeOff,
                                              size: const ImageSize.square(24),
                                            ),
                                          )
                                        : EZResources.image(
                                            ImageParams(
                                              name: AppIcons.icEyeOn,
                                              size: const ImageSize.square(24),
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    ValueListenableBuilder(
                      valueListenable: _confirmPasswordController,
                      builder: (final context, final vConfirm, final child) {
                        return ValueListenableBuilder(
                          valueListenable: isShowNewPassword,
                          builder: (final context, final vIsShow, final child) {
                            return AccountField(
                              maxLines: 1,
                              isShowPass: vIsShow,
                              isOnlyReady: false,
                              controller: _confirmPasswordController,
                              label: context.l10n.confirmPassword,
                              hintText: '',
                              iconRight: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (vConfirm.text.isNotEmpty)
                                    Row(
                                      children: [
                                        IconButton(
                                          onPressed: () {
                                            _confirmPasswordController.clear();
                                          },
                                          icon: const Icon(Icons.cancel),
                                        ),
                                        ColoredBox(
                                          color: Colors.grey.withValues(
                                            alpha: .0,
                                          ),
                                          child: const SizedBox(
                                            width: 1,
                                            height: 24,
                                          ),
                                        ),
                                      ],
                                    ),

                                  IconButton(
                                    onPressed: () {},
                                    icon: const SizedBox.square(dimension: 24),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
