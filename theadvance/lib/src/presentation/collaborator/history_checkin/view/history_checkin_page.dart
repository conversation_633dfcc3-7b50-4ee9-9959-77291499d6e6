// Dart imports:
// ignore_for_file: use_build_context_synchronously

// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:audioplayers/audioplayers.dart';
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:home_widget/home_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

// Project imports:
import '../../../../core/nd_constants/strings.dart';
import '../../../../core/nd_intl/nd_intl.dart';
import '../../../../core/params/request_params.dart';
import '../../../../core/routes/app_router.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../domain/entities/entities.dart';
import '../../../../injector/injector.dart';
import '../../../_blocs/authentication/authentication_bloc.dart';
import '../../../action_attendance/widgets/action_attendance_body.dart';
import '../../../widgets/widgets.dart';
import '../../collaborator.dart';
import '../../tabbar/tabbar.dart';

enum RecordType { min, max }

@RoutePage()
class HistoryCheckinPage extends StatelessWidget {
  const HistoryCheckinPage({super.key});
  static AudioPlayer player = AudioPlayer();
  @override
  Widget build(final BuildContext context) {
    final now = DateTime.now();
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (final context) => getIt<AuthenticationBloc>()),
        BlocProvider(
          create: (final context) => getIt<HistoryCheckinBloc>()
            ..add(
              HistoryCheckinFetched(
                HistoryCheckinFetchRequestParams(
                  monthYear: now.formatByDateFormat(IntlHelper.dateFormatter7),
                ),
              ),
            )
            ..add(
              HistoryCheckinFetched(
                HistoryCheckinFetchRequestParams(
                  monthYear: DateTime(
                    now.month == 1 ? now.year - 1 : now.year,
                    now.month != 1 ? now.month - 1 : 12,
                  ).formatByDateFormat(IntlHelper.dateFormatter7),
                ),
                isNow: false,
              ),
            ),
        ),
      ],
      child: const HistoryCheckinBody(),
    );
  }
}

class HistoryCheckinBody extends StatefulWidget {
  const HistoryCheckinBody({final Key? key}) : super(key: key);

  @override
  HistoryCheckinBodyState createState() => HistoryCheckinBodyState();
}

class HistoryCheckinBodyState extends State<HistoryCheckinBody>
    with SingleTickerProviderStateMixin {
  double screenHeight = 0;
  double screenWidth = 0;

  final _months = <HistoryCheckinFetch?>[null, null];

  DateTime? _selectedDay;
  String? openTime;
  late AnimationController detailBoxController;
  late bool detailBoxVisible;
  late ScrollController _calendarScrollController;
  late int _currentCalendarPage;
  int _currentPage = 1;
  final now = DateTime.now();
  bool? isTakePhoto;
  final pageController = PageController(initialPage: 1);
  RefreshController refreshController = RefreshController();
  @override
  void initState() {
    super.initState();
    unawaited(HistoryCheckinPage.player.setReleaseMode(ReleaseMode.stop));
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      requestAddWidget();
      await HistoryCheckinPage.player.setSource(AssetSource(Strings.audioPath));
    });
    pageController.addListener(() async {
      if (mounted) {
        final newPage = pageController.page?.round();
        if (_currentPage != newPage) {
          setState(() => _currentPage = newPage ?? 0);
          _onCalendarChange(calendarPageIndex: _currentPage);
        }
      }
    });
    detailBoxVisible = false;
    detailBoxController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _calendarScrollController = ScrollController();
    _currentCalendarPage = 1;
    unawaited(onCalendarDateSelected(now, <dynamic>[], <dynamic>[]));
  }

  @override
  void dispose() {
    detailBoxController.dispose();
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    screenHeight = Utils.screenHeight(context);
    screenWidth = Utils.screenWidth(context);

    return Scaffold(
      backgroundColor: const Color(0xffF6F7FB),
      appBar: AppBar(
        backgroundColor: const Color(0xffF6F7FB),
        title: Text(
          context.l10n.timeAttendance,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        centerTitle: true,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(2),
          child: Divider(
            height: 2,
            color: Theme.of(context).hintColor.withValues(alpha: .2),
          ),
        ),
      ),
      body: BlocConsumer<AuthenticationBloc, AuthenticationState>(
        bloc: getIt<AppRouter>().navigatorKey.currentContext
            ?.read<AuthenticationBloc>(),
        listener: (final context, final state) {
          if (state is AuthenticationCheckinSuccess) {
            context.read<HistoryCheckinBloc>().add(
              HistoryCheckinFetched(
                HistoryCheckinFetchRequestParams(
                  monthYear: now.formatByDateFormat(IntlHelper.dateFormatter7),
                ),
              ),
            );
          }
        },
        builder: (final context, final state) {
          return BlocConsumer<HistoryCheckinBloc, HistoryCheckinState>(
            listener: (final context, final state) async {
              if (state is HistoryCheckinLoadFailure) {
                Alert.popup(
                  context,
                  ResultPopupScreen(
                    code: state.code,
                    title: Utils.defaultOnEmpty(
                      state.message,
                      fallback: _displayErrorMessage(state.errorType),
                    ),
                    isSuccess: false,
                  ),
                );
              }
              if (state is HistoryCheckinFetchSuccess) {
                if (state.isNow) {
                  ActionAttendanceBody.totalLeaveDays =
                      state.data?.leaveDays ?? Strings.zero;
                }
                _mapData(state.data);
              }

              if (state is HistoryCheckinUpdateSuccess) {
                if (state.updatedMonthYear != null) {
                  context.read<HistoryCheckinBloc>().add(
                    HistoryCheckinFetched(
                      HistoryCheckinFetchRequestParams(
                        monthYear: state.updatedMonthYear,
                      ),
                    ),
                  );
                }

                Navigator.pop(context);
                Alert.popup(
                  context,
                  ResultPopupScreen(
                    code: ErrorCodes.success,
                    title: context.l10n.updateSuccess,
                  ),
                );
              }
            },
            builder: (final context, final state) {
              return _buildBody(context);
            },
          );
        },
      ),
    );
  }

  Widget _buildBody(final BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SmartRefresher(
            controller: refreshController,
            onRefresh: () async {
              refreshController.refreshCompleted();
              context.read<HistoryCheckinBloc>().add(
                HistoryCheckinFetched(
                  HistoryCheckinFetchRequestParams(
                    monthYear: now.formatByDateFormat(
                      IntlHelper.dateFormatter7,
                    ),
                  ),
                ),
              );
              context.read<HistoryCheckinBloc>().add(
                HistoryCheckinFetched(
                  HistoryCheckinFetchRequestParams(
                    monthYear: DateTime(
                      now.month == 1 ? now.year - 1 : now.year,
                      now.month != 1 ? now.month - 1 : 12,
                      now.day,
                    ).formatByDateFormat(IntlHelper.dateFormatter7),
                  ),
                ),
              );
            },
            child: SizedBox(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () async {
                            if (mounted) {
                              if (pageController.page?.round() == 1) {
                                pageController.animateToPage(
                                  0,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.fastOutSlowIn,
                                );
                                _onCalendarChange(calendarPageIndex: 0);
                              }
                            }
                          },
                          icon: EZResources.image(
                            ImageParams(name: AppIcons.icArrowCircleLeft),
                          ),
                        ),
                        const Spacer(),
                        _buildMonthText(context),
                        const Spacer(),
                        IconButton(
                          onPressed: () async {
                            if (mounted) {
                              if (pageController.page?.round() == 0) {
                                pageController.animateToPage(
                                  1,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.fastOutSlowIn,
                                );
                                _onCalendarChange(calendarPageIndex: 1);
                              }
                            }
                          },
                          icon: EZResources.image(
                            ImageParams(name: AppIcons.icArrowCircleRight),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: GeneralCheckinSummaryBox(
                          monthData: _currentMonthData,
                          leaveDays: _currentMonthData?.leaveDays,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: ColoredBox(
                          color: Colors.white,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: CheckinCalendar(
                              pageController: pageController,
                              months: _months,
                              selectedDay: _selectedDay,
                              onDaySelected: onCalendarDateSelected,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailDayInfoBox(context),
                  ],
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: BaseAppButton(
            onPressed: CheckInButton.onCheckin,
            title: context.l10n.timeAttendanceNow,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailDayInfoBox(final BuildContext context) {
    return DetailDayInfoBox(
      controller: detailBoxController,
      selectedDay: _selectedDay,
      checkinDetail: _detailWhereDateIs(_selectedDay),
      hasExtraInfo: _hasExtraInfo(),
    );
  }

  Widget _buildMonthText(final BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        _displayCurrentSelectedMonth(),
        style: Theme.of(context).textTheme.titleMedium,
      ),
    );
  }

  HistoryCheckinDetail? _detailWhereDateIs(final DateTime? date) {
    if (date != null) {
      final data = _currentMonthData?.items.firstWhereOrNull(
        (final e) => e?.date == IntlHelper.dateFormatter.format(date),
      );
      if (date.isTheSameDay(DateTime.now())) {
        Future.wait<bool?>([
          HomeWidget.saveWidgetData(
            'workDate',
            DateFormat('EEEE, d MMMM', 'vi_VN').format(date),
          ),
          HomeWidget.saveWidgetData('workHours', data?.workingTime),
          HomeWidget.saveWidgetData('checkIn', data?.timeIn),
          HomeWidget.saveWidgetData('checkOut', data?.timeOut),
          HomeWidget.saveWidgetData(
            'total',
            data?.finalCheck?.toStringAsFixed(1),
          ),
          HomeWidget.saveWidgetData(
            'progress',
            (calculateProgress(data?.timeIn, data?.timeOut) * 100).toInt(),
          ),
        ]).then(
          (final _) => HomeWidget.updateWidget(
            name: 'HomeWidget',
            iOSName: 'HomeWidget',
            androidName: 'HomeWidget',
          ),
        );
      }
      return data;
    }

    return null;
  }

  double calculateProgress(
    final String? startTime,
    final String? endTime, {
    final int targetHours = 9,
  }) {
    if (startTime == null ||
        endTime == null ||
        (startTime.isEmpty) ||
        (endTime.isEmpty)) {
      return 0.0;
    }
    // Định dạng thời gian HH:mm
    final format = DateFormat('HH:mm');

    try {
      // 1. Chuyển đổi chuỗi thời gian thành DateTime
      final DateTime start = format.parse(startTime);
      final DateTime end = format.parse(endTime);

      // 2. Tính khoảng thời gian đã trôi qua
      final Duration elapsed = end.difference(start);

      // 3. Chuyển đổi thời gian mục tiêu thành Duration
      final Duration targetDuration = Duration(hours: targetHours);

      // 4. Tính progress
      // Lấy tổng số giây đã trôi qua và chia cho tổng số giây mục tiêu
      final double progress = elapsed.inSeconds / targetDuration.inSeconds;

      // 5. Đảm bảo progress nằm trong khoảng [0.0, 1.0]
      // Nếu thời gian đã trôi qua âm (endTime < startTime) thì progress = 0.0
      // Nếu vượt quá 9 tiếng, progress = 1.0
      return progress.clamp(0.0, 1.0);
    } on FormatException {
      // Xử lý lỗi nếu chuỗi thời gian không đúng định dạng
      return 0.0;
    } catch (e) {
      // Xử lý các lỗi khác
      return 0.0;
    }
  }

  String _displayCurrentSelectedMonth() {
    return '${context.l10n.month.capitalize()} '
        '${_currentMonthData?.month ?? now.month}/'
        '${_currentMonthData?.year ?? now.year}';
  }

  String _displayErrorMessage(final HistoryCheckinError? errorType) {
    if (errorType == null) {
      return context.l10n.unknown;
    }
    switch (errorType) {
      case HistoryCheckinError.fetchDataFailed:
        {
          return context.l10n.fetchHistoryCheckinFailed;
        }
      case HistoryCheckinError.fetchWorkTypeFailed:
        {
          return context.l10n.fetchWorkTypeFailed;
        }
      case HistoryCheckinError.requestUpdateFailed:
        {
          return context.l10n.requestUpdateCheckinFailed;
        }
    }
  }

  // void _updateControllers(CheckinUpdateInfo? info) {
  //   workTypeController?.text = Utils.defaultOnEmpty(info?.workType);
  //   fromDateController?.text = Utils.defaultOnEmpty(info?.fromDate);
  //   toDateController?.text = Utils.defaultOnEmpty(info?.toDate);
  //   noteController?.text = Utils.defaultOnEmpty(info?.note);
  // }

  void _mapData(final HistoryCheckinFetch? newData) {
    final newMonthData = int.tryParse(newData?.month ?? '') ?? -1;
    if (newMonthData != -1) {
      final monthIndex = _months.indexWhere(
        (final e) => (int.tryParse(e?.month ?? '') ?? -1) == newMonthData,
      );
      if (monthIndex != -1) {
        _months[monthIndex] = newData;
      } else {
        _months[isCurrentMonth(newMonthData) ? 1 : 0] = newData;
      }
    }
  }

  Future<void> onCalendarDateSelected(
    final DateTime date,
    final List<dynamic> _,
    final List<dynamic> __,
  ) async {
    await _updateDetailBox();
    if (_selectedDay != date) {
      _selectedDay = date;
      await _updateDetailBox(enable: true);
    } else {
      _selectedDay = null;
      await _updateDetailBox();
    }
  }

  Future<void> _updateCalendarUI({required final bool showBox}) async {
    if (showBox) {
      await detailBoxController.forward();
    } else {
      await detailBoxController.reverse();
    }
    if (showBox) {
      await _scrollCalendar();
    }
  }

  Future<void> _scrollCalendar() async {
    if (_calendarScrollController.hasClients) {
      await _calendarScrollController.animateTo(
        _calendarScrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 150),
        curve: Curves.ease,
      );
    }
  }

  Future<void> _onCalendarChange({
    // required final PageController calendarPageController,
    required final int calendarPageIndex,
  }) async {
    if (_selectedDay != null) {
      await onCalendarDateSelected(_selectedDay!, <dynamic>[], <dynamic>[]);
    }
    _currentCalendarPage = calendarPageIndex;
    setState(() {});
  }

  Future<void> _updateDetailBox({final bool enable = false}) async {
    setState(() {
      detailBoxVisible = enable;
    });
    await _updateCalendarUI(showBox: enable);
  }

  bool _hasExtraInfo() {
    final checkinDetail = _detailWhereDateIs(_selectedDay);

    return checkinDetail?.jsonConvertTimeSheets.isNotEmpty ?? false;
  }

  double get detailBoxHeight => _hasExtraInfo()
      ? Utils.screenHeight(context) * 0.212
      : Utils.screenHeight(context) * 0.106;

  bool isCurrentMonth(final int month) => month == now.month;

  HistoryCheckinFetch? get _currentMonthData => _months[_currentCalendarPage];

  Future<void> requestAddWidget() async {
    if (EZCache.shared.box.get(Keys.isRequestAddWidget, defaultValue: true) ==
        true) {
      EZCache.shared.box.put(Keys.isRequestAddWidget, false);
      if (Utils.isAndroid) {
        Alert.showAlertConfirm(
          AlertConfirmParams(
            context,
            message: context.l10n.requestHomeWidget,
            confirmText: context.l10n.accept,
            cancelButton: context.l10n.skip,
            image: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 300,
                  maxHeight: 280,
                ),
                child: EZResources.image(
                  ImageParams(name: AppImages.androidHomeWidgetSample),
                ),
              ),
            ),
            onPressed: () {
              HomeWidget.requestPinWidget(
                name: 'HomeWidget',
                androidName: 'HomeWidget',
              ).then((final _) {
                Navigator.of(context).pop();
              });
            },
          ),
        );
      }
      if (Utils.isIOS && (await isIosVersion18OrHigher())) {
        Alert.showSuccessDialog(
          SuccessDialogParams(
            context: context,
            message: context.l10n.homeWidgetTip,
            isSuccess: false,
            image: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 300,
                  maxHeight: 280,
                ),
                child: EZResources.image(
                  ImageParams(name: AppImages.iosHomeWidgetSample),
                ),
              ),
            ),
            button: context.l10n.close,
          ),
        );
      }
    }
  }

  Future<bool> isIosVersion18OrHigher() async {
    if (Utils.isIOS) {
      try {
        final versionString = (await Utils.deviceInfo()).osVersion ?? '';

        // Tách chuỗi phiên bản thành các phần số
        final parts = versionString.split('.').map(int.parse).toList();

        // So sánh phiên bản
        if (parts.isNotEmpty) {
          if (parts[0] > 18) {
            return true;
          } else if (parts[0] == 18) {
            // Nếu phần major (18) bằng nhau, kiểm tra phần minor (0, 1, 2...)
            // Nếu không có phần minor, mặc định là 18.0
            if (parts.length > 1) {
              return parts[1] >= 0;
            } else {
              return true;
            }
          }
        }
      } catch (_) {}
    }
    return false;
  }
}
