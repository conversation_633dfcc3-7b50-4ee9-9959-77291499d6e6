// Dart imports:
// ignore_for_file: use_build_context_synchronously

// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../core/params/request_params.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../services/nd_local_notification/local_notification.dart';
import '../../../widgets/widgets.dart';
import '../bloc/checkin_reminder_bloc.dart';
import '../widgets/checkin_reminder_item.dart';
import 'checkin_number_picker.dart';

class CheckinReminderView extends StatefulWidget {
  const CheckinReminderView({super.key});

  @override
  State<CheckinReminderView> createState() => _CheckinReminderViewState();
}

class _CheckinReminderViewState extends State<CheckinReminderView> {
  List<bool> isSwitched = EZCache.shared.checkinRemaider;
  final ValueNotifier<TimeOfDay?> checkinTime = ValueNotifier(
    const TimeOfDay(hour: 5, minute: 30),
  );
  final ValueNotifier<TimeOfDay?> checkoutDate = ValueNotifier(
    const TimeOfDay(hour: 17, minute: 30),
  );

  Future<void> onChange({
    required final bool value,
    required final int index,
  }) async {
    setState(() {
      isSwitched[0 + index] = value;
    });
    await EZCache.shared.saveCheckinRemaider(isSwitched);
    if (value) {
      await showNotificationDayofWeek(
        dayOfWeek: DateTime.monday + index,
        id: 1 + index,
      );
    } else {
      await flutterLocalNotificationsPlugin.cancel(1 + index);
      await flutterLocalNotificationsPlugin.cancel(1 + 10 + index);
    }
  }

  @override
  Widget build(final BuildContext context) {
    final List<String> dayOfWeek = [
      context.l10n.monday,
      context.l10n.tuesday,
      context.l10n.wednesday,
      context.l10n.thursday,
      context.l10n.friday,
      context.l10n.saturday,
      context.l10n.sunDay,
    ];

    return BlocConsumer<CheckinReminderBloc, CheckinReminderState>(
      listener: (final context, final state) {
        if (state is CheckinReminderLoadSuccess) {
          checkinTime.value = TimeOfDay(
            hour: state.checkinHour.hour,
            minute: state.checkinHour.minute,
          );
          checkoutDate.value = TimeOfDay(
            hour: state.checkoutHour.hour,
            minute: state.checkoutHour.minute,
          );
        }
        if (state is CheckinReminderSaveSuccess) {
          context.read<CheckinReminderBloc>().add(CheckinReminderStarted());
          unawaited(
            Alert.showAlert(
              AlertParams(
                context,
                context.l10n.updateSuccess,
                onPressOK: () => Navigator.of(context).pop(),
              ),
            ),
          );
        }
      },
      builder: (final context, final state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            spacing: 24,
            children: [
              Column(
                spacing: 8,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    child: Text(
                      context.l10n.loopByDay.toUpperCase(),
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: buildBody(context, dayOfWeek),
                  ),
                ],
              ),
              Column(
                spacing: 8,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    child: Text(
                      context.l10n.settingCheckinTime.toUpperCase(),
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () {
                            _onShowDatePicker(
                              context,
                              context.l10n.timeCheckIn,
                              checkinTime,
                            ).then((final time) {
                              if (time != null) {
                                if (context.mounted) {
                                  context.read<CheckinReminderBloc>().add(
                                    CheckinReminderTimeSaved(
                                      checkinHour: time,
                                      checkoutHour: checkoutDate.value,
                                    ),
                                  );
                                }
                              }
                            });
                          },
                          child: ValueListenableBuilder(
                            valueListenable: checkinTime,
                            builder: (final context, final value, final child) {
                              return _buildInfoField(
                                context.l10n.timeCheckIn,
                                '${value?.format(context)}',
                                true,
                              );
                            },
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            _onShowDatePicker(
                              context,
                              context.l10n.timeCheckOut,
                              checkoutDate,
                            ).then((final time) {
                              if (time != null) {
                                if (context.mounted) {
                                  context.read<CheckinReminderBloc>().add(
                                    CheckinReminderTimeSaved(
                                      checkinHour: checkinTime.value,
                                      checkoutHour: time,
                                    ),
                                  );
                                }
                              }
                            });
                          },
                          child: ValueListenableBuilder(
                            valueListenable: checkoutDate,
                            builder: (final context, final value, final child) {
                              return _buildInfoField(
                                context.l10n.timeCheckOut,
                                '${value?.format(context)}',
                                true,
                                true,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Future<TimeOfDay?> _onShowDatePicker(
    final BuildContext context,
    final String title,
    final ValueNotifier<TimeOfDay?> valueDateOfTime,
  ) {
    return showDialog<TimeOfDay?>(
      context: context,
      builder: (final builder) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: IconButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            icon: EZResources.image(
                              ImageParams(name: AppIcons.icBack),
                            ),
                          ),
                        ),
                        Align(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              title,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(thickness: .35),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(
                  //     horizontal: 6,
                  //     vertical: 12,
                  //   ),
                  //   child: Row(
                  //     children: [

                  //     ],
                  //   ),
                  // ),
                  //   const SizedBox(height: 8),
                  ValueListenableBuilder(
                    valueListenable: valueDateOfTime,
                    builder: (final context, final vDates, final child) {
                      int hour = vDates?.hour ?? 8;
                      int minute = vDates?.minute ?? 0;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: SizedBox(
                              width: double.infinity,
                              height: 278,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: CheckinReminderNumberPicker(
                                      max: 23,
                                      initialValue: hour,
                                      onChanged: (final value) {
                                        hour = value;
                                      },
                                    ),
                                  ),
                                  Expanded(
                                    child: CheckinReminderNumberPicker(
                                      max: 59,
                                      initialValue: minute,
                                      onChanged: (final value) {
                                        minute = value;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: BaseAppButton(
                              titleColor: Colors.white,
                              onPressed: () {
                                final time = TimeOfDay(
                                  hour: hour,
                                  minute: minute,
                                );
                                Navigator.of(context).pop(time);
                              },
                              title: context.l10n.done,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> openShowTimePicker(
    final ValueNotifier<TimeOfDay?> valueNotifier,
  ) async {
    valueNotifier.value =
        await showTimePicker(
          initialEntryMode: TimePickerEntryMode.input,
          initialTime: valueNotifier.value ?? TimeOfDay.now(),
          context: context,
        ) ??
        valueNotifier.value;
  }

  Widget _buildInfoField(
    final String title,
    final String value, [
    final bool isForward = false,
    final bool isEndField = false,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Row(
              spacing: 16,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.labelLarge?.copyWith(fontSize: 15),
                ),
                if (value.isNotEmpty)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        value,
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontSize: 15,
                          color: Colors.black45,
                        ),
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        context.l10n.notUpdated,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.black54,
                        ),
                      ),
                    ),
                  ),
                if (isForward)
                  EZResources.image(
                    ImageParams(
                      name: AppIcons.icForward,
                      size: const ImageSize.square(24),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (!isEndField) const Divider(thickness: .35),
        ],
      ),
    );
  }

  Widget buildBody(final BuildContext context, final List<String> dayOfWeek) {
    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: isSwitched.length,
        itemBuilder: (final context, final index) {
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: checkinReminderItem(
                  dayOfWeek[index],
                  index,
                  onChange,
                  context,
                  isSwitched: isSwitched[index],
                ),
              ),
              if (index != isSwitched.length - 1)
                const Padding(
                  padding: EdgeInsets.only(left: 12),
                  child: Divider(thickness: .35),
                ),
            ],
          );
        },
      ),
    );
  }
}
