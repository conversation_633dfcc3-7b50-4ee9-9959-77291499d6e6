// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../account/widgets/account_field.dart';
import '../../../product_confirm/widgets/base_layout.dart';

@RoutePage()
class ProfileDetailPage extends StatefulWidget {
  const ProfileDetailPage({
    super.key,
    required this.title,
    required this.value,
    this.isLines = false,
  });
  final String title;
  final String value;
  final bool isLines;

  @override
  State<ProfileDetailPage> createState() => _ProfileDetailPageState();
}

class _ProfileDetailPageState extends State<ProfileDetailPage> {
  late TextEditingController _controller;
  String oldValue = '';
  @override
  void initState() {
    _controller = TextEditingController(text: widget.value);
    oldValue = widget.value;
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      actions: [
        ValueListenableBuilder(
          valueListenable: _controller,
          builder: (final context, final vController, final child) {
            return TextButton(
              onPressed: vController.text.isEmpty
                  ? null
                  : () {
                      if (_controller.text == oldValue) {
                        Navigator.of(context).pop();
                      } else {
                        Navigator.of(context).pop(_controller.text);
                      }
                    },
              child: Text(context.l10n.save),
            );
          },
        ),
      ],
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          children: [
            SingleChildScrollView(
              child: AccountField(
                maxLines: widget.isLines ? 5 : null,
                maxLength: widget.isLines ? 300 : null,
                isOnlyReady: false,
                controller: _controller,
                label: '',
                hintText: widget.title,
                onSubmitted: (final value) {
                  if (value == oldValue) {
                    Navigator.of(context).pop();
                  } else {
                    Navigator.of(context).pop(value);
                  }
                },
                iconRight: ValueListenableBuilder(
                  valueListenable: _controller,
                  builder: (final builder, final vController, final child) {
                    return vController.text.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _controller.clear();
                            },
                            icon: const Icon(Icons.cancel),
                          )
                        : const SizedBox();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      title: Text(
        widget.title,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 20,
        ),
      ),
    );
  }
}
