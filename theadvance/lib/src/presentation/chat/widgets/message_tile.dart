// Dart imports:
import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_intl/ez_intl.dart' hide TextDirection;
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' show PreviewData;
import 'package:flutter_image_clipboard/flutter_image_clipboard.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_image_stack/flutter_image_stack.dart';
import 'package:flutter_link_previewer/flutter_link_previewer.dart' hide Size;
import 'package:flutter_quill_delta_from_html/parser/html_to_delta.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:http/http.dart' as http;
import 'package:linkfy_text/linkfy_text.dart';
import 'package:measure_size/measure_size.dart';
import 'package:mime_type/mime_type.dart';
import 'package:path_provider/path_provider.dart';

// Project imports:
import '../../../config/enums/chat_action.dart';
import '../../../config/enums/chat_reaction.dart';
import '../../../config/enums/file_extension_type.dart';
import '../../../config/enums/message_parse_mode.dart';
import '../../../config/enums/poll_status.dart';
import '../../../config/enums/user_chat_role.dart';
import '../../../config/enums/user_chat_rule.dart';
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/nd_progresshud/loading_dialog.dart';
import '../../../core/params/alert_params.dart';
import '../../../core/params/chat_react_request_params.dart';
import '../../../core/params/chat_reply_bot_message_request_params.dart';
import '../../../core/params/chat_update_poll_request_params.dart';
import '../../../core/params/chat_vote_poll_request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/deeplink_helper.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../core/utils/platform_channel_helper.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/entities.dart';
import '../../../injector/injector.dart';
import '../../../services/nd_downloader/nd_downloader.dart';
import '../../settings/fonts/fonts_bloc.dart';
import '../../widgets/widgets.dart';
import '../chat.dart';
import 'adaptive_message.dart';
import 'animated_sticker_widget.dart';

class MessageTile extends StatefulWidget {
  const MessageTile({
    super.key,
    required this.message,
    required this.customerId,
    required this.taskInfo,
    required this.isShowingAvatar,
    required this.isShowingName,
    required this.isGroup,
    required this.onRemoved,
    required this.onRemovedSuccess,
    required this.onSendEditedImage,
    required this.onReply,
    required this.onEdit,
    required this.onPin,
    required this.onUnpin,
    required this.onForward,
    required this.onTapRootMessage,
    required this.onTapForwardMessage,
    required this.onTapUserTag,
    required this.onSwipeLeft,
    required this.onReact,
    required this.onShowMenuPopup,
    required this.onCopiedSuccess,
    required this.previewDataList,
    required this.userRole,
    required this.isPin,
    required this.onRetractVote,
    required this.onStopVote,
    required this.imageUrlList,
    required this.membersInfo,
    required this.onRetry,
    required this.onShowUserSeen,
    required this.onMultiSelect,
    required this.onTapSticker,
    required this.userRules,
    this.isPopupMessage = false,
  });

  factory MessageTile.previewMessage({
    required final ChatItems? message,
    required final String? customerId,
    required final Map<String, PreviewData> previewDataList,
    final List<ImageProperty?> imageUrlList = const [],
  }) => MessageTile(
    message: message,
    isPopupMessage: true,
    customerId: customerId,
    taskInfo: null,
    isShowingAvatar: false,
    isShowingName: false,
    isGroup: false,
    onRemoved: () {},
    onRemovedSuccess: (final _) {},
    onSendEditedImage: (final _) {},
    onReply: (final _) {},
    onEdit: (final _) {},
    onPin: (final _) {},
    onUnpin: (final _) {},
    onForward: (final _) {},
    onTapRootMessage: () {},
    onTapForwardMessage: () {},
    onTapUserTag: (final _) {},
    onSwipeLeft: (final _) {},
    onReact: (final _, final __) {},
    onShowMenuPopup:
        ({required final bool isShowing, required final ChatItems? message}) {},
    onCopiedSuccess: (final _) {},
    previewDataList: previewDataList,
    userRole: '',
    isPin: false,
    onRetractVote: (final _) {},
    onStopVote: (final _) {},
    imageUrlList: imageUrlList,
    membersInfo: const [],
    onRetry: (final _) {},
    onShowUserSeen: (final _, final __) {},
    onMultiSelect: (final _) {},
    onTapSticker: (final _) {},
    userRules: const [],
  );

  final ChatItems? message;
  final String? customerId;
  final String? userRole;
  final TaskInfo? taskInfo;
  final bool isShowingAvatar;
  final bool isShowingName;
  final bool isGroup;
  final bool isPin;
  final void Function() onRemoved;
  final void Function(String?) onRemovedSuccess;
  final void Function(ChatItems?) onSwipeLeft;
  final void Function(ChatItems?) onReply;
  final void Function(ChatItems?) onRetry;
  final void Function(ChatItems?) onEdit;
  final void Function(Uint8List?) onSendEditedImage;
  final void Function(ChatItems?) onPin;
  final void Function(ChatItems?) onUnpin;
  final void Function(ChatItems?) onForward;
  final void Function(ChatItems?, String) onReact;
  final void Function(ChatItems?) onMultiSelect;
  final void Function() onTapRootMessage;
  final void Function() onTapForwardMessage;
  final void Function({required bool isShowing, required ChatItems? message})
  onShowMenuPopup;
  final void Function(String) onTapUserTag;
  final void Function(Uint8List?) onCopiedSuccess;
  final void Function(String?) onTapSticker;
  final void Function(ChatVotePollRequestParams) onRetractVote;
  final void Function(ChatUpdatePollRequestParams) onStopVote;
  final void Function(ChatItems?, RelativeRect) onShowUserSeen;
  final Map<String, PreviewData> previewDataList;
  final List<ImageProperty?> imageUrlList;
  final List<ChatListItemsMembersInfo?> membersInfo;
  final bool isPopupMessage;
  final List<String?> userRules;
  static String attribuites = '';
  @override
  State<MessageTile> createState() => _MessageTileState();
}

class _MessageTileState extends State<MessageTile> {
  late final GlobalObjectKey<SnappableState> snapKey;
  List<String?> votedOptionIds = [];
  ValueNotifier<ChatGetUserSeen?> userSeenData =
      ValueNotifier<ChatGetUserSeen?>(null);
  bool showUserSeenList = false;
  bool isSender = false;
  @override
  void initState() {
    super.initState();
    snapKey = GlobalObjectKey<SnappableState>(
      '${widget.message?.id ?? DateTime.now().millisecondsSinceEpoch}',
    );
    isSender = widget.customerId == widget.message?.createdByInfo?.username;
  }

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onLongPressStart:
          !widget.isPopupMessage &&
              (widget.message?.call?.callId?.isEmpty ?? true)
          ? (final details) async {
              if (!widget.isPopupMessage) {
                ChatBody.completer = Completer<ChatGetUserSeen?>();
                widget.onShowMenuPopup(
                  isShowing: true,
                  message: widget.message,
                );
                _onShowActionMenu();
                await ChatBody.completer.future.timeout(
                  const Duration(seconds: 3),
                );
                if (ChatBody.completer.isCompleted) {
                  userSeenData.value = await ChatBody.completer.future;
                }
              }
            }
          : null,
      onDoubleTap: () {
        if (!widget.isPopupMessage) {
          context.read<ChatBloc>().add(
            ChatReacted(
              ChatReactRequestParams(
                action: ChatAction.create.name,
                id: widget.message?.id,
                conversationId: widget.message?.conversationId,
                icon: ChatReaction.love.value,
              ),
            ),
          );
        }
      },
      child: !widget.isPopupMessage
          ? SnappableThanos(
              key: snapKey,
              onSnapped: () => widget.onRemovedSuccess(widget.message?.id),
              duration: const Duration(milliseconds: 1500),
              child: buildSingleMessage(context, widget.message),
            )
          : buildSingleMessage(context, widget.message),
    );
  }

  Future<void> _onShowActionMenu() {
    HapticFeedback.mediumImpact();
    return showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.transparent,
      pageBuilder: (final dialogContext, final __, final ___) {
        return Center(child: _buildPopupMenu(dialogContext));
      },
      transitionBuilder: (final _, final animation, final __, final child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutBack,
            ),
            child: child,
          ),
        );
      },
    ).whenComplete(() {
      widget.onShowMenuPopup(isShowing: false, message: widget.message);
    });
  }

  bool isExpanded = false;

  Widget _buildPopupMenu(final BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => Navigator.of(context, rootNavigator: true).pop(),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.sizeOf(context).width - 20,
            maxHeight: MediaQuery.sizeOf(context).height * 0.85,
          ),
          child: Stack(
            alignment: Alignment.topRight,
            children: [
              Stack(
                children: [
                  SingleChildScrollView(
                    reverse: true,
                    padding: const EdgeInsets.only(top: 40),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.sizeOf(context).width - 60,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8, left: 8),
                            child: SelectionArea(
                              child: MessageTile.previewMessage(
                                message: widget.message,
                                customerId: widget.customerId,
                                previewDataList: widget.previewDataList,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        if ((widget.message?.seen ?? false) &&
                            widget.isGroup &&
                            isSender) ...[
                          _buildUserSeenMenu(context),
                          const SizedBox(height: 4),
                        ],
                        if (!(widget.message?.sentFailed ?? false))
                          popupItemWrapper(
                            onTap: () {
                              widget.onReply(widget.message);
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    topRight: Radius.circular(8),
                                  ),
                                ),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.reply2,
                                iconPath: AppIcons.icActionReply,
                              ),
                            ),
                          ),
                        if (checkCopyMessage(widget.message)) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () async {
                              var copiedText = '';
                              if ((widget.message?.content?.isNotEmpty ??
                                      false) ||
                                  (widget.message?.attachment.isEmpty ??
                                      true)) {
                                copiedText =
                                    (widget.message?.content?.isNotEmpty ??
                                        false)
                                    ? widget.message?.content ?? ''
                                    : widget.message?.forward?.content ?? '';
                                if (Utils.isHtml(copiedText)) {
                                  copiedText = removeStyleAttributes(
                                    copiedText,
                                  );
                                  final delta = HtmlToDelta().convert(
                                    copiedText,
                                  );

                                  final obs = delta.toJson();
                                  final result = isAttributesNotEmpty(obs);
                                  if (result) {
                                    final buffer = StringBuffer();
                                    for (final op in delta.toList()) {
                                      if (op.isInsert && op.value is String) {
                                        buffer.write(op.value);
                                      }
                                    }

                                    copiedText = buffer.toString();
                                    MessageTile.attribuites = jsonEncode({
                                      copiedText
                                              .replaceAll(' ', '')
                                              .substring(
                                                0,
                                                copiedText
                                                            .replaceAll(' ', '')
                                                            .length >
                                                        100
                                                    ? 100
                                                    : null,
                                              )
                                              .replaceAll('\n', ''):
                                          delta.operations,
                                    });
                                  }
                                } else {
                                  copiedText = widget.message?.content ?? '';
                                }
                                Clipboard.setData(
                                  ClipboardData(text: copiedText),
                                );

                                widget.onCopiedSuccess(null);
                              }
                              if (copiedText.isEmpty) {
                                unawaited(
                                  _handleCopy(
                                    widget
                                            .message
                                            ?.attachment
                                            .firstOrNull
                                            ?.link ??
                                        widget
                                            .message
                                            ?.forward
                                            ?.attachment
                                            .firstOrNull
                                            ?.link,
                                  ),
                                );
                              }
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.copied,
                                iconPath: AppIcons.icActionCopy,
                              ),
                            ),
                          ),
                        ],
                        if (isSender &&
                            widget.message?.id == null &&
                            (widget.message?.sentFailed ?? false) &&
                            (widget.message?.attachment.isNotEmpty ??
                                false)) ...[
                          popupItemWrapper(
                            onTap: () {
                              widget.onRetry(widget.message);
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    topRight: Radius.circular(8),
                                  ),
                                ),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.tryAgain,
                                iconPath: AppIcons.icReload,
                              ),
                            ),
                          ),
                        ],
                        if ((widget.message?.sticker?.id?.isEmpty ?? true) &&
                            (widget.message?.forward?.id?.isEmpty ?? true) &&
                            !(widget.message?.sentFailed ?? false) &&
                            isSender &&
                            (widget.message?.alowEdited ?? false)) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              widget.onEdit(widget.message);
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.edit,
                                iconPath: AppIcons.icEdit,
                              ),
                            ),
                          ),
                        ],
                        if (!(widget.message?.sentFailed ?? false) &&
                            widget.message?.pollInfo?.status ==
                                PollStatus.ACTIVE.name &&
                            votedOptionIds.isNotEmpty) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              widget.onRetractVote(
                                ChatVotePollRequestParams(
                                  messageId: widget.message?.id,
                                  optionIds: votedOptionIds,
                                  isRetract: true,
                                ),
                              );
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.retractVote,
                                iconPath: AppIcons.icUnvote,
                              ),
                            ),
                          ),
                        ],
                        if (!(widget.message?.sentFailed ?? false) &&
                            widget.message?.pollInfo?.status ==
                                PollStatus.ACTIVE.name &&
                            isSender) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              widget.onStopVote(
                                ChatUpdatePollRequestParams(
                                  messageId:
                                      widget.message?.pollInfo?.messageId,
                                  status: PollStatus.INACTIVE.name,
                                ),
                              );
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.stopVote,
                                iconPath: AppIcons.icStopPoll,
                              ),
                            ),
                          ),
                        ],
                        if (checkForwardMessage(widget.message)) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              widget.onForward(widget.message);
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.forward,
                                iconPath: AppIcons.icActionForward,
                              ),
                            ),
                          ),
                        ],
                        if ((widget.message?.attachment.firstOrNull?.mimetype
                                    ?.contains(FileExtensionType.audio.name) ??
                                false) &&
                            (((widget.userRules.contains(
                                          UserChatRule.downloadRecord.name,
                                        ) &&
                                        !(widget.message?.sentFailed ??
                                            false)) ||
                                    (EZCache
                                            .shared
                                            .chatLoginData
                                            ?.user
                                            ?.permission
                                            ?.record
                                            ?.download ??
                                        false)) &&
                                (EZCache
                                        .shared
                                        .chatLoginData
                                        ?.user
                                        ?.permission
                                        ?.record
                                        ?.download ??
                                    false))) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              ServiceDownloader.instance.onTapDownload(
                                context,
                                widget.message?.attachment.firstOrNull?.link ??
                                    '',
                                TaskInfo(
                                  name: widget
                                      .message
                                      ?.attachment
                                      .firstOrNull
                                      ?.originalname,
                                ),
                                widget
                                    .message
                                    ?.attachment
                                    .firstOrNull
                                    ?.originalname,
                              );
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.download,
                                iconPath: AppIcons.icDownload,
                              ),
                            ),
                          ),
                        ],
                        if (widget.userRules.contains(
                              UserChatRule.pinMessage.name,
                            ) &&
                            !(widget.message?.sentFailed ?? false)) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              if (widget.isPin) {
                                widget.onUnpin(widget.message);
                              } else {
                                widget.onPin(widget.message);
                              }
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: widget.isPin
                                    ? context.l10n.unpinMessage
                                    : context.l10n.pinMessage,
                                iconPath: widget.isPin
                                    ? AppIcons.icUnpin
                                    : AppIcons.icActionPin,
                              ),
                            ),
                          ),
                        ],
                        if ((widget.isGroup || (!widget.isGroup && isSender)) &&
                            ((widget.message?.sentFailed ?? false) ||
                                (isSender ||
                                        !widget.isGroup ||
                                        widget.userRole ==
                                            UserChatRole.owner.name ||
                                        widget.userRules.contains(
                                          UserChatRule.removeMessage.name,
                                        )) &&
                                    (widget.message?.id?.isNotEmpty ??
                                        false))) ...[
                          _buildPopupDivider(context),
                          popupItemWrapper(
                            onTap: () {
                              if (!kIsWeb && snapKey.currentState != null) {
                                snapKey.currentState?.snap();
                              } else {
                                widget.onRemovedSuccess(widget.message?.id);
                              }
                              widget.onRemoved();
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.delete,
                                iconPath: AppIcons.icActionDelete,
                                isDeleteIcon: true,
                              ),
                            ),
                          ),
                        ],
                        ...[
                          popupItemWrapper(
                            child: Container(
                              height: 6,
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              color: const Color(0xffD8DFDA),
                            ),
                          ),
                          popupItemWrapper(
                            onTap: () {
                              widget.onMultiSelect(widget.message);
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 200),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: ShapeDecoration(
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    bottom: Radius.circular(8),
                                  ),
                                ),
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.8),
                              ),
                              child: _buildMessageAction(
                                context,
                                label: context.l10n.select,
                                iconPath: AppIcons.icSelect,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (widget.message?.widthSize != null &&
                      (widget.message?.widthSize ?? 0) < 200)
                    Positioned(
                      top: 40,
                      right: widget.message?.widthSize,
                      child: EZResources.image(
                        ImageParams(name: AppIcons.icThinkingBubble),
                      ),
                    )
                  else
                    Positioned(
                      top: 40,
                      left: 0,
                      child: EZResources.image(
                        ImageParams(name: AppIcons.icThinkingBubble),
                      ),
                    ),
                ],
              ),
              _buildSelectionEmojis(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserSeenMenu(final BuildContext context) {
    return popupItemWrapper(
      onTap: () {
        widget.onShowMenuPopup(isShowing: false, message: widget.message);
        final double left = MediaQuery.sizeOf(context).width / 4;
        final double top = MediaQuery.sizeOf(context).height / 3;
        widget.onShowUserSeen(
          widget.message,
          RelativeRect.fromLTRB(left, top, left, top),
        );
        Navigator.of(context, rootNavigator: true).pop();
      },
      child: Container(
        constraints: const BoxConstraints(minWidth: 200),
        padding: const EdgeInsets.all(12),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 4),
              child: EZResources.image(
                ImageParams(
                  name: (widget.message?.reactions.isNotEmpty ?? false)
                      ? AppIcons.icHeartOutline
                      : AppIcons.icDoubleCheck,
                  size: const ImageSize.square(20),
                  color: Theme.of(context).hintColor,
                ),
              ),
            ),
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: userSeenData,
                builder: (final context, final value, final child) {
                  final seenCount = value?.total ?? 0;
                  final displayText =
                      (widget.message?.reactions.isNotEmpty ?? false)
                      ? '${widget.message?.reactions.length}/$seenCount ${context.l10n.react.toLowerCase()}'
                      : '${seenCount > 0 ? seenCount : ''}'
                            '${Strings.space}'
                            '${context.l10n.seen.toLowerCase()}';
                  return Row(
                    children: [
                      Text(
                        displayText,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                      const Spacer(),
                      if (seenCount > 0)
                        Padding(
                          padding: const EdgeInsets.only(left: 6),
                          child: FlutterImageStack.widgets(
                            itemBorderWidth: 1,
                            showTotalCount: false,
                            itemBorderColor: Theme.of(
                              context,
                            ).colorScheme.surface,
                            itemRadius: 22,
                            itemCount: 2,
                            backgroundColor: Colors.transparent,
                            totalCount: value?.docs.length ?? 0,
                            children:
                                value?.docs
                                    .mapIndexed(
                                      (final index, final react) =>
                                          EzCachedNetworkImage(
                                            imageUrl: react?.avatar,
                                            width: 20,
                                            height: 20,
                                            errorWidget: RandomAvatarWidget(
                                              name: react?.name,
                                              width: 20,
                                              height: 20,
                                            ),
                                          ),
                                    )
                                    .toList() ??
                                [],
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),
            const SizedBox(width: 6),
          ],
        ),
      ),
    );
  }

  Container _buildSelectionEmojis(final BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200, minHeight: 40),
      padding: const EdgeInsets.symmetric(horizontal: 6),
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.94),
      ),
      child: StatefulBuilder(
        builder: (final context, final setState) {
          if (isExpanded) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: GridView.builder(
                padding: const EdgeInsets.symmetric(vertical: 4),
                itemCount: fullReactEmojis.length,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemBuilder: (final context, final index) {
                  return GestureDetector(
                    onTap: () {
                      widget.onReact(widget.message, fullReactEmojis[index]);
                    },
                    child: Text(
                      fullReactEmojis[index],
                      style: const TextStyle(fontSize: 32),
                    ),
                  );
                },
              ),
            );
          }
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: EdgeInsets.zero,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: ChatReaction.values.map((final icon) {
                      final currentReaction = widget.message?.reactions
                          .firstWhereOrNull(
                            (final element) =>
                                element?.username == widget.customerId,
                          );
                      return SizedBox(
                        width: 40,
                        height: 40,
                        child: IconButton(
                          onPressed: () {
                            widget.onReact(widget.message, icon.value);
                          },
                          padding: EdgeInsets.zero,
                          icon: Container(
                            decoration: currentReaction?.icon == icon.value
                                ? ShapeDecoration(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    color: Theme.of(
                                      context,
                                    ).primaryColor.withValues(alpha: 0.25),
                                  )
                                : null,
                            child: Text(
                              icon.value,
                              style: const TextStyle(fontSize: 28),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => setState(() => isExpanded = !isExpanded),
                child: Container(
                  width: 28,
                  height: 28,
                  margin: const EdgeInsets.only(left: 4),
                  decoration: const ShapeDecoration(
                    shape: CircleBorder(),
                    color: Color(0xffA1BF3D),
                  ),
                  child: Icon(
                    Icons.keyboard_arrow_down_sharp,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPopupDivider(final BuildContext context) {
    return popupItemWrapper(
      child: Container(
        height: 1,
        constraints: const BoxConstraints(minWidth: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: const ShapeDecoration(
          shape: RoundedRectangleBorder(),
          color: Color(0xffC7C7C7),
        ),
      ),
    );
  }

  bool isAttributesNotEmpty(final List<Map<String, dynamic>> dataList) {
    for (final item in dataList) {
      if (item.containsKey('attributes') &&
          item['attributes'] is Map &&
          // ignore: avoid_dynamic_calls
          item['attributes'].isNotEmpty) {
        return true;
      }
    }
    return false;
  }

  String removeStyleAttributes(final String html) {
    final RegExp stylePattern = RegExp(r'\s+style="[^"]*"');
    final RegExp classPattern = RegExp(r'\s+class="[^"]*"');
    return html.replaceAll(stylePattern, '').replaceAll(classPattern, '');
  }

  bool checkCopyMessage(final ChatItems? message) {
    // if is not bot, copy follow group permission
    if (widget.message?.createdByInfo?.type != 'bot') {
      return widget.userRules.contains(UserChatRule.copyMessage.name) &&
          (widget.message?.sticker?.id?.isEmpty ?? true) &&
          !(widget.message?.sentFailed ?? false);
    } else {
      // if is bot, copy follow permission on admin tool
      return EZCache.shared.chatLoginData?.user?.permission?.botMessage?.copy ??
          false;
    }
  }

  bool checkForwardMessage(final ChatItems? message) {
    // if message is poll => can not forward
    if (widget.message?.pollInfo?.id != null) {
      return false;
    }
    // if is not bot, forward follow group permission
    if (widget.message?.createdByInfo?.type != 'bot') {
      return widget.userRules.contains(UserChatRule.forwardMessage.name) &&
          !(widget.message?.sentFailed ?? false);
    } else {
      // if is bot, forward follow permission on admin tool
      return EZCache
              .shared
              .chatLoginData
              ?.user
              ?.permission
              ?.botMessage
              ?.forward ??
          false;
    }
  }

  Future<void> _handleCopy(final String? link) async {
    if (!Utils.isURL(link ?? '')) {
      return;
    }
    final http.Response responseData = await http.get(Uri.parse(link ?? ''));
    final uint8list = responseData.bodyBytes;
    final compressBytes = await FlutterImageCompress.compressWithList(
      uint8list,
      minHeight: 1024,
      minWidth: 1024,
      quality: 50,
    );
    final tempDir = await getTemporaryDirectory();
    final File file = await File(
      '${tempDir.path}/img',
    ).writeAsBytes(compressBytes);

    FlutterImageClipboard().copyImageToClipboard(file);
    widget.onCopiedSuccess(compressBytes);
  }

  Widget _buildMessageAction(
    final BuildContext context, {
    required final String iconPath,
    required final String label,
    final bool isDeleteIcon = false,
    final double? iconSize,
    final bool isLeadingIcon = false,
  }) {
    return Row(
      children: [
        if (isLeadingIcon)
          Padding(
            padding: const EdgeInsets.only(right: 4),
            child: EZResources.image(
              ImageParams(
                name: iconPath,
                size: ImageSize.square(iconSize ?? 20),
                color: isDeleteIcon
                    ? Theme.of(context).colorScheme.error
                    : Theme.of(context).hintColor,
              ),
            ),
          ),
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 16,
              color: isDeleteIcon
                  ? Theme.of(context).colorScheme.error
                  : Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ),
        const SizedBox(width: 6),
        if (!isLeadingIcon)
          EZResources.image(
            ImageParams(
              name: iconPath,
              size: ImageSize.square(iconSize ?? 20),
              color: isDeleteIcon
                  ? Theme.of(context).colorScheme.error
                  : Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
      ],
    );
  }

  Widget buildSingleMessage(
    final BuildContext context,
    final ChatItems? messageOrigin,
  ) {
    // final message = (messageOrigin?.forward?.id?.isNotEmpty ?? false)
    //     ? messageOrigin?.copyWith(
    //         content: messageOrigin.forward?.content,
    //         parseMode: messageOrigin.forward?.parseMode,
    //         deepLink: messageOrigin.forward?.deepLink,
    //         attachment: messageOrigin.forward?.attachment,
    //         mention: messageOrigin.forward?.mention
    //             .map(
    //               (final e) => e ?? ChatItemsMention(),
    //             )
    //             .toList(),
    //         sticker: messageOrigin.forward?.sticker,
    //       )
    //     : messageOrigin;
    if (messageOrigin?.forward?.id?.isNotEmpty ?? false) {
      messageOrigin?.content = messageOrigin.forward?.content;
      messageOrigin?.parseMode = messageOrigin.forward?.parseMode;
      messageOrigin?.deepLink = messageOrigin.forward?.deepLink;
      messageOrigin?.attachment = messageOrigin.forward?.attachment ?? [];
      messageOrigin?.mention = messageOrigin.forward?.mention ?? [];
      messageOrigin?.sticker = messageOrigin.forward?.sticker;
    }

    // my message
    if (widget.customerId == messageOrigin?.createdByInfo?.username) {
      return Align(
        alignment: Alignment.centerRight,
        child: MeasureSize(
          onChange: (final size) {
            if (widget.message?.widthSize != null) {
              return;
            }

            widget.message?.widthSize = size.width;
            setState(() {});
          },
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: widget.message?.widthSize != null
                  ? widget.message!.widthSize! + 2
                  : (MediaQuery.sizeOf(context).width - 60),
            ),
            child: _buildUserMessage(context, messageOrigin),
          ),
        ),
      );
    }

    // opposite message
    return MeasureSize(
      onChange: (final size) {
        if (widget.message?.widthSize != null) {
          return;
        }

        widget.message?.widthSize = size.width;
        setState(() {});
      },
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: widget.message?.widthSize != null
              ? widget.message!.widthSize! + 2
              : (MediaQuery.sizeOf(context).width - 60),
        ),
        child: _buildOppositeMessage(context, messageOrigin),
      ),
    );
  }

  Widget _buildUserMessage(
    final BuildContext context,
    final ChatItems? message,
  ) {
    try {
      if (message?.call?.callId?.isNotEmpty ?? false) {
        final timeCall = DateTime.tryParse(
          message?.call?.createdAt ?? '',
        )?.toLocal();
        final duration = _getDurationCall(message?.call?.answerDuration ?? 0);
        return Padding(
          padding: const EdgeInsets.only(right: 10),
          child: CustomPaint(
            painter: CustomChatBubble(
              isOwn: true,
              color: Theme.of(context).primaryColorLight,
            ),
            child: Container(
              // constraints: BoxConstraints(
              //   maxWidth: MediaQuery.sizeOf(context).width - 60,
              // ),
              padding: const EdgeInsets.all(10),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message?.call?.answerDuration == 0
                            ? context.l10n.callEnded
                            : context.l10n.outgoingCall,
                        style: Theme.of(context).textTheme.labelLarge,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          EZResources.image(
                            ImageParams(
                              name: AppIcons.icArrowUpRight,
                              size: const ImageSize.square(14),
                              color: message?.call?.answerDuration == 0
                                  ? Theme.of(context).colorScheme.error
                                  : Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(width: 4),
                          if (timeCall != null)
                            Text(
                              '${DateFormat('HH:mm').format(timeCall)}'
                              ' ${duration.isEmpty ? '' : ', $duration'}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    fontSize: 10,
                                    color: Theme.of(context).primaryColor,
                                  ),
                            ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(width: 20),
                  GestureDetector(
                    onTap: () => _makeCall(),
                    child: EZResources.image(
                      ImageParams(
                        name: AppIcons.icCallRounded,
                        size: const ImageSize.square(22),
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
      if ((message?.sticker?.id?.isNotEmpty ?? false) &&
          (message?.replyInfo?.id?.isEmpty ?? true)) {
        return Padding(
          padding: const EdgeInsets.only(right: 10),
          child: GestureDetector(
            onTap: () => widget.onTapSticker(message?.sticker?.stickerSetId),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (message?.forward?.id?.isNotEmpty ?? false)
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColorLight.withValues(alpha: 0.5),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(14),
                              topRight: Radius.circular(14),
                              bottomLeft: Radius.circular(14),
                              bottomRight: Radius.circular(1),
                            ),
                          ),
                          child: _headerForwardMessage(context, message),
                        ),
                      ),
                    if (message?.sticker?.mimetype?.contains('video') ?? false)
                      ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 182,
                          maxWidth: 182,
                        ),
                        child: AnimatedStickerWidget(message?.sticker),
                      )
                    else
                      EzCachedNetworkImage(
                        imageUrl: message?.sticker?.link,
                        width: 182,
                        height: 182,
                      ),
                  ],
                ),
                if (message?.reactions.isNotEmpty ?? false)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: _buildReaction(context, message, isSticker: true),
                  ),
              ],
            ),
          ),
        );
      }

      if (message?.attachment.isEmpty ?? true) {
        return Padding(
          padding: const EdgeInsets.only(right: 10),
          child: CustomPaint(
            painter: widget.isShowingAvatar
                ? CustomChatBubble(
                    isOwn: true,
                    color: Theme.of(context).primaryColorLight,
                  )
                : null,
            child: Container(
              // constraints: BoxConstraints(
              //   maxWidth: MediaQuery.sizeOf(context).width - 60,
              // ),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColorLight,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(14),
                  topRight: Radius.circular(14),
                  bottomLeft: Radius.circular(14),
                  bottomRight: Radius.circular(8),
                ),
                border: !widget.isShowingAvatar
                    ? Border.all(width: 0.35, color: const Color(0x171A1F4D))
                    : null,
              ),
              child: (message?.poll?.isNotEmpty ?? false)
                  ? _buildPoll(message!.pollInfo!)
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (message?.forward?.id?.isNotEmpty ?? false) ...[
                          _headerForwardMessage(context, message),
                          const SizedBox(height: 2),
                        ],
                        _buildRootMessage(message, context),
                        _buildContentMessage(message, context),
                      ],
                    ),
            ),
          ),
        );
      }

      return Container(
        margin: const EdgeInsets.only(right: 10),
        // constraints: BoxConstraints(
        //   maxWidth: MediaQuery.sizeOf(context).width - 20,
        // ),
        padding:
            (message?.forward?.id?.isNotEmpty ?? false) &&
                (message?.content?.isEmpty ?? true)
            ? const EdgeInsets.fromLTRB(6, 6, 6, 0)
            : EdgeInsets.zero,
        decoration: BoxDecoration(
          color:
              (message?.content?.isEmpty ?? true) &&
                  (message?.forward?.id?.isEmpty ?? true) &&
                  !(message?.replyInfo?.id?.isNotEmpty ?? false)
              ? Colors.transparent
              : Theme.of(context).primaryColorLight,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(14),
            topRight: Radius.circular(14),
            bottomLeft: Radius.circular(14),
            bottomRight: Radius.circular(8),
          ),
          border: (message?.content?.isEmpty ?? true)
              ? null
              : Border.all(width: 0.35, color: const Color(0x171A1F4D)),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if ((message?.forward?.id?.isNotEmpty ?? false) &&
                      (message?.content?.isEmpty ?? true)) ...[
                    _headerForwardMessage(context, message),
                    const SizedBox(height: 2),
                  ],
                  if (message?.replyInfo?.id?.isNotEmpty ?? false)
                    Column(
                      children: [
                        Container(
                          color: Theme.of(
                            context,
                          ).primaryColor.withValues(alpha: 0.1),
                          height: 4,
                        ),
                        _buildRootMessage(message, context),
                      ],
                    ),
                  if (message?.attachment.length == 1)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            if (getFileExtension(
                                  message
                                          ?.attachment
                                          .firstOrNull
                                          ?.originalname ??
                                      '',
                                ) ==
                                FileExtensionType.image)
                              Container(
                                width: message?.widthSize != null
                                    ? ((message?.widthSize ?? 0) + 2)
                                    : null,

                                constraints: BoxConstraints(
                                  maxHeight:
                                      MediaQuery.sizeOf(context).height / 3,
                                ),
                                child: ClipRRect(
                                  borderRadius:
                                      (message?.content?.isEmpty ?? true)
                                      ? BorderRadius.circular(14)
                                      : const BorderRadius.vertical(
                                          top: Radius.circular(14),
                                        ),
                                  child: _buildSingleAdaptiveMessage(
                                    message,
                                    borderRadius: 0,
                                  ),
                                ),
                              )
                            else
                              _buildSingleAdaptiveMessage(message),
                            if (message?.content?.isEmpty ?? true)
                              Positioned(
                                right: 8,
                                bottom: 8,
                                child: _buildMessageTime(
                                  context,
                                  Theme.of(context).colorScheme.surface,
                                  message,
                                ),
                              ),
                          ],
                        ),
                        if ((message?.content?.isEmpty ?? true) &&
                            (message?.reactions.isNotEmpty ?? false))
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: _buildReaction(context, message),
                          ),
                      ],
                    )
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            ClipRRect(
                              borderRadius: (message?.content?.isEmpty ?? true)
                                  ? BorderRadius.circular(14)
                                  : const BorderRadius.vertical(
                                      top: Radius.circular(14),
                                    ),
                              child: _buildGridViewImage(message, context),
                            ),
                            if (message?.content?.isEmpty ?? true)
                              Positioned(
                                right: 8,
                                bottom: 8,
                                child: _buildMessageTime(
                                  context,
                                  Theme.of(context).colorScheme.surface,
                                  message,
                                ),
                              ),
                          ],
                        ),
                        if ((message?.content?.isEmpty ?? true) &&
                            (message?.reactions.isNotEmpty ?? false))
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: _buildReaction(context, message),
                          ),
                      ],
                    ),
                ],
              ),
              const SizedBox(height: 2),
              if (message?.content?.isNotEmpty ?? false)
                CustomPaint(
                  painter: widget.isShowingAvatar
                      ? CustomChatBubble(
                          isOwn: true,
                          color: Theme.of(context).primaryColorLight,
                        )
                      : null,
                  child: Container(
                    // constraints: BoxConstraints(
                    //   maxWidth: MediaQuery.sizeOf(context).width - 60,
                    // ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColorLight,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(14),
                        topRight: Radius.circular(14),
                        bottomLeft: Radius.circular(14),
                        bottomRight: Radius.circular(14),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (message?.forward?.id?.isNotEmpty ?? false) ...[
                          _headerForwardMessage(context, message),
                          const SizedBox(height: 2),
                        ],
                        _buildContentMessage(message, context),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  GestureDetector _headerForwardMessage(
    final BuildContext context,
    final ChatItems? message,
  ) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: widget.onTapForwardMessage,
      child: Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.forwardFrom(':'),
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).primaryColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 2, right: 4),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: EzCachedNetworkImage(
                      imageUrl: message?.forward?.createdByInfo?.avatar,
                      width: 20,
                      height: 20,
                      errorWidget: RandomAvatarWidget(
                        name: message?.forward?.createdByInfo?.name,
                        width: 20,
                        height: 20,
                      ),
                    ),
                  ),
                ),
                if (message?.forward?.sticker?.link?.isNotEmpty ?? false)
                  Expanded(
                    child: Text(
                      message?.forward?.createdByInfo?.name ?? '',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                    ),
                  )
                else
                  message?.widthSize != null
                      ? Expanded(
                          child: Text(
                            message?.forward?.createdByInfo?.name ?? '',
                            style: Theme.of(context).textTheme.labelLarge
                                ?.copyWith(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                      : Text(
                          message?.forward?.createdByInfo?.name ?? '',
                          style: Theme.of(context).textTheme.labelLarge
                              ?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPoll(final ChatItemsPollInfo poll) {
    final List<ChatItemsUserSeen> voteList = [];
    votedOptionIds.clear();
    for (final option in poll.options) {
      option?.members.forEach((final vote) {
        voteList.add(vote);
        if (vote.username == widget.customerId) {
          votedOptionIds.add(option.id);
        }
      });
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(poll.title ?? '', style: Theme.of(context).textTheme.bodyLarge),
        const SizedBox(height: 4),
        if (voteList.isNotEmpty)
          Row(
            children: <Widget>[
              Text(
                (poll.anonymous ?? false)
                    ? context.l10n.anonymous
                    : context.l10n.voted,
              ),
              const SizedBox(width: 6),
              if (!(poll.anonymous ?? false))
                SizedBox(
                  height: 20,
                  child: FlutterImageStack.widgets(
                    itemBorderWidth: 1,
                    itemBorderColor: Theme.of(context).colorScheme.surface,
                    itemRadius: 18,
                    backgroundColor: Colors.transparent,
                    totalCount: voteList.length,
                    extraCountTextStyle: const TextStyle(fontSize: 8),
                    children: voteList
                        .map(
                          (final d) => EzCachedNetworkImage(
                            imageUrl: d.avatar,
                            width: 18,
                            height: 18,
                            errorWidget: RandomAvatarWidget(
                              name: d.username,
                              width: 18,
                              height: 18,
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              const Spacer(),
            ],
          ),
        const SizedBox(height: 16),
        Column(
          children: poll.options
              .map(
                (final e) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(e?.text ?? ''),
                    const SizedBox(height: 4),
                    if (votedOptionIds.isNotEmpty ||
                        poll.status == PollStatus.INACTIVE.name)
                      Row(
                        children: [
                          SizedBox(
                            width: 50,
                            child: Text.rich(
                              textScaler: TextScaler.noScaling,
                              TextSpan(
                                text: voteList.isEmpty
                                    ? '0'
                                    : (((e?.members.length ?? 0) /
                                                  voteList.length) *
                                              100)
                                          .toStringAsFixed(0),
                                children: const [TextSpan(text: '%')],
                              ),
                            ),
                          ),
                          Expanded(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(24),
                              child: Container(
                                height: 10,
                                color: Theme.of(context).dividerColor,
                                child: Row(
                                  children: <Widget>[
                                    Expanded(
                                      flex: e?.members.length ?? 0,
                                      child: Container(
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    Expanded(
                                      flex:
                                          voteList.length -
                                          (e?.members.length ?? 0),
                                      child: Container(
                                        color: Theme.of(context).dividerColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    else
                      RRectCheckboxTitle(
                        size: 20,
                        borderRadius: 24,
                        isCheck: ChatBody.votingList.contains(e?.id),
                        padding: EdgeInsets.zero,
                        titleWidget: const Divider(),
                        onChanged: ({required final bool value}) {
                          if (!(poll.multipleResponse ?? false)) {
                            context.read<ChatBloc>().add(
                              ChatVotePolled(
                                ChatVotePollRequestParams(
                                  messageId: widget.message?.id,
                                  optionIds: [e?.id],
                                  isRetract: false,
                                ),
                              ),
                            );
                          } else {
                            ChatBody.votingList.add(e?.id);
                          }
                        },
                      ),
                    const SizedBox(height: 16),
                  ],
                ),
              )
              .toList(),
        ),
        if ((votedOptionIds.isNotEmpty ||
                poll.status == PollStatus.INACTIVE.name) &&
            !(poll.anonymous ?? false))
          Align(
            child: IconButton(
              onPressed: () async => _showVoteList(voteList),
              icon: Text(
                context.l10n.viewResults,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          )
        else if (poll.multipleResponse ?? false)
          Align(
            child: IconButton(
              onPressed: () {
                final allOptionIds = poll.options
                    .map((final e) => e?.id)
                    .toList();
                final votingCurrent = ChatBody.votingList
                    .where((final e) => allOptionIds.contains(e))
                    .toList();
                context.read<ChatBloc>().add(
                  ChatVotePolled(
                    ChatVotePollRequestParams(
                      messageId: widget.message?.id,
                      optionIds: votingCurrent,
                      isRetract: false,
                    ),
                  ),
                );
                votingCurrent.toList().forEach(
                  (final e) => ChatBody.votingList.remove(e),
                );
              },
              icon: Text(
                context.l10n.voting,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          )
        else
          Align(
            child: Text(
              '${voteList.length} ${context.l10n.voting.toLowerCase()}',
            ),
          ),
      ],
    );
  }

  Future<void> _showVoteList(final List<ChatItemsUserSeen> voteList) {
    return showDialog(
      context: context,
      builder: (final _) => Dialog(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Row(
                children: <Widget>[
                  IconButton(
                    onPressed: Navigator.of(context).pop,
                    icon: const Icon(Icons.close),
                  ),
                  Text(
                    context.l10n.voting,
                    style: Theme.of(
                      context,
                    ).textTheme.titleSmall?.copyWith(fontSize: 18),
                  ),
                ],
              ),
              _detailVoteList(voteList),
            ],
          ),
        ),
      ),
    );
  }

  Padding _detailVoteList(final List<ChatItemsUserSeen> voteList) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        children:
            widget.message?.pollInfo?.options.map((final option) {
              if (option?.members.isEmpty ?? true) {
                return const SizedBox.shrink();
              }
              final ValueNotifier<bool> expand = ValueNotifier(true);
              return ValueListenableBuilder(
                valueListenable: expand,
                builder:
                    (final BuildContext context, final __, final Widget? ___) {
                      final String percent =
                          (((option?.members.length ?? 0) / voteList.length) *
                                  100)
                              .toStringAsFixed(0);
                      return Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              expand.value = !expand.value;
                            },
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    '$percent${Strings.percent}'
                                    '${Strings.hyphenSpace}'
                                    '${option?.text}',
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${option?.members.length ?? 0}'
                                  '${Strings.space}'
                                  '${context.l10n.voting}',
                                ),
                                const SizedBox(width: 8),
                                Icon(
                                  expand.value
                                      ? Icons.keyboard_arrow_down_outlined
                                      : Icons.keyboard_arrow_up_outlined,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          if (expand.value)
                            ...option?.members.map((final vote) {
                                  return ListTile(
                                    leading: ClipOval(
                                      child: EzCachedNetworkImage(
                                        imageUrl: vote.avatar,
                                        width: 44,
                                        height: 44,
                                        errorWidget: RandomAvatarWidget(
                                          name: vote.name,
                                          width: 44,
                                          height: 44,
                                        ),
                                      ),
                                    ),
                                    title: Text(vote.name ?? ''),
                                  );
                                }).toList() ??
                                [],
                        ],
                      );
                    },
              );
            }).toList() ??
            [],
      ),
    );
  }

  // Widget _buildForwardMessage(
  //   final ChatItems? message,
  //   final BuildContext context,
  // ) {
  //   if (message?.forward?.id?.isNotEmpty ?? false) {
  //     return MessageTile(
  //       message: widget.message,
  //       customerId: widget.customerId,
  //       previewDataList: widget.previewDataList,
  //       imageUrlList: widget.imageUrlList,
  //       taskInfo: widget.taskInfo,
  //       isShowingAvatar: widget.isShowingAvatar,
  //       isShowingName: widget.isShowingName,
  //       isGroup: widget.isGroup,
  //       onRemoved: widget.onRemoved,
  //       onRemovedSuccess: widget.onRemovedSuccess,
  //       onReply: widget.onReply,
  //       onEdit: widget.onEdit,
  //       onPin: widget.onPin,
  //       onUnpin: widget.onUnpin,
  //       onForward: widget.onForward,
  //       onTapRootMessage: widget.onTapRootMessage,
  //       onTapForwardMessage: widget.onTapForwardMessage,
  //       onTapUserTag: widget.onTapUserTag,
  //       onSwipeLeft: widget.onSwipeLeft,
  //       onReact: widget.onReact,
  //       onShowMenuPopup: widget.onShowMenuPopup,
  //       onCopiedSuccess: widget.onCopiedSuccess,
  //       userRole: widget.userRole,
  //       isPin: widget.isPin,
  //       onRetractVote: widget.onRetractVote,
  //       onStopVote: widget.onStopVote,
  //       membersInfo: widget.membersInfo,
  //       onRetry: widget.onRetry,
  //     );
  //   }
  //   return const SizedBox.shrink();
  // }

  Widget _buildRootMessage(
    final ChatItems? message,
    final BuildContext context, {
    final bool isOpposite = false,
  }) {
    final thumbnail =
        message?.replyInfo?.attachment.firstOrNull?.thumbnail ??
        message?.replyInfo?.attachment.firstOrNull?.link ??
        message?.replyInfo?.sticker?.link ??
        '';

    final isMedia =
        thumbnail.isNotEmpty &&
        ((message?.replyInfo?.attachment.firstOrNull?.mimetype?.contains(
                  'image',
                ) ??
                false) ||
            (message?.replyInfo?.attachment.firstOrNull?.mimetype?.contains(
                  'video',
                ) ??
                false));

    final isSticker = message?.replyInfo?.sticker?.link?.isNotEmpty ?? false;
    if (message?.replyInfo?.id?.isNotEmpty ?? false) {
      return GestureDetector(
        onTap: widget.onTapRootMessage,
        child: Container(
          constraints: message?.widthSize != null
              ? BoxConstraints(maxWidth: message!.widthSize!)
              : null,
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
            border: Border(
              left: BorderSide(color: Theme.of(context).primaryColor, width: 3),
            ),
          ),
          child: Row(
            mainAxisSize: message?.widthSize != null
                ? MainAxisSize.max
                : MainAxisSize.min,
            children: [
              if (isMedia)
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: EzCachedNetworkImage(
                      width: 40,
                      height: 40,
                      imageUrl: thumbnail,
                    ),
                  ),
                ),
              if (isSticker)
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: (mime(thumbnail)?.contains('image') ?? false)
                        ? EzCachedNetworkImage(
                            width: 40,
                            height: 40,
                            imageUrl: thumbnail,
                          )
                        : FutureBuilder(
                            future: Utils.generateThumbnail(thumbnail),
                            builder: (final _, final snapshot) {
                              return snapshot.hasData
                                  ? Image.memory(
                                      snapshot.data!,
                                      width: 40,
                                      height: 40,
                                    )
                                  : const SizedBox.shrink();
                            },
                          ),
                  ),
                ),
              if (message?.widthSize != null)
                Expanded(
                  child: _buildContentRootMessage(message, context, isMedia),
                )
              else
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth:
                        MediaQuery.sizeOf(context).width -
                        150 -
                        (isMedia || isSticker ? 50 : 0),
                  ),
                  child: _buildContentRootMessage(message, context, isMedia),
                ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Column _buildContentRootMessage(
    final ChatItems? message,
    final BuildContext context,
    final bool isMedia,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          ((message?.forward?.id?.isNotEmpty ?? false)
                  ? message?.forward?.createdByInfo?.name
                  : message?.replyInfo?.createdByInfo?.name) ??
              '',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: Theme.of(context).primaryColor,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (message?.replyInfo?.sticker?.link?.isNotEmpty ?? false)
          Text(
            context.l10n.sticker,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          )
        else if ((message?.replyInfo?.content?.isEmpty ?? false) && !isMedia)
          LastMessageWidget(
            textScale: context.watch<FontsBloc>().textScale,
            lastMessageInfo: ChatItems(
              id: message?.replyInfo?.id,
              content: message?.replyInfo?.content,
              createdAt: message?.replyInfo?.createdAt,
              createdByInfo: ChatItemsCreatedByInfo(
                username: message?.replyInfo?.createdByInfo?.username,
                name: message?.replyInfo?.createdByInfo?.name,
                avatar: message?.replyInfo?.createdByInfo?.avatar,
                id: message?.replyInfo?.createdByInfo?.id,
              ),
              attachment: message?.replyInfo?.attachment ?? [],
            ),
          )
        else
          _buildAdaptiveText(
            ChatItems(
              id: message?.replyInfo?.id,
              content: message?.replyInfo?.content,
              createdAt: message?.replyInfo?.createdAt,
              createdByInfo: ChatItemsCreatedByInfo(
                username: message?.replyInfo?.createdByInfo?.username,
                name: message?.replyInfo?.createdByInfo?.name,
                avatar: message?.replyInfo?.createdByInfo?.avatar,
                id: message?.replyInfo?.createdByInfo?.id,
              ),
              attachment: message?.replyInfo?.attachment ?? [],
            ),
            context,
            maxLines: 1,
            fontSize: 14,
          ),
      ],
    );
  }

  //ignore: long-method
  Widget _buildOppositeMessage(
    final BuildContext context,
    final ChatItems? message,
  ) {
    try {
      if (message?.call?.callId?.isNotEmpty ?? false) {
        final timeCall = DateTime.tryParse(
          message?.call?.createdAt ?? '',
        )?.toLocal();
        final duration = _getDurationCall(message?.call?.answerDuration ?? 0);
        return Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(left: 8),
            child: CustomPaint(
              painter: CustomChatBubble(
                isOwn: false,
                color: Theme.of(context).colorScheme.surface,
              ),
              child: Container(
                // constraints: BoxConstraints(
                //   maxWidth: MediaQuery.sizeOf(context).width - 60,
                // ),
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          message?.call?.answerDuration == 0
                              ? context.l10n.missedCall
                              : context.l10n.incomingCall,
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            EZResources.image(
                              ImageParams(
                                name: AppIcons.icArrowDownLeft,
                                size: const ImageSize.square(14),
                                color: message?.call?.answerDuration == 0
                                    ? Theme.of(context).colorScheme.error
                                    : Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(width: 4),
                            if (timeCall != null)
                              Text(
                                '${DateFormat('HH:mm').format(timeCall)}'
                                ' ${duration.isEmpty ? '' : ', $duration'}',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      fontSize: 10,
                                      color: Theme.of(context).hintColor,
                                    ),
                              ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(width: 20),
                    GestureDetector(
                      onTap: () => _makeCall(),
                      child: EZResources.image(
                        ImageParams(
                          name: AppIcons.icCallRounded,
                          size: const ImageSize.square(22),
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
      if (message?.sticker?.id?.isNotEmpty ?? false) {
        return GestureDetector(
          onTap: () => widget.onTapSticker(message?.sticker?.stickerSetId),
          child: Padding(
            padding: EdgeInsets.only(
              left: widget.isGroup ? 2.5 : 12.5,
              bottom: widget.isShowingAvatar ? 2.5 : 0,
              top: widget.isShowingName ? 2.5 : 0,
            ),
            child: Stack(
              children: [
                if (widget.isShowingAvatar && widget.isGroup)
                  Positioned(
                    left: 0,
                    bottom: 0,
                    child: InkWell(
                      onTap: () async {
                        context.router.push(
                          StoryPersonRoute(
                            codeUser: Utils.defaultOnEmpty(
                              message?.createdByInfo?.username,
                            ),
                          ),
                        );
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(32),
                        child: EzCachedNetworkImage(
                          imageUrl: message?.createdByInfo?.avatar,
                          width: 36,
                          height: 36,
                          errorWidget: RandomAvatarWidget(
                            name: message?.createdByInfo?.name,
                            width: 36,
                            height: 36,
                          ),
                        ),
                      ),
                    ),
                  ),
                Container(
                  margin: EdgeInsets.only(left: widget.isGroup ? 48 : 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (message?.forward?.id?.isNotEmpty ?? false)
                            Expanded(
                              child: Container(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.surface.withValues(alpha: 0.6),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(14),
                                    topRight: Radius.circular(14),
                                    bottomLeft: Radius.circular(14),
                                    bottomRight: Radius.circular(1),
                                  ),
                                ),
                                child: _headerForwardMessage(context, message),
                              ),
                            ),
                          if (message?.sticker?.mimetype?.contains('video') ??
                              false)
                            ConstrainedBox(
                              constraints: const BoxConstraints(
                                maxHeight: 182,
                                maxWidth: 182,
                              ),
                              child: AnimatedStickerWidget(message?.sticker),
                            )
                          else
                            EzCachedNetworkImage(
                              imageUrl: message?.sticker?.link,
                              width: 182,
                              height: 182,
                            ),
                        ],
                      ),
                      if (message?.reactions.isNotEmpty ?? false)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: _buildReaction(
                            context,
                            message,
                            isSticker: true,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }

      return Padding(
        padding: EdgeInsets.only(
          bottom: widget.isShowingAvatar ? 2.5 : 0,
          top: widget.isShowingName ? 2.5 : 0,
        ),
        child: Container(
          constraints: BoxConstraints(
            // maxWidth: MediaQuery.sizeOf(context).width - 60,
            minHeight: widget.isShowingAvatar ? 36 : 0,
          ),
          child: Stack(
            children: <Widget>[
              if (widget.isShowingAvatar && widget.isGroup)
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: InkWell(
                    onTap: () async {
                      context.router.push(
                        StoryPersonRoute(
                          codeUser: Utils.defaultOnEmpty(
                            message?.createdByInfo?.username,
                          ),
                        ),
                      );
                    },
                    child: Utils.isURL(message?.createdByInfo?.avatar ?? '')
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(32),
                            child: EzCachedNetworkImage(
                              imageUrl: message?.createdByInfo?.avatar,
                              width: 36,
                              height: 36,
                            ),
                          )
                        : RandomAvatarWidget(
                            name: message?.createdByInfo?.name,
                            width: 36,
                            height: 36,
                          ),
                  ),
                ),
              if (message?.attachment.isEmpty ?? true)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: widget.isGroup ? 50 : 10),
                      child: CustomPaint(
                        painter: widget.isShowingAvatar
                            ? CustomChatBubble(
                                isOwn: false,
                                color: Theme.of(context).colorScheme.surface,
                              )
                            : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 5,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(14),
                              topRight: Radius.circular(14),
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(14),
                            ),
                            border: !widget.isShowingAvatar
                                ? Border.all(
                                    width: 0.35,
                                    color: const Color(0x171A1F4D),
                                  )
                                : null,
                          ),
                          child: (message?.poll?.isNotEmpty ?? false)
                              ? _buildPoll(message!.pollInfo!)
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (widget.isShowingName && widget.isGroup)
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () async {
                                          context.router.push(
                                            StoryPersonRoute(
                                              codeUser: Utils.defaultOnEmpty(
                                                message
                                                    ?.createdByInfo
                                                    ?.username,
                                              ),
                                            ),
                                          );
                                        },
                                        child: Text(
                                          message?.createdByInfo?.name ?? '',
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall
                                              ?.copyWith(
                                                color: Theme.of(
                                                  context,
                                                ).primaryColor,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ),
                                    if (message?.forward?.id?.isNotEmpty ??
                                        false) ...[
                                      _headerForwardMessage(context, message),
                                      const SizedBox(height: 2),
                                    ],
                                    _buildRootMessage(
                                      message,
                                      context,
                                      isOpposite: true,
                                    ),
                                    _buildContentMessage(message, context),
                                  ],
                                ),
                        ),
                      ),
                    ),
                    if (message?.replyMarkup?.inlineKeyboard.isNotEmpty ??
                        false) ...[
                      if ((message?.replyMarkup?.inlineKeyboard.length ?? 0) >
                          3)
                        Container(
                          height: 44,
                          padding: const EdgeInsets.only(left: 10),
                          width: MediaQuery.sizeOf(context).width,
                          child: ListView.separated(
                            itemCount:
                                message?.replyMarkup?.inlineKeyboard.length ??
                                0,
                            padding: const EdgeInsets.only(top: 4),
                            scrollDirection: Axis.horizontal,
                            separatorBuilder: (final _, final __) =>
                                const SizedBox(width: 4),
                            itemBuilder: (final _, final index) {
                              final button =
                                  message?.replyMarkup?.inlineKeyboard[index];
                              return _buildBotReplyButton(
                                context,
                                message,
                                button,
                              );
                            },
                          ),
                        )
                      else
                        Padding(
                          padding: const EdgeInsets.only(left: 10),
                          child: Row(
                            spacing: 4,
                            children:
                                message?.replyMarkup?.inlineKeyboard
                                    .map(
                                      (final button) => Expanded(
                                        child: _buildBotReplyButton(
                                          context,
                                          message,
                                          button,
                                        ),
                                      ),
                                    )
                                    .toList() ??
                                [],
                          ),
                        ),
                      const SizedBox(height: 2),
                    ],
                  ],
                )
              else // Message with attachments
                Container(
                  padding:
                      (message?.forward?.id?.isNotEmpty ?? false) &&
                          (message?.content?.isEmpty ?? true)
                      ? const EdgeInsets.fromLTRB(6, 6, 6, 0)
                      : EdgeInsets.zero,
                  margin:
                      (message?.forward?.id?.isNotEmpty ?? false) &&
                          (message?.content?.isEmpty ?? true) &&
                          widget.isGroup
                      ? const EdgeInsets.only(left: 50)
                      : EdgeInsets.only(left: 10 + (widget.isGroup ? 40 : 0)),
                  decoration: BoxDecoration(
                    color:
                        (message?.content?.isEmpty ?? true) &&
                            (message?.forward?.id?.isEmpty ?? true) &&
                            !(message?.replyInfo?.id?.isNotEmpty ?? false)
                        ? Colors.transparent
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(14),
                      topRight: const Radius.circular(14),
                      bottomLeft: widget.isShowingAvatar
                          ? const Radius.circular(8)
                          : const Radius.circular(14),
                      bottomRight: const Radius.circular(14),
                    ),
                    border: (message?.content?.isEmpty ?? true)
                        ? null
                        : Border.all(
                            width: 0.35,
                            color: const Color(0x171A1F4D),
                          ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if ((message?.forward?.id?.isNotEmpty ?? false) &&
                          (message?.content?.isEmpty ?? true)) ...[
                        _headerForwardMessage(context, message),
                        const SizedBox(height: 2),
                      ],
                      if (message?.replyInfo?.id?.isNotEmpty ?? false)
                        Column(
                          children: [
                            Container(
                              color: Theme.of(
                                context,
                              ).primaryColor.withValues(alpha: 0.1),
                              height: 4,
                            ),
                            _buildRootMessage(message, context),
                          ],
                        ),
                      if (message?.attachment.length == 1)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Stack(
                              children: [
                                if (getFileExtension(
                                      message
                                              ?.attachment
                                              .firstOrNull
                                              ?.originalname ??
                                          '',
                                    ) ==
                                    FileExtensionType.image)
                                  Container(
                                    width: message?.widthSize != null
                                        ? ((message?.widthSize ?? 0) + 2)
                                        : null,

                                    constraints: BoxConstraints(
                                      maxHeight:
                                          MediaQuery.sizeOf(context).height / 3,
                                    ),
                                    child: ClipRRect(
                                      borderRadius:
                                          (message?.content?.isEmpty ?? true)
                                          ? BorderRadius.circular(14)
                                          : const BorderRadius.vertical(
                                              top: Radius.circular(14),
                                            ),
                                      child: _buildSingleAdaptiveMessage(
                                        message,
                                        borderRadius: 0,
                                      ),
                                    ),
                                  )
                                else
                                  _buildSingleAdaptiveMessage(message),
                                if (message?.content?.isEmpty ?? true)
                                  Positioned(
                                    right: 8,
                                    bottom: 8,
                                    child: _buildMessageTime(
                                      context,
                                      Theme.of(context).colorScheme.surface,
                                      message,
                                    ),
                                  ),
                              ],
                            ),
                            if ((message?.content?.isEmpty ?? true) &&
                                (message?.reactions.isNotEmpty ?? false))
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 4,
                                ),
                                child: _buildReaction(context, message),
                              ),
                          ],
                        )
                      else
                        Container(
                          alignment: Alignment.centerLeft,
                          // margin: EdgeInsets.only(
                          //   left:
                          //       widget.isGroup &&
                          //           !((message?.forward?.id?.isNotEmpty ??
                          //                   false) &&
                          //               (message?.content?.isEmpty ?? true))
                          //       ? 42
                          //       : 0,
                          // ),
                          constraints: BoxConstraints(
                            maxWidth: max(
                              MediaQuery.sizeOf(context).width /
                                  (MediaQuery.sizeOf(context).width <
                                          MediaQuery.sizeOf(context).height
                                      ? 1.2
                                      : 2.2),
                              300,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius:
                                        (message?.content?.isEmpty ?? true)
                                        ? BorderRadius.circular(14)
                                        : const BorderRadius.vertical(
                                            top: Radius.circular(14),
                                          ),
                                    child: _buildGridViewImage(
                                      message,
                                      context,
                                    ),
                                  ),
                                  if (message?.content?.isEmpty ?? true)
                                    Positioned(
                                      right: 8,
                                      bottom: 8,
                                      child: _buildMessageTime(
                                        context,
                                        Theme.of(context).colorScheme.surface,
                                        message,
                                      ),
                                    ),
                                ],
                              ),
                              if ((message?.content?.isEmpty ?? true) &&
                                  (message?.reactions.isNotEmpty ?? false))
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 4,
                                  ),
                                  child: _buildReaction(context, message),
                                ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 2),
                      if (message?.content?.isNotEmpty ?? false)
                        CustomPaint(
                          painter: widget.isShowingAvatar
                              ? CustomChatBubble(
                                  isOwn: false,
                                  color: Theme.of(context).colorScheme.surface,
                                )
                              : null,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(14),
                                topRight: Radius.circular(14),
                                bottomLeft: Radius.circular(14),
                                bottomRight: Radius.circular(14),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (message?.forward?.id?.isNotEmpty ??
                                    false) ...[
                                  _headerForwardMessage(context, message),
                                  const SizedBox(height: 2),
                                ],
                                _buildContentMessage(message, context),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  CustomSizeButton _buildBotReplyButton(
    final BuildContext context,
    final ChatItems? message,
    final dynamic button,
  ) {
    return CustomSizeButton(
      height: 36,
      width: MediaQuery.sizeOf(context).width / 3,
      borderRadius: 8,
      borderColor: Theme.of(context).primaryColor.withValues(alpha: 0.35),
      color: Theme.of(context).primaryColor.withValues(alpha: 0.35),
      onTap: () {
        context.read<ChatBloc>().add(
          ChatRepliedBotMessage(
            ChatReplyBotMessageRequestParams(
              messageId: message?.id,
              replyData: button,
            ),
          ),
        );
      },
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Text(
          (button as Map)['text'],
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: Theme.of(context).colorScheme.surface,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildContentMessage(
    final ChatItems? message,
    final BuildContext context,
  ) {
    if (message?.reactions.isEmpty ?? true) {
      if (message?.widthSize == null ||
          calculateTextWidth(message, justContent: true) >
              (MediaQuery.sizeOf(context).width - 160)) {
        return Text.rich(
          textScaler: TextScaler.noScaling,
          textWidthBasis: TextWidthBasis.longestLine,
          textAlign: TextAlign.right,

          TextSpan(
            children: [
              WidgetSpan(child: _buildAdaptiveText(message, context)),
              const WidgetSpan(child: SizedBox(width: 5)),
              WidgetSpan(
                alignment: PlaceholderAlignment.top,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (message?.isEdited ?? false)
                      _buildEditedText(context, null, message),
                    _buildMessageTime(context, null, message),
                  ],
                ),
              ),
            ],
          ),
        );
      }
      return Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          Expanded(child: _buildAdaptiveText(message, context)),
          if (message?.isEdited ?? false)
            _buildEditedText(context, null, message),
          _buildMessageTime(context, null, message),
        ],
      );
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAdaptiveText(message, context),
        const SizedBox(height: 4),
        LayoutBuilder(
          builder: (final _, final constraints) {
            final titleWidth = calculateTextWidth(message);
            if (message?.widthSize == null) {
              return ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: max(
                    (((message?.isEdited ?? false) ? 100 : 60) *
                            context.watch<FontsBloc>().textScale) +
                        ((message?.reactions
                                    .map((final e) => e?.icon)
                                    .toSet()
                                    .length ??
                                0) *
                            65),
                    titleWidth,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: _buildReaction(context, message),
                      ),
                    ),
                    if (message?.isEdited ?? false)
                      _buildEditedText(context, null, message),
                    _buildMessageTime(context, null, message),
                  ],
                ),
              );
            } else {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  Expanded(child: _buildReaction(context, message)),
                  if (message?.isEdited ?? false)
                    _buildEditedText(context, null, message),
                  _buildMessageTime(context, null, message),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  double calculateTextWidth(
    final ChatItems? message, {
    final bool justContent = false,
  }) {
    try {
      final forwardUserName = message?.forward?.createdByInfo?.name ?? '';
      final rootMessage = message?.replyInfo?.content ?? '';

      String longestText = message?.content ?? '';
      if (forwardUserName.length > longestText.length) {
        longestText = forwardUserName;
      }
      if (rootMessage.length > longestText.length) {
        longestText = rootMessage;
      }

      if (justContent) {
        longestText = message?.content ?? '';
      }

      final textSpan = TextSpan(
        text: Bidi.stripHtmlIfNeeded(longestText),
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontSize: 16,
          height: 22 / 16,
          letterSpacing: -0.4,
        ),
      );

      final tp = TextPainter(
        text: textSpan,
        maxLines: 1,
        textDirection: TextDirection.ltr,
      );
      tp.layout(maxWidth: MediaQuery.sizeOf(context).width - 60);
      return tp.size.width * context.watch<FontsBloc>().textScale;
    } catch (_) {
      return MediaQuery.sizeOf(context).width -
          130 -
          ((message?.isEdited ?? false) ? 50 : 0);
    }
  }

  StaggeredGrid _buildGridViewImage(
    final ChatItems? message,
    final BuildContext context,
  ) {
    return StaggeredGrid.count(
      crossAxisCount: 2,
      mainAxisSpacing: 1,
      crossAxisSpacing: 1,
      axisDirection: AxisDirection.down,
      children:
          message?.attachment.mapIndexed((final index, final item) {
            final fileExtension = getFileExtension(item?.originalname ?? '');

            if (message.attachment.length % 2 != 0 && index == 0) {
              return StaggeredGridTile.count(
                crossAxisCellCount: 2,
                mainAxisCellCount:
                    (fileExtension != FileExtensionType.video &&
                        fileExtension != FileExtensionType.image)
                    ? min(
                        (MediaQuery.sizeOf(context).height / 2) /
                            MediaQuery.sizeOf(context).width,
                        0.62,
                      )
                    : 1,
                child: _buildAdaptiveMessage(fileExtension, item, message),
              );
            }

            return StaggeredGridTile.count(
              crossAxisCellCount:
                  (fileExtension != FileExtensionType.video &&
                      fileExtension != FileExtensionType.image)
                  ? 2
                  : 1,
              mainAxisCellCount:
                  (fileExtension != FileExtensionType.video &&
                      fileExtension != FileExtensionType.image)
                  ? min(
                      (MediaQuery.sizeOf(context).height / 2) /
                          MediaQuery.sizeOf(context).width,
                      0.62,
                    )
                  : 1,
              child: _buildAdaptiveMessage(fileExtension, item, message),
            );
          }).toList() ??
          [],
    );
  }

  String? filterTag(final ChatItems? message) {
    String? result = message?.content ?? '';
    result = result.replaceAll('\n', '<br>');
    message?.mention.forEach((final element) {
      if (element?.text != null) {
        result = result?.replaceAll(
          element!.text!,
          '<a style="text-decoration: none" href="${element.text}">${element.username} - ${element.label}</a>',
        );
      }
    });

    final listRegExpMatch = RegExp(
      regexLink,
    ).allMatches(message?.content ?? '');
    final setLink = listRegExpMatch
        .map(
          (final regExpMatch) => regExpMatch.group(0)?.replaceAll('&nbsp;', ''),
        )
        .toSet();
    for (final element in setLink) {
      String? url = element;
      String? lastCharacter = url?[url.length - 1];
      // remove symbol last character
      while (RegExp(r'[$-/:;<>-?{-~!"^_`\[\]]').hasMatch(lastCharacter ?? '')) {
        url = url?.substring(0, url.length - 1);
        lastCharacter = url?[url.length - 1];
      }
    }
    // replace link text with href tag
    result = wrapLinks(result ?? '');
    return result;
  }

  // Function to remove HTML tags
  String removeHtmlTags(final String input) {
    final RegExp tagRegex = RegExp('<[^>]*>', multiLine: true);
    return input.replaceAll(tagRegex, '');
  }

  // Function to extract list of plain text strings from HTML content
  List<String> extractPlainText(final String htmlContent) {
    final RegExp tagRegex = RegExp('<[^>]*>', multiLine: true);

    // Split the HTML content by tags
    final List<String> splitContent = htmlContent.split(tagRegex);

    // Remove empty strings and trim the remaining strings
    final List<String> plainTextList = splitContent
        .where((final element) => element.trim().isNotEmpty)
        .map((final e) => e.trim())
        .toList();

    return plainTextList;
  }

  // Function to wrap links with <a> tag
  String wrapLinks(String text) {
    // Regex to find URLs
    final RegExp urlRegex = RegExp(
      r'https?:\/\/[\w./?=&%#-]+',
      multiLine: true,
    );
    // Regex to find <a> tags containing URLs
    final RegExp hrefRegex = RegExp(
      r'<a\s+[^>]*href="([^"]+)"[^>]*>',
      multiLine: true,
    );

    final plainTextList = extractPlainText(text);

    for (final segment in plainTextList) {
      final Iterable<Match> urls = urlRegex.allMatches(segment);

      if (urls.isNotEmpty) {
        for (final Match match in urls) {
          final String url = match.group(0)!;
          // Check if the URL is already wrapped by an <a> tag
          if (!hrefRegex.hasMatch(segment)) {
            text = text.replaceAll(
              url,
              '<a href="${removeHtmlTags(url)}" target="_blank" class="text-primary text-sub">$url</a>',
            );
          }
        }
      }
    }
    return text;
  }

  Widget _buildAdaptiveText(
    final ChatItems? message,
    final BuildContext context, {
    final int? maxLines,
    final double fontSize = 16,
  }) {
    if (message?.parseMode == MessageParseMode.html.name ||
        RegExp('<[^>]+>').hasMatch(message?.content ?? '')) {
      if (maxLines == 1) {
        return Text(
          Bidi.stripHtmlIfNeeded(message?.content ?? ''),
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontSize: fontSize,
            height: 22 / 16,
            letterSpacing: -0.4,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        );
      }
      return HtmlWidget(
        filterTag(message) ?? '',
        textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontSize: fontSize,
          height: 22 / 16,
          letterSpacing: -0.4,
        ),
        onTapUrl: (final link) async {
          if (link.startsWith('@')) {
            widget.onTapUserTag(link.replaceAll('@', ''));
          } else {
            Utils.onTapDynamicLink(link: link, linkType: LinkType.url);
          }
          return true;
        },
      );
    }
    if (RegExp(regexLink).hasMatch(message?.content ?? '')) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: LinkPreview(
          padding: EdgeInsets.zero,
          enableAnimation: true,
          textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontSize: fontSize,
            height: 22 / 16,
            letterSpacing: -0.4,
          ),
          onPreviewDataFetched: (final data) {
            if (widget.previewDataList[message?.content] == null) {
              widget.previewDataList[message?.content ?? ''] = data;
              setState(() {});
            }
          },
          previewData: widget.previewDataList[message?.content ?? ''],
          text: message?.content ?? '',
          textWidget: widget.isPopupMessage
              ? SelectionArea(
                  child: _buildLinkifyText(
                    message,
                    maxLines,
                    context,
                    fontSize: fontSize,
                  ),
                )
              : _buildLinkifyText(
                  message,
                  maxLines,
                  context,
                  fontSize: fontSize,
                ),
          width: MediaQuery.sizeOf(context).width,
          onLinkPressed: (message?.deepLink?.isNotEmpty ?? false)
              ? (final _) async => getIt<DeeplinkHelper>().handleDeepLinkRoutes(
                  Uri.tryParse(message?.deepLink ?? ''),
                )
              : null,
        ),
      );
    }
    if (widget.isPopupMessage) {
      return SelectionArea(
        child: _buildLinkifyText(
          message,
          maxLines,
          context,
          fontSize: fontSize,
        ),
      );
    }
    return _buildLinkifyText(message, maxLines, context, fontSize: fontSize);
  }

  // widget auto detect userTag, phone, email, etc.
  LinkifyText _buildLinkifyText(
    final ChatItems? message,
    final int? maxLines,
    final BuildContext context, {
    required final double fontSize,
  }) {
    return LinkifyText(
      message?.content ?? '',
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : null,
      textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        fontSize: fontSize,
        height: 22 / 16,
        letterSpacing: -0.4,
      ),
      linkStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        fontSize: fontSize,
        color: Theme.of(context).primaryColor,
        height: 22 / 16,
        letterSpacing: -0.4,
      ),
      locale: const Locale(AppLocale.vi),
      onTap: (final link) async {
        if (link.type == LinkType.userTag) {
          widget.onTapUserTag(link.value?.replaceAll('@', '') ?? '');
          return;
        }
        Utils.onTapDynamicLink(link: link.value, linkType: link.type);
      },
      linkTypes: const [
        LinkType.userTag,
        LinkType.url,
        LinkType.email,
        LinkType.hashTag,
        LinkType.phone,
      ],
      mapReplace: (message?.mention.isEmpty ?? true)
          ? Map.fromEntries(
              widget.membersInfo.map(
                (final e) => MapEntry(
                  '@${e?.username}',
                  '${e?.username} - ${e?.name ?? ''}',
                ),
              ),
            )
          : Map.fromEntries(
              message?.mention.map(
                    (final e) => MapEntry(
                      '@${e?.username}',
                      '${e?.username} - ${e?.label ?? ''}',
                    ),
                  ) ??
                  [],
            ),
    );
  }

  AdaptiveMessage _buildSingleAdaptiveMessage(
    final ChatItems? message, {
    final double? borderRadius,
  }) {
    return AdaptiveMessage(
      type: getFileExtension(
        message?.attachment.firstOrNull?.originalname ?? '',
      ),
      data: message?.attachment.firstOrNull?.link,
      taskInfo: widget.taskInfo,
      fileName: message?.attachment.firstOrNull?.originalname,
      fileSize: message?.attachment.firstOrNull?.size,
      senderId: widget.message?.createdByInfo?.username,
      onEdited: widget.onSendEditedImage,
      thumbnail: message?.attachment.firstOrNull?.thumbnail,
      width: message?.attachment.firstOrNull?.width,
      height: message?.attachment.firstOrNull?.height,
      duration: message?.attachment.firstOrNull?.duration,
      waveForm: message?.attachment.firstOrNull?.waveform
          ?.map((final e) => (e ?? 0) / 255)
          .toList(),
      message: message,
      imageUrlList: widget.imageUrlList,
      localId: message?.attachment.firstOrNull?.localId,
      isPopupMessage: widget.isPopupMessage,
      borderRadius: borderRadius,
    );
  }

  AdaptiveMessage _buildAdaptiveMessage(
    final FileExtensionType fileExtension,
    final ChatItemsAttachment? item,
    final ChatItems? message,
  ) {
    return AdaptiveMessage(
      type: fileExtension,
      senderId: widget.message?.createdByInfo?.username,
      fileName: item?.originalname,
      fileSize: item?.size,
      data: item?.link,
      taskInfo: widget.taskInfo,
      onEdited: widget.onSendEditedImage,
      thumbnail: item?.thumbnail,
      width: item?.width,
      height: item?.height,
      duration: item?.duration,
      waveForm: item?.waveform?.map((final e) => (e ?? 0) / 255).toList(),
      message: message,
      imageUrlList: widget.imageUrlList,
      localId: item?.localId,
      borderRadius: 0,
      isPopupMessage: widget.isPopupMessage,
    );
  }

  // List<InlineSpan> _buildFooter(
  //   final BuildContext context,
  //   final ChatItems? message, {
  //   final Color? colorText,
  // }) {
  //   return [
  //     TextSpan(
  //       children: [
  //         WidgetSpan(
  //           alignment: PlaceholderAlignment.middle,
  //           child: Row(
  //             mainAxisSize: MainAxisSize.min,
  //             crossAxisAlignment: CrossAxisAlignment.end,
  //             children: [
  //               if (message?.reactions.isNotEmpty ?? false)
  //                 _buildReaction(context, message),
  //               if (message?.isEdited ?? false)
  //                 _buildEditedText(context, colorText, message),
  //               _buildMessageTime(context, colorText, message),
  //             ],
  //           ),
  //         ),
  //       ],
  //     ),
  //   ];
  // }

  Tooltip _buildMessageTime(
    final BuildContext context,
    final Color? colorText,
    final ChatItems? message,
  ) {
    return Tooltip(
      decoration: BoxDecoration(
        color: const Color(0x33010101),
        borderRadius: BorderRadius.circular(32),
      ),
      message: _getTimeString(showTimeDetail: true),
      child: Container(
        padding: EdgeInsets.fromLTRB(6, 1.5, colorText == null ? 0 : 6, 1.5),
        decoration: colorText == null
            ? null
            : BoxDecoration(
                color: const Color(0x33010101),
                borderRadius: BorderRadius.circular(32),
              ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getTimeString(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color:
                    colorText ??
                    (widget.customerId == message?.createdByInfo?.username
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).hintColor),
                fontSize: 11,
                height: 13 / 11,
              ),
            ),
            if (widget.customerId == message?.createdByInfo?.username)
              if (message?.id == null)
                Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: Icon(
                    (message?.sentFailed ?? false)
                        ? Icons.error
                        : Icons.watch_later_outlined,
                    size: 12,
                    color: (message?.sentFailed ?? false)
                        ? Theme.of(context).colorScheme.error
                        : Theme.of(context).primaryColor,
                  ),
                )
              else
                Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: (message?.seen ?? false)
                      ? EZResources.image(
                          ImageParams(
                            name: AppIcons.icDoubleCheck,
                            size: const ImageSize.square(14),
                            color: colorText ?? Theme.of(context).primaryColor,
                          ),
                        )
                      : Icon(
                          Icons.check,
                          size: 12,
                          color: colorText ?? Theme.of(context).primaryColor,
                        ),
                ),
          ],
        ),
      ),
    );
  }

  Padding _buildEditedText(
    final BuildContext context,
    final Color? colorText,
    final ChatItems? message,
  ) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 2),
      child: Text(
        context.l10n.edited + Strings.space,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color:
              colorText ??
              (widget.customerId == message?.createdByInfo?.username
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).hintColor),
          fontSize: 11,
        ),
      ),
    );
  }

  Widget _buildReaction(
    final BuildContext context,
    final ChatItems? message, {
    final bool isSticker = false,
  }) {
    String? reactedIcon;

    final List<String?> items =
        message?.reactions.map((final e) {
          if (e?.username == widget.customerId) {
            reactedIcon = e?.icon;
          }
          return e?.icon;
        }).toList() ??
        [];

    // Count frequencies
    final Map<String, int> frequencyMap = {};
    for (final item in items) {
      frequencyMap[item ?? ''] = (frequencyMap[item] ?? 0) + 1;
    }

    // Sort keys by frequency descending
    final List<String> sortedKeys = frequencyMap.keys.toList()
      ..sort(
        (final a, final b) =>
            frequencyMap[b]?.compareTo(frequencyMap[a] ?? 0) ?? 0,
      );

    // Convert to set (preserves order of sorted list)
    final Set<String> sortedByFrequency = LinkedHashSet.from(sortedKeys);

    final messageReactionSet = sortedByFrequency;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: (messageReactionSet.length) > 3 ? 4 : 0,
      ),
      child: InkWell(
        onTapDown: (final onTapDown) async {
          if (widget.isPopupMessage) {
            return;
          }
          ChatBody.completer = Completer<ChatGetUserSeen?>();
          widget.onShowMenuPopup(isShowing: true, message: widget.message);
          _onShowActionMenu();
          await ChatBody.completer.future.timeout(const Duration(seconds: 3));
          if (ChatBody.completer.isCompleted) {
            userSeenData.value = await ChatBody.completer.future;
          }
        },
        child: Text.rich(
          textScaler: TextScaler.noScaling,
          TextSpan(
            children: messageReactionSet.mapIndexed((final index, final icon) {
              final count =
                  message?.reactions
                      .where((final e) => icon == e?.icon)
                      .length ??
                  0;
              final userList =
                  message?.reactions
                      .where((final e) => e?.icon == icon)
                      .toList() ??
                  [];
              // if (index >
              //     (MediaQuery.sizeOf(context).width < 420 ? 3 : 5)) {
              //   return const WidgetSpan(child: SizedBox.shrink());
              // }
              return WidgetSpan(
                child: Container(
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(60),
                    ),
                    color: (reactedIcon == icon)
                        ? Theme.of(context).primaryColor.withValues(alpha: 0.4)
                        : ((widget.customerId ==
                                      message?.createdByInfo?.username)
                                  ? const Color(0xffBBEAB3)
                                  : const Color(0xffEBF6E5))
                              .withValues(alpha: isSticker ? 0.5 : 1),
                  ),
                  margin: const EdgeInsets.only(right: 2, bottom: 2),
                  padding: const EdgeInsets.only(
                    left: 8,
                    right: 8,
                    top: 2.5,
                    bottom: 2.5,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        icon,
                        textScaler: TextScaler.noScaling,
                        style: const TextStyle(fontSize: 19),
                      ),
                      if (count > 3)
                        Text(
                          count.toString() + Strings.space,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontSize: 10,
                              ),
                          textScaler: TextScaler.noScaling,
                        ),
                      if (count < 4)
                        FlutterImageStack.widgets(
                          itemBorderWidth: 1,
                          itemBorderColor: Theme.of(
                            context,
                          ).colorScheme.surface,
                          itemRadius: 22,
                          backgroundColor: Colors.transparent,
                          totalCount: userList.length,
                          children: userList
                              .map(
                                (final react) => EzCachedNetworkImage(
                                  imageUrl: react?.avatar,
                                  width: 20,
                                  height: 20,
                                  errorWidget: RandomAvatarWidget(
                                    name: react?.name,
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Future<void> _makeCall() async {
    final canCall = (await EZCache.shared.configurations)?.canCall ?? true;
    if (!canCall) {
      if (mounted) {
        Alert.showAlert(AlertParams(context, context.l10n.callIsBlocked));
      }
      return;
    }
    final opposite = widget.membersInfo.firstWhereOrNull(
      (final e) => e?.username != EZCache.shared.getUserProfile()?.employeeId,
    );

    if (mounted) {
      LoadingDialog.instance
          .show(context)
          .then(
            (_) => Future.delayed(const Duration(seconds: 2), () {
              if (LoadingDialog.isShowing) {
                final context = getIt<AppRouter>().navigatorKey.currentContext;
                if (context != null) {
                  // need to use currentContext
                  // ignore: use_build_context_synchronously
                  LoadingDialog.instance.hide(context);
                }
              }
            }),
          );
    }
    PlatformChannelHelper.onMakeCallNotifier(
      to: opposite?.username,
      toName: opposite?.name,
      avatarUrl: opposite?.avatar,
    );
  }

  String _getDurationCall(final int duration) {
    if (duration == 0) {
      return '';
    }
    if (duration < 60) {
      return '$duration ${context.l10n.second}';
    } else {
      final minutes = duration ~/ 60;
      final seconds = duration % 60;
      return '$minutes ${context.l10n.minute} $seconds ${context.l10n.second}';
    }
  }

  String _getTimeString({final bool showTimeDetail = false}) {
    try {
      String time = '';
      if (widget.message?.createdAt != null) {
        final date = DateTime.parse(widget.message?.createdAt ?? '').toLocal();
        time = showTimeDetail
            ? DateFormat('dd/MM/yyyy HH:mm').format(date)
            : DateFormat('HH:mm').format(date);
      }

      return time;
    } catch (_) {
      return '';
    }
  }

  FileExtensionType getFileExtensionByMimeType(
    final ChatItemsAttachment? item,
  ) {
    final fileExtension = FileExtensionType.values.firstWhereOrNull(
      (final element) =>
          element.name == item?.originalname?.split('.').lastOrNull,
    );
    if (fileExtension == null) {
      return (item?.originalname != null)
          ? getFileExtension(item?.originalname ?? '')
          : getFileExtension(item?.link ?? '');
    }
    return fileExtension;
  }

  Widget popupItemWrapper({
    required final Widget child,
    final VoidCallback? onTap,
  }) {
    return Padding(
      padding: EdgeInsets.only(right: isSender ? 10 : 0),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: IntrinsicWidth(child: child),
      ),
    );
  }
}
