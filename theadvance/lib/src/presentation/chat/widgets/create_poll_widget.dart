// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/params/chat_poll_request_params.dart';
import '../../../core/utils/nd_utils.dart';

class CreatePollWidget extends StatefulWidget {
  const CreatePollWidget({super.key, required this.onCreated});

  final void Function(ChatPollRequestParams) onCreated;

  @override
  State<CreatePollWidget> createState() => _CreatePollWidgetState();
}

class _CreatePollWidgetState extends State<CreatePollWidget> {
  TextEditingController questionController = TextEditingController();
  List<TextEditingController> optionalControllerList = [
    TextEditingController(),
    TextEditingController(),
  ];
  List<Widget> moreOptionalList = [];
  bool anonymous = false;
  bool multipleChoose = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      moreOptionalList = [
        _buildOptionalField(optionalControllerList[0]),
        _buildOptionalField(optionalControllerList[1]),
      ];
    });
  }

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: FocusScope.of(context).unfocus,
      child: Container(
        height: MediaQuery.sizeOf(context).height * 0.75,
        padding: MediaQuery.of(context).viewInsets,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 8),
              ..._buildQuestionField(),
              const SizedBox(height: 16),
              ..._buildListOptional(),
              const SizedBox(height: 4),
              Text(
                context.l10n.canCreate8Optional,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).hintColor,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Theme.of(context).dividerColor),
                  ),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            context.l10n.anonymousVoting,
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ),
                        Switch.adaptive(
                          value: anonymous,
                          activeThumbColor: Theme.of(context).primaryColor,
                          activeTrackColor: Utils.isIOS
                              ? Theme.of(context).primaryColor
                              : Theme.of(
                                  context,
                                ).primaryColor.withValues(alpha: 0.3),
                          inactiveTrackColor: Theme.of(context).dividerColor,
                          trackOutlineColor: WidgetStateProperty.all(
                            Colors.transparent,
                          ),
                          onChanged: (final bool value) {
                            anonymous = value;
                            setState(() {});
                          },
                        ),
                      ],
                    ),
                    const Divider(),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            context.l10n.multipleAnswers,
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ),
                        Switch.adaptive(
                          value: multipleChoose,
                          activeThumbColor: Theme.of(context).primaryColor,
                          activeTrackColor: Utils.isIOS
                              ? Theme.of(context).primaryColor
                              : Theme.of(
                                  context,
                                ).primaryColor.withValues(alpha: 0.3),
                          inactiveTrackColor: Theme.of(context).dividerColor,
                          trackOutlineColor: WidgetStateProperty.all(
                            Colors.transparent,
                          ),
                          onChanged: (final bool value) {
                            multipleChoose = value;
                            setState(() {});
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Row _buildHeader() {
    return Row(
      children: <Widget>[
        IconButton(
          onPressed: Navigator.of(context).pop,
          icon: Icon(Icons.close_outlined, color: Theme.of(context).hintColor),
        ),
        Expanded(
          child: Text(
            context.l10n.createPoll,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontSize: 18),
          ),
        ),
        IconButton(
          onPressed: () => widget.onCreated(
            ChatPollRequestParams(
              title: questionController.text,
              anonymous: anonymous,
              multipleResponse: multipleChoose,
              options: optionalControllerList
                  .map(
                    (final controller) =>
                        OptionRequestParams(text: controller.text),
                  )
                  .toList(),
            ),
          ),
          icon: EZResources.image(ImageParams(name: AppIcons.icSendOff)),
        ),
      ],
    );
  }

  TextField _buildOptionalField(final TextEditingController controller) {
    return TextField(
      controller: controller,
      onChanged: (final _) {},
      decoration: InputDecoration(
        filled: true,
        fillColor: Theme.of(context).colorScheme.surface,
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Theme.of(context).primaryColor),
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Theme.of(context).dividerColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        hintStyle: Theme.of(
          context,
        ).textTheme.bodyLarge?.copyWith(color: Theme.of(context).hintColor),
        hintText:
            context.l10n.input +
            Strings.space +
            context.l10n.optional.toLowerCase(),
        suffixIcon: IconButton(
          onPressed: () {
            final index = optionalControllerList.indexOf(controller);
            moreOptionalList.removeAt(index);
            optionalControllerList.removeAt(index);
            setState(() {});
          },
          icon: Icon(
            Icons.remove_circle_outline,
            color: Theme.of(context).hintColor,
          ),
        ),
      ),
    );
  }

  List<Widget> _buildQuestionField() {
    return [
      Text(
        context.l10n.questionNumber,
        style: Theme.of(context).textTheme.labelLarge,
      ),
      const SizedBox(height: 4),
      TextField(
        controller: questionController,
        onChanged: (final _) {},
        decoration: InputDecoration(
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Theme.of(context).primaryColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          hintStyle: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: Theme.of(context).hintColor),
          hintText:
              context.l10n.input +
              Strings.space +
              context.l10n.questionNumber.toLowerCase(),
        ),
      ),
    ];
  }

  List<Widget> _buildListOptional() {
    return [
      Text(
        context.l10n.optional,
        style: Theme.of(context).textTheme.labelLarge,
      ),
      const SizedBox(height: 4),
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: Theme.of(context).dividerColor),
          ),
          color: Theme.of(context).colorScheme.surface,
        ),
        child: Column(
          children: <Widget>[
            ...moreOptionalList,
            if (moreOptionalList.length < 8)
              IconButton(
                onPressed: () {
                  optionalControllerList.add(TextEditingController());
                  moreOptionalList.add(
                    _buildOptionalField(
                      optionalControllerList[optionalControllerList.length - 1],
                    ),
                  );

                  setState(() {});
                },
                icon: Row(
                  children: <Widget>[
                    Icon(
                      Icons.add_circle_outline_sharp,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      context.l10n.addOptional,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              )
            else
              const SizedBox(height: 8),
          ],
        ),
      ),
    ];
  }
}
