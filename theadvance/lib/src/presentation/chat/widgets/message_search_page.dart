// Dart imports:
import 'dart:async';
import 'dart:math';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/params/request_params.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../core/utils/time_elapsed.dart';
import '../../../domain/entities/entities.dart';
import '../../settings/fonts/fonts_bloc.dart';
import '../../widgets/chat_search_bar.dart';
import '../../widgets/widgets.dart';
import '../bloc/bloc.dart';

class MessageSearchPage extends StatefulWidget {
  const MessageSearchPage({
    super.key,
    this.onCancel,
    this.onTapMessage,
    required this.conversationId,
    required this.membersInfo,
    this.onSearchByDate,
  });

  final VoidCallback? onCancel;
  final Function(ChatItems?)? onTapMessage;
  final String? conversationId;
  final List<ChatListItemsMembersInfo?> membersInfo;
  final Function(String)? onSearchByDate;

  @override
  State<MessageSearchPage> createState() => _MessageSearchPageState();
}

class _MessageSearchPageState extends State<MessageSearchPage> {
  final controller = TextEditingController();
  int searchPage = 1;
  String? createdAt;
  String? createdBy;
  Timer? timer;
  final PagingController<int, ChatItems?> pagingController = PagingController(
    firstPageKey: 1,
  );

  final FocusNode focusNode = FocusNode();
  bool isRemove = false;
  bool isSearchUser = false;
  int totalSearchMessages = 0;

  List<ChatListItemsMembersInfo?> userList = [];
  @override
  void initState() {
    super.initState();
    pagingController.addPageRequestListener((final pageKey) {
      searchPage = pageKey;
      context.read<ChatBloc>().add(
        ChatSearched(
          ChatSearchRequestParams(
            conversationId: widget.conversationId,
            page: searchPage,
            search: controller.text,
            createdAt: createdAt,
            createdBy: createdBy,
          ),
        ),
      );
    });
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<ChatBloc, ChatState>(
      listener: (final context, final state) {
        if (state.status == ChatStatus.searchSuccess) {
          totalSearchMessages =
              Utils.getData<ChatSearch>(state.data)?.total ?? 0;
          final newItems = Utils.getData<ChatSearch>(state.data)?.items ?? [];

          if (newItems.length < 10) {
            pagingController.appendLastPage(newItems);
          } else {
            pagingController.appendPage(newItems, searchPage + 1);
          }
        }
      },
      builder: (final context, final state) {
        return SafeArea(
          bottom: false,
          child: Scaffold(
            backgroundColor: Colors.transparent,
            resizeToAvoidBottomInset: false,
            body: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(left: 14, right: 6),
                  color: const Color(0xffF3F8F3),
                  child: Row(
                    children: [
                      Expanded(
                        child: KeyboardListener(
                          focusNode: focusNode,
                          onKeyEvent: (final event) {
                            if (event.logicalKey ==
                                    LogicalKeyboardKey.backspace &&
                                isRemove) {
                              if (createdBy == null) {
                                isSearchUser = false;
                              } else {
                                createdBy = null;
                                Future.delayed(Durations.short2, () {
                                  isSearchUser = true;
                                  setState(() {});
                                });
                              }
                              userList = widget.membersInfo;
                              pagingController.itemList = [];
                            }
                            if (controller.text.isEmpty) {
                              isRemove = true;
                            }
                            setState(() {});
                          },
                          child: ChatSearchBar(
                            center: false,
                            controller: controller,
                            autofocus: true,
                            suffixIcon:
                                controller.text.isNotEmpty ||
                                    createdBy != null ||
                                    isSearchUser
                                ? GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      pagingController.itemList = [];
                                      userList = widget.membersInfo;
                                      if (controller.text.isNotEmpty) {
                                        controller.clear();
                                        setState(() {});
                                        return;
                                      }
                                      if (createdBy != null) {
                                        createdBy = null;
                                        isSearchUser = true;
                                        setState(() {});
                                        return;
                                      }
                                      if (isSearchUser) {
                                        isSearchUser = false;
                                        setState(() {});
                                        return;
                                      }
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      margin: const EdgeInsets.only(right: 8),
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Color(0xff808782),
                                      ),
                                      child: const Icon(
                                        Icons.close_sharp,
                                        size: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : null,
                            prefix: _buildPrefix(context),
                            onChanged: (final key) {
                              isRemove = false;

                              if (isSearchUser) {
                                if (key.isEmpty) {
                                  userList = widget.membersInfo;
                                }
                                userList = widget.membersInfo
                                    .where(
                                      (final e) =>
                                          (e?.name?.toLowerCase().contains(
                                                key.toLowerCase(),
                                              ) ??
                                              false) ||
                                          (e?.username?.toLowerCase().contains(
                                                key.toLowerCase(),
                                              ) ??
                                              false),
                                    )
                                    .toList();
                                setState(() {});
                                return;
                              }

                              timer?.cancel();
                              // if (key.length < 2) {
                              //   pagingController.itemList = [];
                              //   return;
                              // }
                              timer = Timer(
                                const Duration(milliseconds: 800),
                                () async {
                                  setState(() {});
                                  pagingController.refresh();
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Text(
                          context.l10n.cancel,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                fontSize: 15,
                                color: Theme.of(context).primaryColor,
                              ),
                        ),
                        onPressed: () {
                          widget.onCancel?.call();
                        },
                      ),
                    ],
                  ),
                ),
                Container(
                  height: min(
                    isSearchUser
                        ? (userList.length * 45)
                        : ((pagingController.itemList?.length ?? 0) == 0
                              ? 200
                              : (pagingController.itemList?.length ?? 0) * 75),
                    MediaQuery.sizeOf(context).height - 180,
                  ),
                  color:
                      (controller.text.isEmpty &&
                          createdBy == null &&
                          !isSearchUser)
                      ? Colors.transparent
                      : Colors.white,
                  child: isSearchUser
                      ? ListView.separated(
                          separatorBuilder: (final _, final index) =>
                              const SizedBox(height: 12),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 11,
                            vertical: 5,
                          ),
                          itemCount: userList.length,
                          itemBuilder: (final _, final index) {
                            return GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                isSearchUser = false;
                                createdBy = userList[index]?.username;
                                controller.text = '';
                                isRemove = true;
                                setState(() {});
                                pagingController.refresh();
                              },
                              child: Row(
                                children: <Widget>[
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(30),
                                    child: EzCachedNetworkImage(
                                      imageUrl: userList[index]?.avatar ?? '',
                                      width: 30,
                                      height: 30,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                          userList[index]?.name ?? '',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.labelLarge,
                                        ),
                                        if (index != userList.length - 1) ...[
                                          const SizedBox(height: 12),
                                          const Divider(),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        )
                      : controller.text.isNotEmpty || createdBy != null
                      ? _buildSearchedMessageList()
                      : null,
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 14,
                    vertical: 10,
                  ),
                  width: double.maxFinite,
                  decoration: const BoxDecoration(
                    color: Color(
                      0xFFF3F3F3,
                    ) /* Gray-Border-Background-background-header */,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );
                          if (date != null) {
                            createdAt = DateFormat('yyyy-MM-dd').format(date);
                            if (createdAt != null) {
                              widget.onCancel?.call();
                              widget.onSearchByDate?.call(createdAt!);
                            }
                          }
                        },
                        icon: EZResources.image(
                          ImageParams(
                            name: AppIcons.icSearchByDate,
                            color: Theme.of(context).primaryColor,
                            size: const ImageSize.square(24),
                          ),
                        ),
                      ),
                      if (createdBy == null)
                        IconButton(
                          onPressed: () {
                            controller.clear();
                            isSearchUser = true;
                            userList = widget.membersInfo;
                            setState(() {});
                          },
                          icon: EZResources.image(
                            ImageParams(
                              name: AppIcons.icSearchByUser,
                              color: Theme.of(context).primaryColor,
                              size: const ImageSize.square(24),
                            ),
                          ),
                        ),
                      if (createdBy != null)
                        Text(
                          '$totalSearchMessages ${context.l10n.message}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Theme.of(context).primaryColor),
                        ),
                      // const Spacer(),
                      // if (createdBy != null)
                      //   Text(
                      //     context.l10n.viewAll,
                      //     style:
                      // Theme.of(context).textTheme.bodyMedium?.copyWith(
                      //           color: Theme.of(context).primaryColor,
                      //         ),
                      //   ),
                      // const SizedBox(width: 8),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  BaseInfiniteListView<ChatItems> _buildSearchedMessageList() {
    return BaseInfiniteListView(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      itemBuilder: (final context, final item, final index) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            widget.onCancel?.call();
            FocusScope.of(context).unfocus();
            widget.onTapMessage?.call(item);
          },
          child: Row(
            children: <Widget>[
              ClipRRect(
                borderRadius: BorderRadius.circular(64),
                child: Utils.isURL(item?.createdByInfo?.avatar ?? '')
                    ? EzCachedNetworkImage(
                        imageUrl: item?.createdByInfo?.avatar,
                        width: 56,
                        height: 56,
                      )
                    : Container(
                        width: 56,
                        height: 56,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              Utils.randomColor(
                                item?.id ?? '',
                              ).withValues(alpha: 0.5),
                              Utils.randomColor(item?.id ?? ''),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Text(
                            Utils.defaultOnEmpty(
                                  item?.createdByInfo?.name,
                                ).isEmpty
                                ? Strings.appName[0].toUpperCase()
                                : Utils.defaultOnEmpty(
                                    item?.createdByInfo?.name,
                                  ).firstOrEmpty().toUpperCase(),
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.surface,
                                ),
                          ),
                        ),
                      ),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.fromLTRB(8, 8, 6, 14),
                  decoration: BoxDecoration(
                    border:
                        (index != (pagingController.itemList?.length ?? 0) - 1)
                        ? Border(
                            bottom: BorderSide(
                              color: Theme.of(context).dividerColor,
                            ),
                          )
                        : null,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Stack(
                              children: <Widget>[
                                Container(
                                  padding: const EdgeInsets.only(right: 16),
                                  child: Text(
                                    item?.createdByInfo?.name ?? '',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.titleSmall,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            LastMessageWidget(
                              textScale: context.watch<FontsBloc>().textScale,
                              lastMessageInfo: item,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          Text(
                            TimeElapsed.fromDateStr(item?.createdAt ?? ''),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Theme.of(context).hintColor,
                                  fontSize: 13,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 8),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
      pagingController: pagingController,
    );
  }

  Widget? _buildPrefix(final BuildContext context) {
    if (!isSearchUser && createdAt == null && createdBy == null) {
      return null;
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        if (isSearchUser && createdBy == null)
          Text(
            context.l10n.from.toLowerCase() + Strings.colonSpace,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        if (createdBy != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            margin: const EdgeInsets.only(right: 8),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              color: const Color(0xFF808080) /* color-black-black-500 */,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              spacing: 1,
              children: [
                EZResources.image(
                  ImageParams(
                    name: AppIcons.icUser,
                    size: const ImageSize.square(20),
                    color: Colors.white,
                  ),
                ),
                Text(
                  userList
                          .firstWhereOrNull(
                            (final e) => e?.username == createdBy,
                          )
                          ?.name ??
                      '',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
