// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:app_ui/app_ui.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_permission/ez_permission.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' show PreviewData;
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_quill_delta_from_html/parser/html_to_delta.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:http_parser/http_parser.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mime_type/mime_type.dart';
import 'package:pasteboard/pasteboard.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:uuid/uuid.dart';
import 'package:video_compress/video_compress.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

// Project imports:
import '../../../../app.dart';
import '../../../config/enums/attachment_option.dart';
import '../../../config/enums/chat_action.dart';
import '../../../config/enums/conversation_type.dart';
import '../../../config/enums/file_extension_type.dart';
import '../../../config/enums/group_chat_action.dart';
import '../../../config/enums/user_chat_role.dart';
import '../../../config/enums/user_chat_rule.dart';
import '../../../core/enums/socket_event.dart';
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../core/params/chat_get_by_id_request_params.dart';
import '../../../core/params/request_params.dart' hide TaskInfo;
import '../../../core/routes/app_router.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/mappers.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../data/models/nd_models.dart';
import '../../../domain/entities/chat_upload_file.dart' as entity;
import '../../../domain/entities/entities.dart' hide ChatUploadFile;
import '../../../injector/injector.dart';
import '../../../services/nd_downloader/nd_downloader.dart';
import '../../../services/nd_socket/socket.dart';
import '../../_blocs/general_bloc/general_bloc.dart';
import '../../chat_list/bloc/bloc.dart';
import '../../settings/fonts/fonts_bloc.dart';
import '../../widgets/chat_search_bar.dart';
import '../../widgets/widgets.dart';
import '../bloc/chat_bloc.dart';
import 'animated_sticker_widget.dart';
import 'create_poll_widget.dart';
import 'emoji_picker.dart';
import 'empty_message.dart';
import 'forward_user_tile.dart';
import 'gallery_chat.dart';
import 'input_area.dart';
import 'lifecycle_event_handler.dart';
import 'message_search_page.dart';
import 'message_tile.dart';
import 'recorder_widget.dart';
import 'selecting_file_widget.dart';

class ChatBody extends StatefulWidget {
  const ChatBody({
    final Key? key,
    required this.conversationId,
    this.createdAt,
    this.forwardMessage,
    this.defaultText,
  }) : super(key: key);
  final String? conversationId;
  final String? createdAt;
  final String? defaultText;
  final ChatItems? forwardMessage;
  static List<String?> votingList = [];
  static Completer<ChatGetUserSeen?> completer = Completer<ChatGetUserSeen?>();

  @override
  State<ChatBody> createState() => _ChatBodyState();
}

class _ChatBodyState extends State<ChatBody> {
  Chat? data;
  TaskInfo? taskInfo;
  ValueNotifier<bool> emojiShowing = ValueNotifier<bool>(false);
  late QuillController messageController;
  TextEditingController searchUserController = TextEditingController();
  FocusNode messageFocusNode = FocusNode();
  FocusNode searchUserFocusNode = FocusNode();
  List<PickedFileBundle> pickedFileBundles = [];
  final PagingController<int, ChatListItems?> _conversationPagingController =
      PagingController(firstPageKey: 1);
  final PagingController<int, ChatItems?> _pagingController = PagingController(
    firstPageKey: 1,
  );
  final PagingController<int, ChatItems?> _pagingControllerPrevious =
      PagingController(firstPageKey: 1);
  int conversationCurrentPage = 1;
  int currentPage = 1;
  int currentPagePrevious = 1;
  String? createdAtBefore;
  String? createdAtAfter;
  ValueNotifier<bool> isAtTheBottomList = ValueNotifier<bool>(true);
  double previousPixels = 0;
  UserModel? user;
  List<AssetEntity> selectedAssetList = [];
  ValueNotifier<int> unreadCount = ValueNotifier<int>(0);
  ValueNotifier<int> mentionCount = ValueNotifier<int>(0);
  List<String?> seenMessageIdList = [];
  bool scrollToLastSeen = false;
  Timer? timer;
  Timer? timerShowDate;
  Timer? timerSaveLastSeen;
  String? lastSeenMessageId;
  String? lastSeenMessageTime;
  ChatListItems? conversation;
  ChatItems? replyMessage;
  ChatItems? editMessage;
  ChatItems? forwardMessage;
  String? searchMessageId;
  String? searchMessageCreatedAt;
  String? searchTag = '';
  List<ChatListItemsMembersInfo?> tagUserList = [];
  final AutoScrollController autoScrollController = AutoScrollController();
  bool initPreviousList = false;
  ChatItems? latestMessage;
  TextEditingController searchBarController = TextEditingController();
  List<ChatListItems?> forwardConversations = [];
  String? lastUpdate;
  ValueNotifier<double> blurValue = ValueNotifier<double>(0);
  Uint8List? imageClipboard;
  Map<String, PreviewData> previewDataList = {};
  List<ChatListSearchItems?> userSearchList = [];
  Completer<List<ChatListSearchItems?>> completer = Completer();
  bool showMessageForwardSuccess = false;
  bool recording = false;
  String recordPath = '';
  List<String?> needUpdateConversationId = [];
  List<ChatItems?> pinMessageList = [];
  int currentIndexPinMessage = 0;
  bool showPinList = false;
  int readMessageCount = 0;
  int? recordDuration;
  List<ImageProperty?> imageUrlList = [];
  Widget? audioPlayer;
  List<String?> listFailedMessageId = [];
  String? initForwardId;
  final PagingController<int, ChatGetUserSeenDocs?> _userSeenPagingController =
      PagingController(firstPageKey: 1);
  int userSeenCurrentPage = 1;
  ChatItems? messageGetUserSeen;
  bool isMultiSelect = false;
  List<ChatItems?> selectedMessageList = [];
  ChatGetUserSeen? userSeenData;
  ValueNotifier<int> selectedMessagesCount = ValueNotifier<int>(0);
  ValueNotifier<bool> isShowDate = ValueNotifier<bool>(false);
  ChatItems? currentMessageViewByDate;
  bool isShowSearchView = false;
  List<ChatGetUserStickerItems?> stickerData = [];
  List<ChatGetUserStickerItemsStickers> stickersRecent = [];
  final ValueNotifier<bool> vIsShow = ValueNotifier(false);
  List<ChatItems?> newMessages = [];
  final ValueNotifier<bool> isExpandedSticker = ValueNotifier(false);
  final String regExpEmp = '(@)[0-9]';
  List<String?> userRules = [];
  double _lastOffset = 0;
  bool isScrollDown = false;
  double keyboardHeight = 0;
  double safeBottom = 0;
  @override
  void initState() {
    super.initState();
    const initialJson = r'''
    [
      {"insert": "\n"}
    ]
    ''';
    _socketListen();

    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      final stickers = await EZCache.shared.getStickerRecent();
      stickersRecent = getIt<Mapper>().convertList(stickers);
    });

    searchBarController = CustomTextFieldController(
      text: Strings.empty,
      rebuild: () {},
    );
    messageController = QuillController(
      document: Document.fromJson(jsonDecode(initialJson)),
      selection: const TextSelection.collapsed(offset: 0),
      onReplaceText: (final index, final e, final text) {
        if (text is String && text.contains(' ')) {
          final currentStyle = messageController.getSelectionStyle();
          final isCurrentlyBold = currentStyle.attributes.containsKey('bold');
          if (isCurrentlyBold) {
            messageController.formatSelection(
              Attribute.clone(Attribute.bold, null),
            );
          }
          final isItalic = currentStyle.attributes.containsKey('italic');
          if (isItalic) {
            messageController.formatSelection(
              Attribute.clone(Attribute.italic, null),
            );
          }
          final isUnder = currentStyle.attributes.containsKey('underline');

          if (isUnder) {
            messageController.formatSelection(
              Attribute.clone(Attribute.underline, null),
            );
          }
        }
        return true;
      },
    );

    if (widget.conversationId == audioUrlListId ||
        (audioPlayerGlobal.state == PlayerState.stopped &&
            currentAudioUrl.value.isEmpty)) {
      for (final e in audioPlayerList) {
        if (e.audioPlayer.playerId != audioPlayerGlobal.playerId) {
          unawaited(e.audioPlayer.dispose());
        }
      }
      audioPlayerList.clear();
      audioUrlList.clear();
    }

    audioUrlListId = widget.conversationId ?? '';

    initForwardId = widget.forwardMessage?.id;
    user = EZCache.shared.getUserProfile();
    searchMessageCreatedAt = widget.createdAt;
    forwardMessage = widget.forwardMessage;

    final textDraff =
        widget.defaultText ??
        EZCache.shared.getDraftMessage(widget.conversationId ?? '');
    needUpdateConversationId.add(widget.conversationId);

    messageController.document.insert(0, textDraff);
    messageController.moveCursorToEnd();
    unawaited(getImageClipboard());
    messageFocusNode = FocusNode(
      onKeyEvent: (final node, final event) {
        final enterPressedWithShift =
            event is KeyDownEvent &&
            event.physicalKey == PhysicalKeyboardKey.enter &&
            HardwareKeyboard.instance.physicalKeysPressed.any(
              (final key) => <PhysicalKeyboardKey>{
                PhysicalKeyboardKey.shiftLeft,
                PhysicalKeyboardKey.shiftRight,
              }.contains(key),
            );

        if (enterPressedWithShift) {
          // messageController += '\n';
          return KeyEventResult.handled;
        } else {
          final enterPressedWithoutShift =
              event is KeyDownEvent &&
              event.physicalKey == PhysicalKeyboardKey.enter &&
              !HardwareKeyboard.instance.physicalKeysPressed.any(
                (final key) => <PhysicalKeyboardKey>{
                  PhysicalKeyboardKey.shiftLeft,
                  PhysicalKeyboardKey.shiftRight,
                }.contains(key),
              );

          if (enterPressedWithoutShift) {
            if (!RegExp(
              r'^\s*$',
            ).hasMatch(messageController.plainTextEditingValue.text.trim())) {
              unawaited(_onTapSend(context));
            }
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        }
      },
    );
    messageFocusNode.addListener(() {
      if (messageFocusNode.hasFocus) {
        emojiShowing.value = false;
        if (searchMessageCreatedAt != null) {
          Future.delayed(Durations.medium4, () {
            if (autoScrollController.hasClients) {
              autoScrollController.jumpTo(
                autoScrollController.position.pixels +
                    keyboardHeight -
                    safeBottom,
              );
            }
          });
        }
      }
    });

    autoScrollController.addListener(() {
      final double currentOffset = autoScrollController.offset;

      if (currentOffset > _lastOffset) {
        if (searchMessageCreatedAt == null) {
          isScrollDown = false;
        } else {
          isScrollDown = true;
        }
      } else if (currentOffset < _lastOffset) {
        if (searchMessageCreatedAt == null) {
          isScrollDown = true;
        } else {
          isScrollDown = false;
        }
      }

      _lastOffset = currentOffset;
      if (autoScrollController.position.pixels < 100) {
        if (newMessages.isNotEmpty) {
          _pagingController.itemList?.insert(0, newMessages.firstOrNull);
          newMessages.removeAt(0);
          setState(() {});
        }
      }

      if (searchMessageCreatedAt != null) {
        if (autoScrollController.position.maxScrollExtent !=
            autoScrollController.position.pixels) {
          isAtTheBottomList.value = false;
        } else {
          isAtTheBottomList.value = true;
        }
      } else {
        if (autoScrollController.position.pixels < 20) {
          isAtTheBottomList.value = true;
        } else {
          isAtTheBottomList.value = false;
        }
      }
      autoScrollController.cancelAllHighlights();

      timerShowDate?.cancel();
      isShowDate.value = true;
      timerShowDate = Timer(const Duration(seconds: 2), () {
        isShowDate.value = false;
      });
    });

    messageController.addListener(() {
      final text = messageController.plainTextEditingValue.text;
      vIsShow.value = text.length > 1;
      timer?.cancel();

      timer = Timer(const Duration(milliseconds: 500), () {
        EZCache.shared.saveDraftMessage(
          widget.conversationId ?? '',
          messageController.plainTextEditingValue.text.trim(),
        );
      });
    });

    _conversationPagingController.addPageRequestListener((final pageKey) {
      conversationCurrentPage = pageKey;
      if (conversationCurrentPage == 1) {
        lastUpdate = '';
      }

      context.read<ChatListBloc>().add(
        ChatListStarted(
          ChatListRequestParams(
            type: ConversationType.internal.name,
            updatedAt: lastUpdate,
          ),
        ),
      );
    });
    _userSeenPagingController.addPageRequestListener((final pageKey) {
      userSeenCurrentPage = pageKey;
      context.read<ChatBloc>().add(
        ChatGotUserSeen(
          ChatGetUserSeenRequestParams(
            conversationId: messageGetUserSeen?.conversationId,
            messageCreatedAt: messageGetUserSeen?.createdAt,
            page: userSeenCurrentPage,
          ),
        ),
      );
    });
    if (Platform.isIOS) {
      WidgetsBinding.instance.addObserver(
        LifecycleEventHandler(
          resumeCallBack: () async {
            getImageClipboard();
          },
        ),
      );
    }
    VisibilityDetectorController.instance.updateInterval = Duration.zero;
    if (Utils.isHtml(widget.defaultText ?? '')) {
      final delta = HtmlToDelta().convert(widget.defaultText ?? '');
      final document = Document.fromDelta(delta);
      messageController.document = document;
      messageController.moveCursorToEnd();
    }
  }

  Future<void> getImageClipboard() async {
    if (Platform.isIOS) {
      imageClipboard = await Pasteboard.image;
    }
  }

  @override
  void dispose() {
    SocketService().clearListeners();
    messageFocusNode.dispose();
    messageController.dispose();
    emojiShowing.dispose();
    vIsShow.dispose();
    selectedMessagesCount.dispose();
    isAtTheBottomList.dispose();
    isShowDate.dispose();
    unreadCount.dispose();
    mentionCount.dispose();
    blurValue.dispose();
    isExpandedSticker.dispose();
    searchUserController.dispose();
    searchUserFocusNode.dispose();
    _conversationPagingController.dispose();
    _userSeenPagingController.dispose();
    _pagingController.dispose();
    _pagingControllerPrevious.dispose();
    timer?.cancel();
    timerShowDate?.cancel();
    timerSaveLastSeen?.cancel();
    autoScrollController.dispose();
    searchBarController.dispose();
    // unawaited(audioPlayerGlobal.dispose());
    // currentAudioUrl.value = '';
    // for (final e in audioPlayerList) {
    //   if (e.playerId != audioPlayerGlobal.playerId) {
    //     unawaited(e.dispose());
    //   }
    // }
    // audioPlayerList.clear();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocListener<GeneralBloc, GeneralState>(
      listener: (final context, final state) {
        if (state is GeneralSendImageMessageSuccess) {
          if (state.bytes != null) {
            _onPasteImage(state.bytes!, context, isCompress: false);
          }
        }
      },
      child: BlocListener<ChatListBloc, ChatListState>(
        listener: (final context, final state) {
          if (state.status == ChatListStatus.pinConversationSuccess) {
            final ChatListPinConversation data = Utils.getData(state.data);
            if (data.items.isNotEmpty) {
              _conversationPagingController.itemList = [];
              _conversationPagingController.itemList?.insertAll(0, data.items);
            }
          }
          if (state.status == ChatListStatus.internalLoadSuccess) {
            final newItems = Utils.getData<ChatList>(state.data)?.items ?? [];

            if (newItems.length < 10) {
              _conversationPagingController.appendLastPage(newItems);
            } else {
              lastUpdate = newItems.last?.updatedAt;
              conversationCurrentPage++;
              _conversationPagingController.appendPage(
                newItems,
                conversationCurrentPage,
              );
            }
          }
          if (state.status == ChatListStatus.searchSuccess) {
            userSearchList =
                Utils.getData<ChatListSearch>(state.data)?.items ?? [];
            if (!completer.isCompleted) {
              completer.complete(userSearchList);
            }
          }
          if (state.status == ChatListStatus.markAsReadSuccess) {
            timer?.cancel();
            lastSeenMessageId = latestMessage?.id;
            lastSeenMessageTime = latestMessage?.createdAt;
            if (latestMessage?.id == lastSeenMessageId) {
              unreadCount.value = 0;
              unawaited(_scrollToEnd(controller: _pagingController));
            }
          }
        },
        child: BlocConsumer<ChatBloc, ChatState>(
          listener: (final context, final state) {
            if (state.status == ChatStatus.chatInitialized) {
              conversation = Utils.getData(state.data);
              context.read<ChatBloc>().add(
                ChatGotUserRules(
                  GroupChatDetailGetUserRulesRequestParams(
                    conversationId: widget.conversationId,
                    username: EZCache.shared.getUserProfile()?.employeeId,
                  ),
                ),
              );
              latestMessage = conversation?.lastMessageInfo;
              currentMessageViewByDate = latestMessage;
              lastSeenMessageId =
                  conversation?.conversationDetails?.lastSeenMessageId;
              lastSeenMessageTime =
                  conversation?.conversationDetails?.lastSeenAt;
              unreadCount.value = conversation?.unreadCount ?? 0;
              mentionCount.value =
                  conversation?.conversationDetails?.mentionMessages?.length ??
                  0;
              if (lastSeenMessageId == latestMessage?.id &&
                  unreadCount.value > 0) {
                unreadCount.value = 0;
                context.read<ChatListBloc>().add(
                  ChatListMarkAsRead(
                    ChatListMarkAsReadRequestParams([conversation?.id]),
                  ),
                );
              }
              _pagingController.addPageRequestListener((final pageKey) {
                if (pageKey == 1) {
                  newMessages.clear();
                }
                currentPage = pageKey;
                if (searchMessageCreatedAt == null) {
                  context.read<ChatBloc>().add(
                    ChatStarted(
                      ChatRequestParams(
                        conversationId: widget.conversationId,
                        createdAtBefore: createdAtBefore,
                      ),
                      isScrollDown: true,
                    ),
                  );
                  return;
                }
                if (currentPage == 1 && searchMessageCreatedAt != null) {
                  context.read<ChatBloc>().add(
                    ChatStarted(
                      ChatRequestParams(
                        conversationId: widget.conversationId,
                        createdAt: searchMessageCreatedAt,
                      ),
                      isScrollDown: true,
                    ),
                  );
                } else {
                  context.read<ChatBloc>().add(
                    ChatStarted(
                      ChatRequestParams(
                        conversationId: widget.conversationId,
                        createdAtAfter: currentPage != 1 ? createdAtAfter : '',
                      ),
                      isScrollDown: true,
                    ),
                  );
                }
              });
            }

            if (state.status == ChatStatus.failure) {
              unawaited(
                ApiErrorDialog.show(
                  ApiErrorParams(
                    context,
                    Utils.getData(state.data),
                    onPressOK: () {
                      setState(() {
                        isReconnecting = true;
                      });
                      Future.delayed(const Duration(seconds: 1), () {
                        isReconnecting = false;
                        setState(() {});
                        if (context.mounted) {
                          context.read<ChatBloc>().add(
                            ChatInitialized(
                              ChatGetByIdRequestParams(
                                id: widget.conversationId,
                              ),
                            ),
                          );
                          context.read<ChatBloc>().add(
                            ChatGetPinnedList(
                              ChatGetPinListRequestParams(
                                widget.conversationId,
                              ),
                            ),
                          );
                          context.read<ChatListBloc>().add(
                            ChatListPinnedConversation(
                              const ChatListPinConversationRequestParams(),
                            ),
                          );
                        }
                      });
                    },
                  ),
                ),
              );
            }

            if (state.status == ChatStatus.getConversationDetailSuccess) {
              final ChatListItems? newConversation = Utils.getData(state.data);
              _conversationPagingController.itemList?.removeWhere(
                (final e) => e?.id == newConversation?.id,
              );

              _conversationPagingController.itemList?.insert(
                0,
                newConversation,
              );

              forwardConversations.add(newConversation);
              if (mounted) {
                setState(() {});
              }
            }

            if (state.status == ChatStatus.success) {
              data = Utils.getData(state.data);

              final newItems = data?.items ?? [];
              createdAtBefore = newItems.firstOrNull?.createdAt;

              if (newItems.length < 10 &&
                  (searchMessageCreatedAt == null ||
                      currentPagePrevious != 1)) {
                _pagingControllerPrevious.appendLastPage(
                  newItems.reversed.toList(),
                );
              } else {
                currentPagePrevious++;
                _pagingControllerPrevious.appendPage(
                  newItems.reversed.toList(),
                  currentPagePrevious,
                );
              }
              for (final mess in newItems.reversed) {
                final attachment =
                    (mess?.attachment.reversed.toList() ?? []) +
                    (mess?.forward?.attachment.reversed.toList() ?? []);
                for (final media in attachment) {
                  if (getFileExtension(media?.originalname ?? '') ==
                      FileExtensionType.audio) {
                    audioUrlList.insert(
                      0,
                      AudioUrl(
                        conversationId: widget.conversationId,
                        attachment: media?..localId = const Uuid().v4(),
                      ),
                    );
                  }
                  if (getFileExtension(media?.originalname ?? '') ==
                      FileExtensionType.image) {
                    imageUrlList.insert(
                      0,
                      ImageProperty(
                        url: media?.link,
                        fileNameWithExtension: media?.originalname,
                      ),
                    );
                  }
                }
              }
            }
            if (state.status == ChatStatus.scrollDown) {
              // check first load and scroll to last seen message ( use 1 list )
              if (data == null &&
                  searchMessageCreatedAt == null &&
                  conversation?.conversationDetails?.lastSeenMessageId !=
                      conversation?.lastMessageInfo?.id &&
                  unreadCount.value > 0) {
                Future.delayed(Durations.short4, () {
                  _scrollToMessage(
                    controller: _pagingController,
                    messageId:
                        conversation?.conversationDetails?.lastSeenMessageId,
                    messageCreatedAt:
                        conversation?.conversationDetails?.lastSeenAt,
                  );
                });
              }

              data = Utils.getData(state.data);

              final newItems = data?.items ?? [];

              // normal flow (use 1 list)
              if (searchMessageCreatedAt == null) {
                createdAtBefore = newItems.firstOrNull?.createdAt;

                if (newItems.length < 10) {
                  _pagingController.appendLastPage(newItems.reversed.toList());
                } else {
                  currentPage++;
                  _pagingController.appendPage(
                    newItems.reversed.toList(),
                    currentPage,
                  );
                }
                for (final mess in newItems.reversed) {
                  final attachment =
                      (mess?.attachment.reversed.toList() ?? []) +
                      (mess?.forward?.attachment.reversed.toList() ?? []);
                  for (final media in attachment) {
                    if (getFileExtension(media?.originalname ?? '') ==
                        FileExtensionType.audio) {
                      audioUrlList.insert(
                        0,
                        AudioUrl(
                          conversationId: widget.conversationId,
                          attachment: media?..localId = const Uuid().v4(),
                        ),
                      );
                    }
                    if (getFileExtension(media?.originalname ?? '') ==
                        FileExtensionType.image) {
                      imageUrlList.insert(
                        0,
                        ImageProperty(
                          url: media?.link,
                          fileNameWithExtension: media?.originalname,
                        ),
                      );
                    }
                  }
                }
                return;
              }

              // flow with search (use 2 lists)
              createdAtAfter = newItems.lastOrNull?.createdAt;

              if (currentPage == 1) {
                createdAtBefore = newItems.firstOrNull?.createdAt;
                _pagingControllerPrevious.refresh();
                if (!initPreviousList) {
                  _pagingControllerPrevious.addPageRequestListener((
                    final pageKey,
                  ) {
                    currentPagePrevious = pageKey;
                    context.read<ChatBloc>().add(
                      ChatStarted(
                        ChatRequestParams(
                          conversationId: widget.conversationId,
                          createdAtBefore: createdAtBefore,
                        ),
                      ),
                    );
                  });
                  initPreviousList = true;
                }
              }

              if (newItems.length < 10 &&
                  (searchMessageCreatedAt == null || currentPage != 1)) {
                _pagingController.appendLastPage(newItems.toList());
                if (currentPage == 1) {
                  _scrollWhenFirstLoad();
                }
              } else {
                currentPage++;
                _pagingController.appendPage(newItems.toList(), currentPage);
                if (currentPage == 2) {
                  _scrollWhenFirstLoad();
                }
              }
              for (final mess in newItems.reversed) {
                final attachment =
                    (mess?.attachment.reversed.toList() ?? []) +
                    (mess?.forward?.attachment.reversed.toList() ?? []);
                for (final media in attachment) {
                  if (getFileExtension(media?.originalname ?? '') ==
                      FileExtensionType.audio) {
                    audioUrlList.insert(
                      0,
                      AudioUrl(
                        conversationId: widget.conversationId,
                        attachment: media?..localId = const Uuid().v4(),
                      ),
                    );
                  }
                  if (getFileExtension(media?.originalname ?? '') ==
                      FileExtensionType.image) {
                    imageUrlList.insert(
                      0,
                      ImageProperty(
                        url: media?.link,
                        fileNameWithExtension: media?.originalname,
                      ),
                    );
                  }
                }
              }
            }

            if (state is ChatGetConversationFailure) {
              showMessageForwardSuccess = false;
            }
            if (state.status == ChatStatus.newMessage) {
              final ChatItems? message = Utils.getData(state.data);
              _pagingController.itemList?.removeWhere(
                (final e) => e?.id == message?.id,
              );
              if (searchMessageCreatedAt == null) {
                if (autoScrollController.position.pixels < 100) {
                  _pagingController.itemList?.insert(0, message);
                } else {
                  newMessages.add(message);
                }
              } else {
                if (latestMessage?.id ==
                    _pagingController.itemList?.lastOrNull?.id) {
                  _pagingController.itemList?.add(message);
                }
              }
              unreadCount.value++;
              latestMessage = message;
              final attachment =
                  (message?.attachment ?? []) +
                  (message?.forward?.attachment ?? []);
              for (final media in attachment) {
                if (getFileExtension(media?.originalname ?? '') ==
                    FileExtensionType.audio) {
                  audioUrlList.add(
                    AudioUrl(
                      conversationId: widget.conversationId,
                      attachment: media?..localId = const Uuid().v4(),
                    ),
                  );
                }
                if (getFileExtension(media?.originalname ?? '') ==
                    FileExtensionType.image) {
                  imageUrlList.add(
                    ImageProperty(
                      url: media?.link,
                      fileNameWithExtension: media?.originalname,
                    ),
                  );
                }
              }
            }
            if (state is ChatTranscribeSuccess) {
              final ChatTranscribe? res = Utils.getData(state.data);

              final message = _pagingController.itemList?.firstWhereOrNull(
                (final element) => state.messageId == element?.id,
              );
              if (message != null) {
                message.attachment
                        .firstWhereOrNull(
                          (final e) => state.attachmentId == e?.attachmentId,
                        )
                        ?.speechToText
                        ?.text =
                    res?.text;
                return;
              }

              final messagePrevious = _pagingControllerPrevious.itemList
                  ?.firstWhereOrNull(
                    (final element) => state.messageId == element?.id,
                  );
              if (messagePrevious != null) {
                messagePrevious.attachment
                        .firstWhereOrNull(
                          (final e) => state.attachmentId == e?.attachmentId,
                        )
                        ?.speechToText
                        ?.text =
                    res?.text;
              }
            }
            if (state.status == ChatStatus.sendSuccess) {
              _handleWhenSendSuccess(state);
            }
            if (state.status == ChatStatus.conversationDetailsUpdateSuccess) {
              readMessageCount = 0;
              final lastSeenMessage =
                  Utils.getData<ChatConversationDetailsUpdate>(state.data);
              lastSeenMessageId = lastSeenMessage?.lastSeenMessageId;
              lastSeenMessageTime = lastSeenMessage?.lastSeenAt;
              if (latestMessage?.id == lastSeenMessageId) {
                unreadCount.value = 0;
                unawaited(_scrollToEnd(controller: _pagingController));
              }
            }
            if (state.status == ChatStatus.uploadFileFailure) {
              _pagingController.itemList
                      ?.firstWhereOrNull(
                        (final e) => e?.requestId == state.data?.toString(),
                      )
                      ?.sentFailed =
                  true;
              listFailedMessageId.add(state.data?.toString());
            }
            if (state is ChatSendFileSuccess) {
              int nPart = 1;
              final List<entity.ChatUploadFile?> uploadFileList = Utils.getData(
                state.data,
              );

              final message = _pagingController.itemList
                  ?.firstWhereOrNull(
                    (final e) => e?.requestId == state.requestId,
                  )
                  ?.content;
              if (uploadFileList.length > 10) {
                nPart = (uploadFileList.length / 10).ceil();
              }

              for (int i = 1; i <= nPart; i++) {
                if (i == nPart) {
                  context.read<ChatBloc>().add(
                    ChatSent(
                      ChatSendRequestParams(
                        conversationId: conversation?.id,
                        content: message ?? state.content,
                        parseMode: Utils.isHtml(message ?? state.content ?? '')
                            ? 'html'
                            : 'text',
                        requestId: state.requestId,
                        attachment: uploadFileList
                            .sublist((nPart - 1) * 10)
                            .map(
                              (final e) => AttachmentRequestParams(
                                link: e?.link,
                                size: e?.size,
                                originalname: e?.originalname,
                                mimetype: e?.mimetype,
                                thumbnail: e?.thumbnail,
                                duration: e?.duration,
                                waveform: e?.waveform,
                                isRecord:
                                    e?.duration != null && e!.duration! > 0,
                                checksum: e?.checksum,
                              ),
                            )
                            .toList(),
                        mention: tagUserList
                            .map(
                              (final e) => MentionUserRequestParams(
                                username: e?.username,
                                label: e?.name,
                                text: '@${e?.username}',
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  );
                } else {
                  context.read<ChatBloc>().add(
                    ChatSent(
                      ChatSendRequestParams(
                        parseMode: 'text',
                        conversationId: conversation?.id,
                        content: '',
                        requestId: state.requestId,
                        attachment: uploadFileList
                            .sublist(
                              (i - 1) * 10,
                              (i * 10).clamp(
                                (i - 1) * 10,
                                uploadFileList.length,
                              ),
                            )
                            .map(
                              (final e) => AttachmentRequestParams(
                                link: e?.link,
                                size: e?.size,
                                originalname: e?.originalname,
                                mimetype: e?.mimetype,
                                thumbnail: e?.thumbnail,
                                duration: e?.duration,
                                waveform: e?.waveform,
                                isRecord:
                                    e?.duration != null && e!.duration! > 0,
                              ),
                            )
                            .toList(),
                        mention: [],
                      ),
                    ),
                  );
                }
              }
            }

            if (state.status == ChatStatus.reactSuccess) {
              final ChatMessageFromSocketEvent? res = Utils.getData(state.data);
              final message = _pagingController.itemList?.firstWhereOrNull(
                (final element) => res?.message?.id == element?.id,
              );
              if (message != null) {
                message.reactions.clear();
                message.reactions.addAll(res?.message?.reactions ?? []);
                message.widthSize = null;
                return;
              }

              final messagePrevious = _pagingControllerPrevious.itemList
                  ?.firstWhereOrNull(
                    (final element) => res?.message?.id == element?.id,
                  );
              if (messagePrevious != null) {
                messagePrevious.reactions.clear();
                messagePrevious.reactions.addAll(res?.message?.reactions ?? []);
                messagePrevious.widthSize = null;
              }
            }

            if (state.status == ChatStatus.messageEditSuccess) {
              final ChatMessageFromSocketEvent? res = Utils.getData(state.data);
              final message = _pagingController.itemList?.firstWhereOrNull(
                (final element) => res?.message?.id == element?.id,
              );
              if (message != null) {
                message.content = res?.message?.content;
                message.isEdited = res?.message?.isEdited;
                message.mention = res?.message?.mention ?? [];
                for (final element
                    in _pagingController.itemList ?? <ChatItems?>[]) {
                  if (element?.replyInfo?.id == res?.message?.id) {
                    element?.replyInfo?.content = res?.message?.content;
                  }
                }
                message.widthSize = null;
                return;
              }

              final messagePrevious = _pagingControllerPrevious.itemList
                  ?.firstWhereOrNull(
                    (final element) => res?.message?.id == element?.id,
                  );
              if (messagePrevious != null) {
                messagePrevious.content = res?.message?.content;
                messagePrevious.isEdited = res?.message?.isEdited;
                messagePrevious.mention = res?.message?.mention ?? [];
                for (final element
                    in _pagingControllerPrevious.itemList ?? <ChatItems?>[]) {
                  if (element?.replyInfo?.id == res?.message?.id) {
                    element?.replyInfo?.content = res?.message?.content;
                  }
                }
                messagePrevious.widthSize = null;
                return;
              }
            }
            if (state.status == ChatStatus.markUserSeenSuccess) {
              final ChatMessageFromSocketEvent? res = Utils.getData(state.data);
              final message = _pagingController.itemList?.firstWhereOrNull(
                (final element) => res?.message?.id == element?.id,
              );
              if (message != null && !message.seen) {
                for (final item
                    in _pagingController.itemList ?? <ChatItems?>[]) {
                  if (item?.seen ?? false) {
                    return;
                  }
                  item?.seen = true;
                }

                return;
              }

              final messagePrevious = _pagingControllerPrevious.itemList
                  ?.firstWhereOrNull(
                    (final element) => res?.message?.id == element?.id,
                  );
              if (messagePrevious != null) {
                messagePrevious.seen = true;
                return;
              }
            }

            if (state.status == ChatStatus.forwardSuccess) {
              _handleWhenSendSuccess(state, isForward: true);
              showMessageForwardSuccess = false;
              unawaited(
                EzToast.showShortToast(
                  message: context.l10n.forwardMessageSuccess,
                ),
              );
              // if (forwardMessage?.id != initForwardId &&
              //     !showMessageForwardSuccess) {
              //   _handleWhenSendSuccess(state);
              // } else {
              //   unawaited(
              //     EzToast.showShortToast(
              //       message: context.l10n.forwardMessageSuccess,
              //     ),
              //   );
              //   showMessageForwardSuccess = false;
              // }
            }

            if (state.status == ChatStatus.messageRemoveWithAnimationSuccess) {
              Future.delayed(Durations.short2, () {
                final ChatMessageFromSocketEvent? removedMessage =
                    Utils.getData(state.data);
                if (removedMessage != null) {
                  _pagingController.itemList
                          ?.firstWhereOrNull(
                            (final element) =>
                                removedMessage.message?.id ==
                                element?.replyInfo?.id,
                          )
                          ?.replyInfo =
                      null;

                  _pagingControllerPrevious.itemList?.removeWhere(
                    (final element) =>
                        removedMessage.message?.id == element?.id,
                  );
                }
                setState(() {});
              });
            }
            if (state.status == ChatStatus.messageRemoveSuccess) {
              Future.delayed(Durations.short2, () {
                final ChatMessageFromSocketEvent? removedMessage =
                    Utils.getData(state.data);
                if (removedMessage != null) {
                  _pagingController.itemList?.removeWhere(
                    (final element) =>
                        removedMessage.message?.id == element?.id,
                  );

                  _pagingController.itemList
                          ?.firstWhereOrNull(
                            (final element) =>
                                removedMessage.message?.id ==
                                element?.replyInfo?.id,
                          )
                          ?.replyInfo =
                      null;

                  _pagingControllerPrevious.itemList?.removeWhere(
                    (final element) =>
                        removedMessage.message?.id == element?.id,
                  );

                  _pagingControllerPrevious.itemList
                          ?.firstWhereOrNull(
                            (final element) =>
                                removedMessage.message?.id ==
                                element?.replyInfo?.id,
                          )
                          ?.replyInfo =
                      null;
                }
                setState(() {});
              });
            }

            if (state.status == ChatStatus.getPinListSuccess) {
              pinMessageList =
                  (Utils.getData<ChatGetPinList>(state.data)?.messages?.items ??
                          [])
                      .reversed
                      .toList();

              if (pinMessageList.isEmpty) {
                showPinList = false;
              }
            }

            if (state.status == ChatStatus.pinMessageSuccess ||
                state.status == ChatStatus.unpinMessageSuccess) {
              context.read<ChatBloc>().add(
                ChatGetPinnedList(
                  ChatGetPinListRequestParams(widget.conversationId),
                ),
              );
            }

            if (state.status == ChatStatus.votePollSuccess ||
                state.status == ChatStatus.updatePollSuccess) {
              ChatItems? message;
              int index = -1;

              if (state.status == ChatStatus.votePollSuccess) {
                final ChatVotePoll? poll = Utils.getData(state.data);
                message = poll?.message;
              } else {
                final ChatUpdatePoll? poll = Utils.getData(state.data);
                message = poll?.message;
              }
              index =
                  _pagingController.itemList?.indexWhere(
                    (final e) => e?.id == message?.id,
                  ) ??
                  -1;
              if (index != -1) {
                _pagingController.itemList?.removeAt(index);
                _pagingController.itemList?.insert(index, message);
              } else {
                index =
                    _pagingControllerPrevious.itemList?.indexWhere(
                      (final e) => e?.id == message?.id,
                    ) ??
                    -1;
                if (index != -1) {
                  _pagingControllerPrevious.itemList?.removeAt(index);
                  _pagingControllerPrevious.itemList?.insert(index, message);
                }
              }
            }

            if (state.status == ChatStatus.getUserSeenSuccess) {
              final newItems =
                  Utils.getData<ChatGetUserSeen>(state.data)?.docs ?? [];
              if (newItems.length < 10) {
                _userSeenPagingController.appendLastPage(newItems);
              } else {
                _userSeenPagingController.appendPage(
                  newItems,
                  userSeenCurrentPage + 1,
                );
              }
            }
            if (state.status == ChatStatus.getTotalUserSeenSuccess) {
              userSeenData = Utils.getData<ChatGetUserSeen>(state.data);
              ChatBody.completer.complete(userSeenData);
            }
            if (state.status == ChatStatus.getUserStickerSuccess) {
              stickerData =
                  Utils.getData<ChatGetUserSticker>(state.data)?.items ?? [];
              setState(() {});
            }
            if (state.status == ChatStatus.getUserRulesSuccess) {
              if (conversation?.isGroup ?? false) {
                final GroupChatDetailGetUserRules? data = Utils.getData(
                  state.data,
                );
                if (data?.role?.name == UserChatRole.owner.name) {
                  userRules = UserChatRule.values
                      .map((final e) => e.name)
                      .toList();
                } else {
                  userRules =
                      data?.role?.rules.map((final e) => e?.actions).toList() ??
                      [];
                  if (data?.role?.name == UserChatRole.admin.name) {
                    userRules.addAll(
                      defaultMemberRules.map((final e) => e.name).toList(),
                    );
                  }
                }
              } else {
                userRules = UserChatRule.values
                    .map((final e) => e.name)
                    .toList();
              }
            }
          },
          builder: (final context, final state) {
            keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
            if (MediaQuery.of(context).padding.bottom > 0) {
              safeBottom = MediaQuery.of(context).padding.bottom;
            }

            return Stack(
              children: [
                PopScope(
                  canPop: false,
                  onPopInvokedWithResult: (final didPop, final _) {
                    if (didPop) {
                      return;
                    }
                    if (listFailedMessageId.isNotEmpty) {
                      Alert.showAlertConfirm(
                        AlertConfirmParams(
                          context,
                          message: context.l10n.alertHasFailedMessage,
                          title: context.l10n.alert,
                          confirmText: context.l10n.confirm,
                          cancelButton: context.l10n.cancel,
                          onPressed: () {
                            Navigator.of(context).pop();
                            _onBack(context);
                          },
                        ),
                      );
                    } else {
                      _onBack(context);
                    }
                  },
                  child: Dismissible(
                    key: const ValueKey(0),
                    resizeDuration: null,
                    onUpdate: (final _) {
                      if (listFailedMessageId.isNotEmpty) {
                        Alert.showAlertConfirm(
                          AlertConfirmParams(
                            context,
                            message: context.l10n.alertHasFailedMessage,
                            title: context.l10n.alert,
                            confirmText: context.l10n.confirm,
                            cancelButton: context.l10n.cancel,
                            onPressed: () {
                              Navigator.of(context).pop();
                              _onBack(context);
                            },
                          ),
                        );
                      } else {
                        if (timer?.isActive ?? false) {
                          timer?.cancel();
                          context.read<ChatBloc>().add(
                            ChatConversationDetailsUpdated(
                              ChatConversationDetailsUpdateRequestParams(
                                conversationId: conversation?.id,
                                lastSeenMessageId: lastSeenMessageId,
                                lastSeenAt: lastSeenMessageTime,
                                conversationType:
                                    ConversationType.internal.name,
                                readMessageCount: readMessageCount,
                                mentionMessages: conversation
                                    ?.conversationDetails
                                    ?.mentionMessages
                                    ?.map(
                                      (final e) => MentionMessageRequestParams(
                                        id: e?.id,
                                        createdAt: e?.createdAt,
                                      ),
                                    )
                                    .toList(),
                              ),
                            ),
                          );
                        }
                      }
                    },
                    onDismissed: (final direction) {
                      if (context.router.canPop()) {
                        context.router.popForced();
                        Future.delayed(const Duration(milliseconds: 300), () {
                          final currentContext =
                              getIt<AppRouter>().navigatorKey.currentContext;
                          if (currentContext != null &&
                              currentContext.mounted) {
                            currentContext.read<GeneralBloc>().add(
                              GeneralUpdateConversation(
                                needUpdateConversationId.reversed.toList(),
                              ),
                            );
                          }
                        });
                      }
                    },
                    direction: DismissDirection.startToEnd,
                    child: Stack(
                      children: [
                        if (showPinList)
                          _buildPinList(context)
                        else
                          Scaffold(
                            backgroundColor: const Color(0xffF6F6F6),
                            bottomNavigationBar: Utils.isAndroid
                                ? null
                                : SizedBox(
                                    height: (isMultiSelect || recording)
                                        ? 10
                                        : 20,
                                  ),
                            appBar: AppBar(
                              centerTitle: true,
                              backgroundColor: const Color(0xffF5F9F3),
                              foregroundColor: Theme.of(context).primaryColor,
                              actionsPadding: const EdgeInsets.only(bottom: 3),
                              leading: isMultiSelect
                                  ? const SizedBox.shrink()
                                  : GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        if (listFailedMessageId.isNotEmpty) {
                                          Alert.showAlertConfirm(
                                            AlertConfirmParams(
                                              context,
                                              message: context
                                                  .l10n
                                                  .alertHasFailedMessage,
                                              title: context.l10n.alert,
                                              confirmText: context.l10n.confirm,
                                              cancelButton: context.l10n.cancel,
                                              onPressed: () {
                                                Navigator.of(context).pop();
                                                _onBack(context);
                                              },
                                            ),
                                          );
                                        } else {
                                          _onBack(context);
                                        }
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                          right: 28,
                                        ),
                                        child: Icon(
                                          Icons.arrow_back_ios_new_rounded,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ),
                              title: isMultiSelect
                                  ? ValueListenableBuilder(
                                      valueListenable: selectedMessagesCount,
                                      builder:
                                          (
                                            final BuildContext _,
                                            final value,
                                            final Widget? __,
                                          ) {
                                            return Text(
                                              value.toString() +
                                                  Strings.space +
                                                  context.l10n.optional,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleSmall
                                                  ?.copyWith(fontSize: 18),
                                              textAlign: TextAlign.center,
                                            );
                                          },
                                    )
                                  : GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () => _pushToGroupDetails(context),
                                      child: _buildConversationName(context),
                                    ),
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  color: Theme.of(context).dividerColor,
                                ),
                              ),
                              // actions: _buildAppBarActions(),
                              actions: [
                                if (isMultiSelect)
                                  IconButton(
                                    icon: Text(
                                      context.l10n.cancel,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.copyWith(
                                            color: Theme.of(
                                              context,
                                            ).primaryColor,
                                          ),
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        isMultiSelect = false;
                                        selectedMessageList.clear();
                                      });
                                    },
                                  )
                                else
                                  _buildAvatar(context),
                                const SizedBox(width: 6),
                              ],
                              toolbarHeight:
                                  40 * context.watch<FontsBloc>().textScale,
                            ),
                            body: Stack(
                              children: [
                                SafeArea(
                                  top: false,

                                  child: Column(
                                    children: [
                                      _buildPinListCollapse(context),
                                      GlobalAudioPlayer(
                                        onClose: () {
                                          if (mounted) {
                                            setState(() {});
                                          }
                                        },
                                      ),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            FocusScope.of(context).unfocus();
                                            emojiShowing.value = false;
                                          },
                                          child: Scaffold(
                                            backgroundColor:
                                                (forwardMessage != null &&
                                                    forwardMessage?.id !=
                                                        initForwardId)
                                                ? Theme.of(
                                                    context,
                                                  ).colorScheme.surface
                                                : AppColors.secondaryBackground,
                                            floatingActionButton: Padding(
                                              padding: const EdgeInsets.only(
                                                right: 18,
                                              ),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  _buildMentionMessageButton(),
                                                  _buildScrollToEndButton(),
                                                ],
                                              ),
                                            ),
                                            floatingActionButtonLocation:
                                                FloatingActionButtonLocation
                                                    .startDocked,
                                            body: Container(
                                              decoration: BoxDecoration(
                                                image: DecorationImage(
                                                  image: EZResources.assetImage(
                                                    AppImages.chatBackground,
                                                  ),
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              child:
                                                  forwardMessage != null &&
                                                      forwardMessage?.id !=
                                                          initForwardId
                                                  ? _buildForwardConversations()
                                                  : _buildMessageList(context),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Divider(
                                        color: Theme.of(context).dividerColor,
                                      ),
                                      if (pickedFileBundles.isNotEmpty)
                                        SelectingFileWidget(
                                          pickedFileBundles: pickedFileBundles,
                                          onTapRemove: onTapRemoveFile,
                                          onTapAddFile: () async {
                                            FocusScope.of(context).unfocus();
                                            pickedFileBundles.addAll(
                                              await attachFile() ?? [],
                                            );
                                            if (mounted) {
                                              setState(() {});
                                            }
                                            return;
                                          },
                                        ),
                                      if (replyMessage != null)
                                        _buildReplyMessage(context),
                                      if (forwardMessage != null)
                                        _buildForwardMessage(context),
                                      if (editMessage != null)
                                        _buildEditMessage(context),
                                      buildRecorder(context),
                                      if (!recording && !isMultiSelect)
                                        _buildUserTagList(),
                                      if (isMultiSelect)
                                        _buildActionForMultiSelecting(context),
                                      EmojiPickerWidget(
                                        emojiShowing: emojiShowing,
                                        textController: messageController,
                                        focusNode: messageFocusNode,
                                        sets: stickerData,
                                        onStickerSelected: (final sticker) {
                                          _onStickerSelected(sticker);
                                        },
                                        stickersRecently: stickersRecent,
                                        onStickerCreated: () {},
                                        isExpandedSticker: isExpandedSticker,
                                      ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                    top: pinMessageList.isNotEmpty
                                        ? 55 *
                                              context
                                                  .watch<FontsBloc>()
                                                  .textScale
                                        : 0,
                                  ),
                                  child: ValueListenableBuilder<bool>(
                                    valueListenable: isShowDate,
                                    builder:
                                        (
                                          final context,
                                          final value,
                                          final child,
                                        ) {
                                          return AnimatedSwitcher(
                                            duration: const Duration(
                                              milliseconds: 300,
                                            ),
                                            transitionBuilder:
                                                (
                                                  final Widget child,
                                                  final Animation<double>
                                                  animation,
                                                ) {
                                                  return SizeTransition(
                                                    sizeFactor: animation,
                                                    child: child,
                                                  );
                                                },
                                            child: value
                                                ? IntrinsicHeight(
                                                    // Ensures animation works
                                                    key: const ValueKey<bool>(
                                                      true,
                                                    ),
                                                    child: _buildDate(
                                                      currentMessageViewByDate,
                                                    ),
                                                  )
                                                : const SizedBox.shrink(),
                                          );
                                        },
                                  ),
                                ),
                                if (state.status ==
                                    ChatStatus.getConversationInProgress)
                                  const LoadingWidget(),
                              ],
                            ),
                          ),
                        ValueListenableBuilder<double>(
                          valueListenable: blurValue,
                          builder: (final context, final value, final child) =>
                              BackdropFilter(
                                filter: ImageFilter.blur(
                                  sigmaX: value,
                                  sigmaY: value,
                                ),
                                child: blurValue.value == 0
                                    ? const SizedBox.shrink()
                                    : Opacity(
                                        opacity: 0.85,
                                        child: Container(
                                          width: double.infinity,
                                          height: double.infinity,
                                          decoration: BoxDecoration(
                                            // color: Colors.transparent,
                                            image: DecorationImage(
                                              image: EZResources.assetImage(
                                                AppImages.callBackground,
                                              ),
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                      ),
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
                if (isShowSearchView)
                  MessageSearchPage(
                    conversationId: widget.conversationId,
                    membersInfo: conversation?.membersInfo ?? [],
                    onCancel: () {
                      isShowSearchView = false;
                      setState(() {});
                    },
                    onTapMessage: (final message) {
                      searchMessageCreatedAt = message?.createdAt;
                      _pagingController.refresh();
                    },
                    onSearchByDate: (final date) {
                      searchMessageCreatedAt = date;
                      _pagingController.refresh();
                    },
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  Future<dynamic> _showStickerView(final String? stickerSetId) {
    ChatGetUserStickerItems? stickerSet;
    if (stickerData.isNotEmpty) {
      stickerSet = stickerData.firstWhereOrNull(
        (final e) => e?.id == stickerSetId,
      );
    }
    return showModalBottomSheet(
      context: context,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.sizeOf(context).height * 0.6,
      ),
      builder: (final _) => BlocProvider.value(
        value: context.read<ChatBloc>(),
        child: BlocConsumer<ChatBloc, ChatState>(
          listener: (final context, final state) {
            if (state.status == ChatStatus.getUserStickerSuccess) {
              stickerSet = stickerData.firstWhereOrNull(
                (final e) => e?.id == stickerSetId,
              );
            }
          },
          builder: (final context, final state) {
            if (stickerSet == null && state.status == ChatStatus.loading) {
              return const LoadingWidget();
            }
            return Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(14),
                    ),
                    color: Color(0xffF3F3F3),
                  ),
                  child: Stack(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: IconButton(
                          icon: Text(
                            context.l10n.close,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  fontSize: 15,
                                  color: Theme.of(context).primaryColor,
                                ),
                          ),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),
                      Center(
                        child: IconButton(
                          onPressed: () {},
                          icon: Text(
                            stickerSet?.name ?? '',
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(child: _buildStickerTab(stickerSet)),
              ],
            );
          },
        ),
      ),
    );
  }

  Container _buildActionForMultiSelecting(final BuildContext context) {
    return Container(
      color: const Color(0xffF6F6F6),
      padding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      width: MediaQuery.sizeOf(context).width,
      child: Row(
        children: <Widget>[
          if (userRules.contains(UserChatRule.removeMessage.name))
            IconButton(
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icDeleteV2,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              onPressed: () {
                for (final item in selectedMessageList) {
                  context.read<ChatBloc>().add(
                    ChatMessageRemoved(item?.id ?? ''),
                  );
                }

                setState(() {
                  isMultiSelect = false;
                  selectedMessageList.clear();
                });
              },
            ),
          const Spacer(),
          if (userRules.contains(UserChatRule.forwardMessage.name))
            IconButton(
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icActionForward,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              onPressed: () {
                setState(() {
                  showPinList = false;
                  replyMessage = null;
                  editMessage = null;
                  final message = selectedMessageList.firstOrNull;
                  forwardMessage = message?.copyWith(
                    id: message.forward?.id ?? message.id,
                    createdByInfo: message.forward?.createdByInfo?.id != null
                        ? ChatItemsCreatedByInfo(
                            avatar: message.forward?.createdByInfo?.avatar,
                            name: message.forward?.createdByInfo?.name,
                            username: message.forward?.createdByInfo?.username,
                            id: message.forward?.createdByInfo?.id,
                          )
                        : message.createdByInfo,
                  );
                  isMultiSelect = false;
                });
              },
            ),
        ],
      ),
    );
  }

  Future<void> _pushToGroupDetails(final BuildContext context) async {
    context.router.push(GroupChatDetailRoute(conversation: conversation)).then((
      final item,
    ) async {
      if (item != null && item is ChatItems && context.mounted) {
        context.router.popAndPush(
          ChatRoute(
            conversationId: item.conversationId,
            createdAt: item.createdAt,
          ),
        );
      }
      if (item != null && item is String) {
        conversation?.avatar = item;
      }
      if (item != null && item is GroupChatAction) {
        isShowSearchView = true;
      }
      setState(() {});
    });
  }

  void _onBack(final BuildContext context) {
    if (context.router.canPop()) {
      if (timer?.isActive ?? false) {
        timer?.cancel();
        context.read<ChatBloc>().add(
          ChatConversationDetailsUpdated(
            ChatConversationDetailsUpdateRequestParams(
              conversationId: conversation?.id,
              lastSeenMessageId: lastSeenMessageId,
              lastSeenAt: lastSeenMessageTime,
              conversationType: ConversationType.internal.name,
              readMessageCount: readMessageCount,
              mentionMessages: conversation
                  ?.conversationDetails
                  ?.mentionMessages
                  ?.map(
                    (final e) => MentionMessageRequestParams(
                      id: e?.id,
                      createdAt: e?.createdAt,
                    ),
                  )
                  .toList(),
            ),
          ),
        );
      }

      context.router.popForced();

      Future.delayed(const Duration(milliseconds: 300), () {
        final currentContext = getIt<AppRouter>().navigatorKey.currentContext;
        if (currentContext != null && currentContext.mounted) {
          currentContext.read<GeneralBloc>().add(
            GeneralUpdateConversation(
              needUpdateConversationId.reversed.toList(),
            ),
          );
        }
      });
    }
  }

  Widget _buildConversationName(final BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                right: (conversation?.conversationDetails?.isMute ?? false)
                    ? 16
                    : 0,
              ),
              child: Text(
                conversation?.name ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontSize: 18),
                textAlign: TextAlign.center,
              ),
            ),
            Positioned(
              right: 0,
              top: 3,
              child: (conversation?.conversationDetails?.isMute ?? false)
                  ? EZResources.image(
                      ImageParams(
                        name: AppIcons.icMute2,
                        size: const ImageSize.square(16),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
        if (conversation?.isGroup ?? false)
          Text(
            '${conversation?.members.length}'
            '${Strings.space}${context.l10n.member.toLowerCase()}',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Theme.of(context).hintColor),
            textAlign: TextAlign.center,
          ),
      ],
    );
  }

  Widget _buildPinList(final BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF5F9F3),
      appBar: AppBar(
        backgroundColor: const Color(0xffF5F9F3),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            showPinList = false;
            setState(() {});
          },
        ),
        title: Text(
          '${pinMessageList.length}${Strings.space}'
          '${context.l10n.pinnedMessage.toLowerCase()}',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: 18),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(color: Theme.of(context).dividerColor, height: 1),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: EZResources.assetImage(AppImages.chatBackground),
                  fit: BoxFit.cover,
                ),
              ),
              child: ListView.separated(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 32,
                ),
                reverse: true,
                itemCount: pinMessageList.length,
                separatorBuilder:
                    (final BuildContext context, final int index) {
                      return const SizedBox(height: 8);
                    },
                itemBuilder: (final BuildContext context, final int index) {
                  final item = pinMessageList[index];
                  final lastIndex = pinMessageList.length;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (index == lastIndex - 1 ||
                          !isTheSameDay(
                            item?.createdAt,
                            pinMessageList[index + 1]?.createdAt,
                          ))
                        _buildDate(item),
                      Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 44),
                            child: _buildMessage(
                              context,
                              index,
                              item,
                              lastIndex,
                              _pagingController,
                            ),
                          ),
                          Align(
                            alignment: Alignment.bottomRight,
                            child: SizedBox(
                              height: 32,
                              child: IconButton(
                                padding: EdgeInsets.zero,
                                onPressed: () {
                                  showPinList = false;
                                  setState(() {});

                                  FocusScope.of(context).unfocus();
                                  searchMessageCreatedAt = item?.createdAt;
                                  _pagingController.refresh();
                                },
                                icon: Icon(
                                  Icons.arrow_circle_right_outlined,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
          const Divider(),
          TextButton(
            onPressed: () async {
              Alert.showAlertConfirm(
                AlertConfirmParams(
                  context,
                  confirmText: context.l10n.accept,
                  cancelButton: context.l10n.cancel,
                  message: context.l10n.unpinAllMessageWarning,
                  onPressed: () {
                    context.read<ChatBloc>().add(
                      ChatUnpinnedMessage(
                        ChatUnpinMessageRequestParams(
                          conversationId: widget.conversationId,
                          messageIds: pinMessageList
                              .map((final e) => e?.id)
                              .toList(),
                        ),
                      ),
                    );
                    Navigator.of(context).pop();
                  },
                ),
              );
            },
            child: Text(context.l10n.unpinAllMessage),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPinListCollapse(final BuildContext context) {
    if (pinMessageList.isEmpty) {
      return const SizedBox();
    }
    final scroll = ScrollController();

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (currentIndexPinMessage >= pinMessageList.length - 1) {
          currentIndexPinMessage = 0;
        } else {
          currentIndexPinMessage++;
        }
        if (pinMessageList.length > 3) {
          if (currentIndexPinMessage == 0) {
            unawaited(
              scroll.animateTo(
                0,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              ),
            );
          }
          if (currentIndexPinMessage > 2) {
            unawaited(
              scroll.animateTo(
                scroll.offset + 18,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              ),
            );
          }
        }

        setState(() {});

        FocusScope.of(context).unfocus();
        final focusIndex = currentIndexPinMessage == 0
            ? pinMessageList.length - 1
            : currentIndexPinMessage - 1;
        searchMessageCreatedAt = pinMessageList[focusIndex]?.createdAt;
        _pagingController.refresh();
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 4, 0, 4),
        height: 55 * context.watch<FontsBloc>().textScale,
        color: const Color(0xffF1F7F3),
        width: double.infinity,
        child: Row(
          children: [
            SizedBox(
              width: 2,
              child: pinMessageList.length <= 3
                  ? Column(
                      children: pinMessageList
                          .mapIndexed(
                            (final index, final item) => Expanded(
                              child: Container(
                                margin: const EdgeInsets.only(bottom: 2.5),
                                width: 2,
                                color:
                                    currentIndexPinMessage == index ||
                                        (currentIndexPinMessage > index &&
                                            index == pinMessageList.length - 1)
                                    ? Theme.of(context).primaryColor
                                    : Theme.of(context).disabledColor,
                              ),
                            ),
                          )
                          .toList(),
                    )
                  : SingleChildScrollView(
                      controller: scroll,
                      child: Column(
                        children: pinMessageList
                            .mapIndexed(
                              (final index, final item) => Container(
                                margin: const EdgeInsets.only(bottom: 2.5),
                                width: 2,
                                height: 13,
                                color:
                                    currentIndexPinMessage == index ||
                                        (currentIndexPinMessage > index &&
                                            index == pinMessageList.length - 1)
                                    ? Theme.of(context).primaryColor
                                    : Theme.of(context).disabledColor,
                              ),
                            )
                            .toList(),
                      ),
                    ),
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.pinnedMessage,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  LastMessageWidget(
                    textScale: context.watch<FontsBloc>().textScale,
                    lastMessageInfo:
                        pinMessageList[min(
                          pinMessageList.length - 1,
                          currentIndexPinMessage,
                        )],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 6),
            IconButton(
              onPressed: () {
                showPinList = true;
                setState(() {});
              },
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icPinList,
                  size: const ImageSize.square(24),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForwardConversations() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xffF3F3F3),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          Stack(
            children: <Widget>[
              IconButton(
                icon: Text(
                  context.l10n.close,
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    fontSize: 15,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                onPressed: () {
                  forwardMessage = null;
                  forwardConversations.clear();
                  if (mounted) {
                    setState(() {});
                  }
                },
              ),
              SizedBox(
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Center(
                    child: Text(
                      context.l10n.forward,
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ),
                ),
              ),
            ],
          ),
          _buildSearchUserForward(),
          const Divider(thickness: 0.5, color: Color(0xffC7C7C7)),
          Container(height: 8, color: Theme.of(context).colorScheme.surface),
          Expanded(
            child: PagedListView(
              pagingController: _conversationPagingController,
              builderDelegate: PagedChildBuilderDelegate<ChatListItems?>(
                itemBuilder: (final context, final item, final index) =>
                    ForwardUserTile(
                      item,
                      isCheck:
                          forwardConversations.firstWhereOrNull(
                            (final element) => element?.id == item?.id,
                          ) !=
                          null,
                      onTap: ({required final bool value}) {
                        if (item?.id != null) {
                          if (value) {
                            forwardConversations.add(item);
                          } else {
                            forwardConversations.removeWhere(
                              (final element) => element?.id == item?.id,
                            );
                          }
                          setState(() {});
                        }
                      },
                    ),
                noItemsFoundIndicatorBuilder: (final context) =>
                    Center(child: Text(context.l10n.noData)),
                firstPageProgressIndicatorBuilder: (final context) =>
                    const SizedBox.shrink(),
                newPageProgressIndicatorBuilder: (final context) =>
                    const SizedBox.shrink(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // List<Widget> _buildAppBarActions() {
  //   return [
  //     if (conversation?.membersInfo.length == 2)
  //       IconButton(
  //         padding: EdgeInsets.zero,
  //         onPressed: _makeCall,
  //         icon: EZResources.image(
  //           ImageParams(name: AppIcons.icCallChat),
  //         ),
  //       ),
  //     // SizedBox(
  //     //   width: 36,
  //     //   height: 36,
  //     //   child: IconButton(
  //     //     padding: EdgeInsets.zero,
  //     //     icon: EZResources.image(
  //     //       ImageParams(name: AppIcons.icSearch),
  //     //     ),
  //     //     onPressed: () {},
  //     //   ),
  //     // ),
  //     // SizedBox(
  //     //   width: 36,
  //     //   height: 36,
  //     //   child: IconButton(
  //     //     padding: EdgeInsets.zero,
  //     //     icon: EZResources.image(
  //     //       ImageParams(name: AppIcons.icMenu),
  //     //     ),
  //     //     onPressed: () {},
  //     //   ),
  //     // ),
  //   ];
  // }

  TypeAheadField<ChatListSearchItems?> _buildSearchUserForward() {
    return TypeAheadField<ChatListSearchItems?>(
      hideOnEmpty: true,
      suggestionsCallback: (final typingText) async {
        timer?.cancel();
        completer = Completer();
        if (typingText.length <= 1) {
          userSearchList.clear();
          completer.complete(userSearchList);
        } else {
          timer = Timer(const Duration(milliseconds: 800), () {
            context.read<ChatListBloc>().add(
              ChatListSearched(ChatListSearchRequestParams(typingText)),
            );
          });
        }
        return completer.future;
      },
      controller: searchUserController,
      focusNode: searchUserFocusNode,
      builder:
          (
            final context,
            final htmlController,
            final controller,
            final focusNode,
          ) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: ChatSearchBar(
                controller: controller,
                focusNode: focusNode,
                onClear: () {
                  searchUserController.clear();
                  userSearchList.clear();
                },
              ),
            );
          },
      loadingBuilder: (final context) => SizedBox(
        height: 80,
        child: LoadingAnimationWidget.hexagonDots(
          size: 36,
          color: Theme.of(context).primaryColor,
        ),
      ),
      itemBuilder: (final context, final member) {
        return ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(32),
            child: (member?.avatar?.isNotEmpty ?? false)
                ? EzCachedNetworkImage(
                    imageUrl: member?.avatar,
                    width: 44,
                    height: 44,
                  )
                : Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          Utils.randomColor(
                            member?.id ?? '',
                          ).withValues(alpha: 0.5),
                          Utils.randomColor(member?.id ?? ''),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Text(
                        Utils.defaultOnEmpty(member?.name).isEmpty
                            ? Strings.appName[0].toUpperCase()
                            : Utils.defaultOnEmpty(
                                member?.name,
                              ).firstOrEmpty().toUpperCase(),
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.surface,
                            ),
                      ),
                    ),
                  ),
          ),
          title: Text(Utils.defaultOnEmpty(member?.name)),
          subtitle: (member?.username?.isNotEmpty ?? false)
              ? Text(Utils.defaultOnEmpty(member?.username))
              : null,
        );
      },
      decorationBuilder: (final context, final child) {
        return Material(
          type: MaterialType.card,
          elevation: 4,
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          child: child,
        );
      },
      onSelected: (final conversation) {
        FocusScope.of(context).unfocus();
        if (_conversationPagingController.itemList?.firstWhereOrNull(
              (final element) => element?.id == conversation?.id,
            ) !=
            null) {
          final newConversation = ChatListItems(
            id: conversation?.id,
            membersInfo: [],
            members: [],
            avatar: conversation?.avatar,
            name: conversation?.name,
            memberRules: [],
          );

          _conversationPagingController.itemList?.removeWhere(
            (final e) => e?.id == newConversation.id,
          );

          _conversationPagingController.itemList?.insert(0, newConversation);

          forwardConversations.add(newConversation);
        } else {
          if (conversation?.username?.isNotEmpty ?? false) {
            context.read<ChatBloc>().add(
              ChatGetConversationDetail(
                ChatGetByIdRequestParams(username: conversation?.username),
              ),
            );
          } else {
            context.read<ChatBloc>().add(
              ChatGetConversationDetail(
                ChatGetByIdRequestParams(id: conversation?.id),
              ),
            );
          }
        }
        searchUserController.clear();
      },
    );
  }

  void _handleWhenSendSuccess(
    final ChatState state, {
    final bool isForward = false,
  }) {
    final ChatSend? sendMessage = Utils.getData(state.data);

    // this is message is created when forward multi messages
    // so do not reload chat list
    if (sendMessage?.requestId == ChatAction.forward.name) {
      return;
    }

    listFailedMessageId.remove(sendMessage?.requestId);

    latestMessage = sendMessage?.message;

    _pagingController.itemList?.removeWhere(
      (final e) => e?.requestId == sendMessage?.requestId,
    );
    lastSeenMessageId = sendMessage?.message?.id;
    lastSeenMessageTime = sendMessage?.message?.createdAt;
    unreadCount.value = 0;

    // check action is send or push with init forward message
    if (!isForward || initForwardId != null) {
      if (searchMessageCreatedAt == null) {
        _pagingController.itemList?.insert(0, sendMessage?.message);
      }
    }

    final attachment =
        (sendMessage?.message?.attachment ?? []) +
        (sendMessage?.message?.forward?.attachment ?? []);
    for (final media in attachment) {
      if (getFileExtension(media?.originalname ?? '') ==
          FileExtensionType.audio) {
        audioUrlList.add(
          AudioUrl(
            conversationId: widget.conversationId,
            attachment: media?..localId = const Uuid().v4(),
          ),
        );
      }
      if (getFileExtension(media?.originalname ?? '') ==
          FileExtensionType.image) {
        imageUrlList.add(
          ImageProperty(
            url: media?.link,
            fileNameWithExtension: media?.originalname,
          ),
        );
      }
    }

    // check action is send or push with init forward message
    if (!isForward || initForwardId != null) {
      unawaited(_scrollToEnd(controller: _pagingController));
    }

    if (isForward) {
      initForwardId = null;
    }
  }

  void _socketListen() {
    SocketService().listen(SocketEvent.newMessage.value, (final response) {
      final ChatItems? message = getIt<Mapper>().tryConvert(
        NewMessageResponseModel.fromJson(response).data?.message,
      );

      if (message != null && message.conversationId == widget.conversationId) {
        if (mounted) {
          context.read<ChatBloc>().add(ChatGetNewMessage(message));
        }
      }
    });
    SocketService().listen(SocketEvent.sendReaction.name, (final response) {
      final ChatItems? message = getIt<Mapper>().tryConvert(
        NewMessageResponseModel.fromJson(response).data?.message,
      );

      if (message != null && message.conversationId == widget.conversationId) {
        if (mounted) {
          context.read<ChatBloc>().add(
            ChatGetUpdateMessage(message, SocketEvent.sendReaction),
          );
        }
      }
    });
    SocketService().listen(SocketEvent.editMessage.name, (final response) {
      final ChatItems? message = getIt<Mapper>().tryConvert(
        NewMessageResponseModel.fromJson(response).data?.message,
      );

      if (message != null && message.conversationId == widget.conversationId) {
        if (mounted) {
          context.read<ChatBloc>().add(
            ChatGetUpdateMessage(message, SocketEvent.editMessage),
          );
        }
      }
    });
    SocketService().listen(SocketEvent.removeMessage.name, (final response) {
      final ChatItems? message = getIt<Mapper>().tryConvert(
        NewMessageResponseModel.fromJson(response).data?.message,
      );

      if (message != null && message.conversationId == widget.conversationId) {
        if (mounted) {
          context.read<ChatBloc>().add(
            ChatGetUpdateMessage(message, SocketEvent.removeMessage),
          );
        }
      }
    });
    SocketService().listen(SocketEvent.sendVotePoll.name, (final response) {
      final ChatItems? message = getIt<Mapper>().tryConvert(
        NewMessageResponseModel.fromJson(response).data?.message,
      );

      if (message != null && message.conversationId == widget.conversationId) {
        int index = -1;
        index =
            _pagingController.itemList?.indexWhere(
              (final e) => e?.id == message.id,
            ) ??
            -1;
        if (index != -1) {
          _pagingController.itemList?.removeAt(index);
          _pagingController.itemList?.insert(index, message);
        } else {
          index =
              _pagingControllerPrevious.itemList?.indexWhere(
                (final e) => e?.id == message.id,
              ) ??
              -1;
          if (index != -1) {
            _pagingControllerPrevious.itemList?.removeAt(index);
            _pagingControllerPrevious.itemList?.insert(index, message);
          }
        }
      }
      if (mounted) {
        setState(() {});
      }
    });
    SocketService().listen(SocketEvent.sendUpdatePoll.name, (final response) {
      final poll = ChatItemsPollInfoModel.fromJson(
        response['data'] as Map<String, dynamic>,
      );
      final mess = _pagingController.itemList?.firstWhereOrNull(
        (final e) => e?.id == poll.messageId,
      );
      mess?.pollInfo?.status = poll.status;
      if (mounted) {
        setState(() {});
      }
    });
    SocketService().listen(SocketEvent.userSeen.name, (final response) {
      final ChatItems? message = getIt<Mapper>().tryConvert(
        NewMessageResponseModel.fromJson(response).data?.message,
      );
      if (message != null && message.conversationId == widget.conversationId) {
        if (mounted) {
          context.read<ChatBloc>().add(
            ChatGetUpdateMessage(message, SocketEvent.userSeen),
          );
        }
      }
    });
    SocketService().listen(SocketEvent.updateUserPermission.name, (
      final response,
    ) {
      final data = GroupChatDetailUpdateMemberRuleModel.fromJson(
        (response['data'] as Map<String, dynamic>)['conversationDetails']
            as Map<String, dynamic>,
      );
      userRules = data.rules.map((final e) => e?.actions).toList();
      if (mounted) {
        setState(() {});
      }
    });
    SocketService().listen(SocketEvent.updateMemberPermission.name, (
      final response,
    ) {
      if (mounted) {
        context.read<ChatBloc>().add(
          ChatGotUserRules(
            GroupChatDetailGetUserRulesRequestParams(
              conversationId: widget.conversationId,
              username: EZCache.shared.getUserProfile()?.employeeId,
            ),
          ),
        );
      }
    });
  }

  void _scrollWhenFirstLoad() {
    ChatItems? messageNeedToScroll;
    if (searchMessageCreatedAt != null) {
      messageNeedToScroll = _pagingController.itemList?.firstWhereOrNull(
        (final e) => e?.createdAt == searchMessageCreatedAt,
      );
    } else {
      messageNeedToScroll = _pagingController.itemList?.lastOrNull;
    }
    Future.delayed(
      const Duration(milliseconds: 100),
      () => unawaited(
        _scrollToMessage(
          controller: _pagingController,
          messageId: messageNeedToScroll?.id,
          messageCreatedAt: messageNeedToScroll?.createdAt,
          isHighlight: searchMessageCreatedAt != null,
          callApiIfNotFound: false,
        ),
      ),
    );
  }

  Widget _buildAvatar(final BuildContext context) {
    return GestureDetector(
      onTap: () => _pushToGroupDetails(context),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(36),
        child: (conversation?.avatar?.isNotEmpty ?? false)
            ? EzCachedNetworkImage(
                imageUrl: conversation?.avatar,
                width: 36 * context.watch<FontsBloc>().textScale,
                height: 36 * context.watch<FontsBloc>().textScale,
              )
            : Container(
                width: 36 * context.watch<FontsBloc>().textScale,
                height: 36 * context.watch<FontsBloc>().textScale,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Utils.randomColor(
                        conversation?.id ?? '',
                      ).withValues(alpha: 0.5),
                      Utils.randomColor(conversation?.id ?? ''),
                    ],
                  ),
                ),
                child: Center(
                  child: Text(
                    Utils.defaultOnEmpty(conversation?.name).isEmpty
                        ? Strings.appName[0].toUpperCase()
                        : Utils.defaultOnEmpty(
                            conversation?.name,
                          ).firstOrEmpty().toUpperCase(),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.surface,
                    ),
                    textScaler: TextScaler.noScaling,
                  ),
                ),
              ),
      ),
    );
  }

  String? _getSearchTag() {
    try {
      final textBeforeCursor = messageController.plainTextEditingValue.text
          .substring(0, messageController.selection.base.offset);
      final searchTag = textBeforeCursor.substring(
        textBeforeCursor.lastIndexOf('@'),
      );
      return searchTag.replaceAll('@', '');
    } catch (_) {
      return null;
    }
  }

  void addFileBundle(
    final BuildContext context, {
    final File? file,
    required final String title,
    final Uint8List? thumbnail,
  }) {
    final fileExtension = getFileExtension(file?.path ?? '');
    pickedFileBundles.add(
      PickedFileBundle(
        name: title,
        fileExtension: fileExtension,
        image: (thumbnail != null)
            ? Image.memory(thumbnail, fit: BoxFit.cover)
            : Icon(Icons.error, color: Theme.of(context).hintColor),
        path: file?.path ?? '',
        size: file?.lengthSync() ?? 0,
      ),
    );
  }

  TypeAheadField<ChatListItemsMembersInfo?> _buildUserTagList() {
    return TypeAheadField<ChatListItemsMembersInfo?>(
      direction: VerticalDirection.up,
      hideOnEmpty: true,
      hideOnSelect: false,
      suggestionsCallback: (final typingText) async {
        if (!(conversation?.isGroup ?? true)) {
          return null;
        }
        vIsShow.value = typingText.length > 1;
        if (!typingText.contains('@')) {
          return null;
        }
        searchTag = _getSearchTag();
        if (searchTag == null) {
          return null;
        }
        final listWithTagAll =
            (conversation?.membersInfo ?? []) +
            [ChatListItemsMembersInfo(name: context.l10n.all, username: 'all')];
        final searchList = listWithTagAll
            .where(
              (final e) =>
                  ((e?.name?.toLowerCase().contains(searchTag!.toLowerCase()) ??
                          false) ||
                      (e?.username?.toLowerCase().contains(
                            searchTag!.toLowerCase(),
                          ) ??
                          false)) &&
                  e?.username != user?.employeeId,
            )
            .toList();
        return searchList.isNotEmpty ? searchList : null;
      },
      htmlController: messageController,
      isHtml: true,
      focusNode: messageFocusNode,
      builder:
          (
            final context,
            final htmlController,
            final controller,
            final focusNode,
          ) {
            return ValueListenableBuilder(
              valueListenable: emojiShowing,
              builder: (final _, final __, final ___) {
                return InputArea(
                  isExpandedSticker: isExpandedSticker,
                  userRules: userRules,
                  vIsShow: vIsShow,
                  textController: htmlController,
                  messageFocusNode: focusNode,
                  emojiOn: emojiShowing.value,
                  onTapEmojiButton: () async {
                    if (stickerData.isEmpty) {
                      context.read<ChatBloc>().add(
                        ChatGotUserSticker(
                          const ChatGetUserStickerRequestParams(),
                        ),
                      );
                    }
                    if (focusNode.hasFocus) {
                      FocusScope.of(context).unfocus();
                      await Future.delayed(const Duration(milliseconds: 100));
                    }

                    isExpandedSticker.value = false;
                    emojiShowing.value = !emojiShowing.value;
                  },
                  onTapAttachFileButton: () async {
                    GalleryChat.showGalleryChat(
                      controller: htmlController,
                      mentions: conversation?.membersInfo ?? [],
                      context,
                      userRules: userRules,
                      selectedAssetList: selectedAssetList,
                      onCompleteSelected: (final files) async {
                        selectedAssetList = [...files];
                        for (int i = 0; i < files.length; i++) {
                          final e = files[i];
                          File? file;
                          Uint8List? thumbnail;
                          if ((Platform.isIOS && e.relativePath == null) ||
                              Platform.isAndroid &&
                                  !(e.relativePath?.contains(
                                        'com.ngocdung.theadvance',
                                      ) ??
                                      false)) {
                            file = await e.originFile;
                            thumbnail = await e.thumbnailData;
                            final title = await e.titleAsync;
                            if (context.mounted) {
                              addFileBundle(
                                context,
                                file: file,
                                thumbnail: thumbnail,
                                title: title,
                              );

                              if (i == files.length - 1) {
                                Navigator.of(context).pop();
                                _onTapSend(context);
                              }
                            }
                          } else {
                            file = File(e.relativePath ?? '');
                            thumbnail = file.readAsBytesSync();
                            final title = await e.titleAsync;
                            if (context.mounted) {
                              addFileBundle(
                                context,
                                file: file,
                                thumbnail: thumbnail,
                                title: title,
                              );
                              if (i == files.length - 1) {
                                Navigator.of(context).pop();
                                _onTapSend(context);
                              }
                            }
                          }
                        }
                      },
                      onSelectedFile: (final files) {
                        pickedFileBundles.addAll(files);
                      },
                      takePictureComplete: (final path) {
                        final sendId = const Uuid().v4();
                        context.read<ChatBloc>().add(
                          ChatUploadFile(
                            params: [
                              ChatUploadFileRequestParams(
                                MultipartFile.fromFileSync(
                                  path,
                                  contentType: MediaType.parse(
                                    mime(path.toLowerCase()) ?? '',
                                  ),
                                ),
                                filename: path.split('/').lastOrNull,
                              ),
                            ],
                            sendId: sendId,
                          ),
                        );
                        Navigator.of(context).pop();
                      },
                    ).then((final val) async {
                      if (val == null) {
                        htmlController.clear();
                        selectedAssetList.clear();
                      }
                      if (val != null) {
                        if (val == AttachmentOption.file) {
                          if (context.mounted) {
                            _onTapSend(context);
                          }
                          return;
                        }
                        if (val == AttachmentOption.poll) {
                          if (context.mounted) {
                            showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              builder: (final _) => CreatePollWidget(
                                onCreated: (final pollInfo) {
                                  Navigator.of(context).pop();
                                  _onTapSend(context, pollInfo: pollInfo);
                                },
                              ),
                            );
                          }
                        }
                      }
                    });
                  },
                  onTapSendButton: () {
                    unawaited(_onTapSend(context));
                    emojiShowing.value = false;
                    isExpandedSticker.value = false;
                  },
                  isShowSendButton:
                      pickedFileBundles.isNotEmpty ||
                      (emojiShowing.value &&
                          !RegExp(r'^\s*$').hasMatch(
                            messageController.plainTextEditingValue.text.trim(),
                          )) ||
                      (forwardMessage != null &&
                          (forwardConversations.isNotEmpty ||
                              forwardMessage?.id == initForwardId)),
                  onPasteImage: (final bytes) {
                    if (bytes == null) {
                      return;
                    }

                    unawaited(_onPasteImage(bytes, context));
                  },
                  onTapRecordButton: onTapRecordButton,
                );
              },
            );
          },
      itemBuilder: (final context, final member) {
        return ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(32),
            child: (member?.avatar?.isNotEmpty ?? false)
                ? EzCachedNetworkImage(
                    imageUrl: member?.avatar,
                    width: 44,
                    height: 44,
                  )
                : Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          Utils.randomColor(
                            member?.id ?? '',
                          ).withValues(alpha: 0.5),
                          Utils.randomColor(member?.id ?? ''),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Text(
                        Utils.defaultOnEmpty(member?.name).isEmpty
                            ? Strings.appName[0].toUpperCase()
                            : Utils.defaultOnEmpty(
                                member?.name,
                              ).firstOrEmpty().toUpperCase(),
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.surface,
                            ),
                      ),
                    ),
                  ),
          ),
          title: Text(Utils.defaultOnEmpty(member?.name)),
          subtitle: Text(Utils.defaultOnEmpty(member?.username)),
        );
      },
      decorationBuilder: (final context, final child) {
        return Material(
          type: MaterialType.card,
          elevation: 4,
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          child: child,
        );
      },
      onSelected: (final value) {
        final lengthToReplace = '@$searchTag'.length;
        final newText = '@${value?.username ?? ''} ';
        messageController.replaceText(
          messageController.selection.base.offset - lengthToReplace,
          lengthToReplace,
          newText,
          null,
        );

        messageController.updateSelection(
          TextSelection.collapsed(
            offset:
                messageController.selection.base.offset + newText.length - 1,
          ),
          ChangeSource.local,
        );
      },
    );
  }

  Future<void> _onPasteImage(
    final Uint8List bytes,
    final BuildContext context, {
    final bool isCompress = true,
  }) async {
    final compressBytes = await FlutterImageCompress.compressWithList(
      bytes,
      minHeight: 1024,
      minWidth: 1024,
      quality: isCompress ? 50 : 100,
    );
    final fileName = '${DateTime.now().microsecondsSinceEpoch}.jpg';
    final multipartFile = MultipartFile.fromBytes(
      compressBytes,
      filename: fileName,
      contentType: MediaType.parse('image/jpeg'),
    );
    final sendId = const Uuid().v4();

    // add local message
    final tempDir = await getTemporaryDirectory();
    final File tempFile = await File(
      '${tempDir.path}/$fileName',
    ).writeAsBytes(compressBytes);

    final newMessage = ChatItems(
      attachment: [
        ChatItemsAttachment(
          link: tempFile.path,
          mimetype: mime(fileName.toLowerCase()) ?? '',
          originalname: fileName,
        ),
      ],
      createdAt: DateTime.now().toUtc().toIso8601String(),
      createdByInfo: ChatItemsCreatedByInfo(
        username: user?.employeeId,
        id: user?.employeeId,
      ),
      requestId: sendId,
      reactions: [],
      userSeen: [],
      mention: [],
    );
    if (searchMessageCreatedAt == null) {
      _pagingController.itemList?.insert(0, newMessage);
    }
    latestMessage = newMessage;
    // send to server
    if (context.mounted) {
      context.read<ChatBloc>().add(
        ChatUploadFile(
          params: [
            ChatUploadFileRequestParams(
              multipartFile,
              duration: recordDuration?.toString(),
            ),
          ],
          sendId: sendId,
        ),
      );
    }
  }

  Container _buildReplyMessage(final BuildContext context) {
    final isSticker = replyMessage?.sticker?.link?.isNotEmpty ?? false;
    final image =
        replyMessage?.attachment.firstOrNull?.thumbnail ??
        replyMessage?.attachment.firstOrNull?.link ??
        replyMessage?.sticker?.link;
    final isImage =
        (replyMessage?.attachment.firstOrNull?.mimetype?.contains('image') ??
            false) ||
        (replyMessage?.sticker?.mimetype?.contains('image') ?? false);
    final isVideo =
        (replyMessage?.attachment.firstOrNull?.mimetype?.contains('video') ??
            false) ||
        (replyMessage?.sticker?.mimetype?.contains('video') ?? false);
    final content = isSticker
        ? context.l10n.sticker
        : (replyMessage?.forward?.id?.isNotEmpty ?? false)
        ? (replyMessage?.forward?.content ?? '')
        : (replyMessage?.content?.isNotEmpty ?? false)
        ? replyMessage?.content ?? ''
        : replyMessage?.attachment.firstOrNull?.link ?? '';
    return Container(
      color: const Color(0xffF6F6F6),
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: <Widget>[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: EZResources.image(ImageParams(name: AppIcons.icReplyV2)),
          ),
          Container(
            width: 2,
            height: 36,
            decoration: ShapeDecoration(
              color: const Color(0xFF007E00),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isSticker ||
              (replyMessage?.attachment.firstOrNull?.link?.isNotEmpty ?? false))
            if (isImage)
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: EzCachedNetworkImage(
                    width: 40,
                    height: 40,
                    imageUrl: image,
                  ),
                ),
              )
            else if (isVideo)
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: EzCachedNetworkImage(
                    width: 40,
                    height: 40,
                    imageUrl: image,
                    errorWidget: FutureBuilder(
                      future: Utils.generateThumbnail(image ?? ''),
                      builder: (final _, final snapshot) {
                        return snapshot.hasData
                            ? Image.memory(
                                snapshot.data!,
                                width: 40,
                                height: 40,
                              )
                            : const SizedBox.shrink();
                      },
                    ),
                  ),
                ),
              ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.l10n.replyTo(
                    ((replyMessage?.forward?.id?.isNotEmpty ?? false)
                            ? replyMessage?.forward?.createdByInfo?.name
                            : replyMessage?.createdByInfo?.name) ??
                        '',
                  ),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).primaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (Utils.isHtml(replyMessage?.content ?? ''))
                  SizedBox(
                    width: double.maxFinite,
                    height: 16,
                    child: HtmlWidget(
                      Utils.defaultOnEmpty(replyMessage?.content ?? ''),
                    ),
                  )
                else if (content.isNotEmpty)
                  Text(
                    content,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).hintColor,
                    ),
                  )
                else
                  LastMessageWidget(
                    textScale: context.watch<FontsBloc>().textScale,
                    lastMessageInfo: ChatItems(
                      id: replyMessage?.id,
                      content: replyMessage?.content,
                      createdAt: replyMessage?.createdAt,
                      createdByInfo: ChatItemsCreatedByInfo(
                        username: replyMessage?.createdByInfo?.username,
                        name: replyMessage?.createdByInfo?.name,
                        avatar: replyMessage?.createdByInfo?.avatar,
                        id: replyMessage?.createdByInfo?.id,
                      ),
                      attachment: replyMessage?.attachment ?? [],
                    ),
                  ),
              ],
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              replyMessage = null;
              if (mounted) {
                setState(() {});
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Icon(Icons.close, color: Theme.of(context).primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Container _buildForwardMessage(final BuildContext context) {
    if (forwardMessage?.forward?.id?.isNotEmpty ?? false) {
      forwardMessage = forwardMessage?.copyWith(
        content: forwardMessage?.forward?.content,
        parseMode: forwardMessage?.forward?.parseMode,
        deepLink: forwardMessage?.forward?.deepLink,
        attachment: forwardMessage?.forward?.attachment,
        mention: forwardMessage?.forward?.mention
            .map((final e) => e ?? ChatItemsMention())
            .toList(),
        sticker: forwardMessage?.forward?.sticker,
      );
    }
    final isSticker = forwardMessage?.sticker?.link?.isNotEmpty ?? false;
    return Container(
      color: const Color(0xffF6F6F6),
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: <Widget>[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: EZResources.image(ImageParams(name: AppIcons.icForwardV2)),
          ),
          Container(
            width: 2,
            height: 36,
            decoration: ShapeDecoration(
              color: const Color(0xFF007E00),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isSticker ||
              (forwardMessage?.attachment.firstOrNull?.link?.isNotEmpty ??
                  false))
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child:
                    (forwardMessage?.sticker?.mimetype?.contains('video') ??
                        false)
                    ? FutureBuilder(
                        future: Utils.generateThumbnail(
                          forwardMessage?.sticker?.link ?? '',
                        ),
                        builder: (final _, final snapshot) {
                          if (snapshot.data != null) {
                            return Image.memory(
                              snapshot.data!,
                              width: 40,
                              height: 40,
                              errorBuilder: (final _, final __, final ___) =>
                                  const SizedBox.shrink(),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      )
                    : EzCachedNetworkImage(
                        width: 40,
                        height: 40,
                        imageUrl:
                            forwardMessage?.attachment.firstOrNull?.thumbnail ??
                            forwardMessage?.attachment.firstOrNull?.link ??
                            forwardMessage?.sticker?.link ??
                            '',
                        errorWidget: Center(
                          child: EZResources.image(
                            ImageParams(
                              name: AppIcons.icFileV2,
                              size: const ImageSize.square(24),
                              color: Theme.of(context).hintColor,
                            ),
                          ),
                        ),
                      ),
              ),
            ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.l10n.forwardTo(
                    forwardMessage?.createdByInfo?.name ?? '',
                  ),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).primaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  (forwardMessage?.content?.isNotEmpty ?? false)
                      ? Bidi.stripHtmlIfNeeded(forwardMessage?.content ?? '')
                      : forwardMessage?.attachment.firstOrNull?.link ??
                            (isSticker ? context.l10n.sticker : ''),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).hintColor,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              forwardMessage = null;
              initForwardId = null;
              forwardConversations.clear();
              if (mounted) {
                setState(() {});
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Icon(Icons.close, color: Theme.of(context).primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Container _buildEditMessage(final BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: <Widget>[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: EZResources.image(
              ImageParams(
                name: AppIcons.icEdit,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          SizedBox(
            height: 44,
            child: VerticalDivider(
              thickness: 2,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 8),
          if (editMessage?.attachment.firstOrNull?.link?.isNotEmpty ?? false)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: EzCachedNetworkImage(
                  width: 40,
                  height: 40,
                  imageUrl: editMessage?.attachment.firstOrNull?.link ?? '',
                ),
              ),
            ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.l10n.edit +
                      Strings.space +
                      context.l10n.message.toLowerCase(),
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Theme.of(context).primaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (Utils.isHtml(editMessage?.content ?? ''))
                  SizedBox(
                    width: double.maxFinite,
                    height: 16,
                    child: HtmlWidget(
                      Utils.defaultOnEmpty(editMessage?.content ?? ''),
                    ),
                  )
                else
                  Text(
                    (editMessage?.content?.isNotEmpty ?? false)
                        ? editMessage?.content ?? ''
                        : editMessage?.attachment.firstOrNull?.link ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              editMessage = null;
              if (mounted) {
                setState(() {});
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Icon(Icons.close, color: Theme.of(context).hintColor),
            ),
          ),
        ],
      ),
    );
  }

  final Key downListKey = UniqueKey();
  bool isReconnecting = false;
  Widget _buildMessageList(final BuildContext context) {
    if (isReconnecting) {
      return LoadingAnimationWidget.hexagonDots(
        size: 36,
        color: Theme.of(context).primaryColor,
      );
    }
    if (lastSeenMessageTime?.isNotEmpty ?? false) {
      if (searchMessageCreatedAt == null) {
        return BaseInfiniteListView<ChatItems?>(
          scrollController: autoScrollController,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 6),
          reverse: true,
          pagingController: _pagingController,
          itemBuilder: (final context, final item, final index) {
            final lastIndex = _pagingController.itemList?.length ?? 0;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (index == lastIndex - 1 ||
                    !isTheSameDay(
                      item?.createdAt,
                      _pagingController.itemList?[index + 1]?.createdAt,
                    ))
                  _buildDate(item),
                _buildMessage(
                  context,
                  index,
                  item,
                  lastIndex,
                  _pagingController,
                ),
              ],
            );
          },
          noItemsWidget: const EmptyMessage(),
          separator: const SizedBox(height: 2),
        );
      }
      return Padding(
        padding: const EdgeInsets.fromLTRB(6, 0, 6, 0),
        child: CustomScrollView(
          center: downListKey,
          physics: const ClampingScrollPhysics(),
          primary: false,
          controller: autoScrollController,
          slivers: [
            if (currentPage > 1 &&
                (_pagingControllerPrevious.itemList?.isNotEmpty ?? true))
              PagedSliverList.separated(
                pagingController: _pagingControllerPrevious,
                separatorBuilder: (final context, final index) =>
                    const SizedBox(height: 2),
                builderDelegate: PagedChildBuilderDelegate<ChatItems?>(
                  itemBuilder: (final context, final item, final index) {
                    final lastIndex =
                        _pagingControllerPrevious.itemList?.length ?? 0;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (index == lastIndex - 1 ||
                            !isTheSameDay(
                              item?.createdAt,
                              _pagingControllerPrevious
                                  .itemList?[index + 1]
                                  ?.createdAt,
                            ))
                          _buildDate(item),
                        _buildMessage(
                          context,
                          index,
                          item,
                          lastIndex,
                          _pagingControllerPrevious,
                          negativeIndex: -(index + 1),
                        ),
                      ],
                    );
                  },
                  firstPageProgressIndicatorBuilder: (final context) => Center(
                    child: LoadingAnimationWidget.hexagonDots(
                      size: 36,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  newPageProgressIndicatorBuilder: (final context) => Center(
                    child: LoadingAnimationWidget.hexagonDots(
                      size: 36,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  noItemsFoundIndicatorBuilder: (final context) =>
                      const EmptyMessage(),
                ),
              ),
            const SliverToBoxAdapter(child: SizedBox(height: 16)),
            PagedSliverList.separated(
              key: downListKey,
              pagingController: _pagingController,
              separatorBuilder: (final context, final index) =>
                  const SizedBox(height: 2),
              builderDelegate: PagedChildBuilderDelegate<ChatItems?>(
                itemBuilder: (final context, final item, final index) {
                  final lastIndex = _pagingController.itemList?.length ?? 0;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (index == 0 ||
                          !isTheSameDay(
                            item?.createdAt,
                            _pagingController.itemList?[index - 1]?.createdAt,
                          ))
                        _buildDate(item),
                      _buildMessage(
                        context,
                        index,
                        item,
                        lastIndex,
                        _pagingController,
                      ),
                    ],
                  );
                },
                firstPageProgressIndicatorBuilder: (final context) => Center(
                  child: LoadingAnimationWidget.hexagonDots(
                    size: 36,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                newPageProgressIndicatorBuilder: (final context) => Center(
                  child: LoadingAnimationWidget.hexagonDots(
                    size: 36,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                noItemsFoundIndicatorBuilder: (final context) =>
                    const EmptyMessage(),
              ),
            ),
            const SliverToBoxAdapter(child: SizedBox(height: 8)),
          ],
        ),
      );
    }
    return LoadingAnimationWidget.hexagonDots(
      size: 36,
      color: Theme.of(context).primaryColor,
    );
  }

  Widget _buildMessage(
    final BuildContext context,
    final int index,
    final ChatItems? item,
    final int lastIndex,
    final PagingController<int, ChatItems?> controller, {
    final int? negativeIndex,
  }) {
    // add mention user
    if (item?.mention.isEmpty ?? true) {
      final regex = RegExp(r'^@[a-zA-Z0-9]+$');
      final listStr = item?.content?.split(' ') ?? [];
      for (final element in listStr) {
        if (regex.hasMatch(element)) {
          final userTag = conversation?.membersInfo.firstWhereOrNull(
            (final e) => e?.username == element.substring(1),
          );
          if (userTag != null) {
            item?.mention.add(
              ChatItemsMention(
                username: userTag.username,
                label: userTag.name,
                text: '@${userTag.username}',
              ),
            );
          }
        }
      }
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: AutoScrollTag(
        key: ValueKey(negativeIndex ?? index),
        controller: autoScrollController,
        index: negativeIndex ?? index,
        highlightColor: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        child: VisibilityDetector(
          key: UniqueKey(),
          onVisibilityChanged: (final VisibilityInfo visibilityInfo) {
            try {
              final currentDateView = IntlHelper.dateFormatter2
                  .parseUTC(currentMessageViewByDate!.createdAt!)
                  .toLocal();
              final dateView = IntlHelper.dateFormatter2
                  .parseUTC(item!.createdAt!)
                  .toLocal();
              if ((dateView.isBeforeDay(currentDateView) && !isScrollDown) ||
                  (dateView.isAfterDay(currentDateView) && isScrollDown)) {
                Future.delayed(const Duration(milliseconds: 200), () {
                  isShowDate.value = false;
                });
                currentMessageViewByDate = item;
              }
            } catch (_) {}
            _markMessageSeen(item, visibilityInfo);
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 2),
            width: double.maxFinite,
            child: Row(
              children: [
                if (isMultiSelect) ...[
                  _buildSelectMessageButton(item, context),
                ],
                Expanded(
                  child: GestureDetector(
                    onTap: isMultiSelect
                        ? () {
                            if (selectedMessageList.firstWhereOrNull(
                                  (final e) => e?.id == item?.id,
                                ) !=
                                null) {
                              selectedMessageList.removeWhere(
                                (final e) => e?.id == item?.id,
                              );
                              if (selectedMessageList.isEmpty) {
                                isMultiSelect = false;
                                setState(() {});
                              }
                            } else {
                              selectedMessageList.add(item);
                            }
                            selectedMessagesCount.value =
                                selectedMessageList.length;
                          }
                        : null,
                    child: AbsorbPointer(
                      absorbing: isMultiSelect,
                      child: MessageTile(
                        taskInfo: taskInfo,
                        message: item,
                        imageUrlList: imageUrlList,
                        userRole: conversation?.conversationDetails?.role,
                        userRules: userRules,
                        previewDataList: previewDataList,
                        onSendEditedImage: (final bytes) {
                          if (!userRules.contains(
                            UserChatRule.sendImage.name,
                          )) {
                            EzToast.showToast(
                              message: context.l10n.noSendImagePermission,
                            );
                            return;
                          }
                          if (bytes == null) {
                            return;
                          }
                          unawaited(
                            _onPasteImage(bytes, context, isCompress: false),
                          );
                          Navigator.pop(context);
                        },
                        membersInfo: conversation?.membersInfo ?? [],
                        onForward: (final message) => setState(() {
                          showPinList = false;
                          replyMessage = null;
                          editMessage = null;

                          forwardMessage = message?.copyWith(
                            id: message.forward?.id ?? message.id,
                            createdByInfo:
                                message.forward?.createdByInfo?.id != null
                                ? ChatItemsCreatedByInfo(
                                    avatar:
                                        message.forward?.createdByInfo?.avatar,
                                    name: message.forward?.createdByInfo?.name,
                                    username: message
                                        .forward
                                        ?.createdByInfo
                                        ?.username,
                                    id: message.forward?.createdByInfo?.id,
                                  )
                                : message.createdByInfo,
                          );
                        }),
                        customerId: user?.employeeId,
                        isShowingAvatar: searchMessageCreatedAt == null
                            ? isShowingAvatar(
                                index,
                                showPinList
                                    ? pinMessageList
                                    : controller.itemList ?? [],
                              )
                            : isShowingName(
                                index,
                                showPinList
                                    ? pinMessageList
                                    : controller.itemList ?? [],
                              ),
                        isShowingName: searchMessageCreatedAt == null
                            ? isShowingName(
                                index,
                                showPinList
                                    ? pinMessageList
                                    : controller.itemList ?? [],
                              )
                            : isShowingAvatar(
                                index,
                                showPinList
                                    ? pinMessageList
                                    : controller.itemList ?? [],
                              ),
                        isGroup: conversation?.isGroup ?? false,
                        onShowMenuPopup:
                            ({
                              required final bool isShowing,
                              required final ChatItems? message,
                            }) {
                              if (isShowing) {
                                isExpandedSticker.value = false;
                                emojiShowing.value = false;
                                blurValue.value = 23;
                                context.read<ChatBloc>().add(
                                  ChatGotUserSeen(
                                    ChatGetUserSeenRequestParams(
                                      conversationId: message?.conversationId,
                                      messageCreatedAt: message?.createdAt,
                                      page: 1,
                                    ),
                                    getTotal: true,
                                  ),
                                );
                              } else {
                                blurValue.value = 0;
                              }
                              // if (mounted) {
                              //   setState(() {});
                              // }
                            },
                        onRemoved: () => context.read<ChatBloc>().add(
                          ChatMessageRemoved(item?.id ?? '', animation: true),
                        ),
                        onRemovedSuccess: (final messId) {
                          _pagingController.itemList?.removeWhere(
                            (final e) => e?.id == messId,
                          );
                          _pagingControllerPrevious.itemList?.removeWhere(
                            (final e) => e?.id == messId,
                          );
                          setState(() {});
                        },
                        onSwipeLeft: (final mess) {
                          showPinList = false;
                          _onReplyMessage(mess);
                        },
                        onReply: (final mess) {
                          showPinList = false;
                          _onReplyMessage(mess);
                        },
                        onEdit: (final mess) {
                          replyMessage = null;
                          forwardMessage = null;
                          editMessage = mess;
                          if (Utils.isHtml(editMessage?.content ?? '')) {
                            final delta = HtmlToDelta().convert(
                              editMessage?.content ?? '',
                            );
                            final document = Document.fromDelta(delta);
                            messageController.document = document;
                          } else {
                            messageController.clear();
                            messageController.document = Document()
                              ..insert(0, editMessage?.content);
                          }
                          messageController.moveCursorToEnd();
                          if (mounted) {
                            setState(() {});
                          }
                          messageFocusNode.requestFocus();
                        },
                        onTapRootMessage: () async {
                          FocusScope.of(context).unfocus();
                          if (searchMessageCreatedAt == null) {
                            await _scrollToMessage(
                              controller: controller,
                              messageId: item?.replyInfo?.id,
                              messageCreatedAt: item?.replyInfo?.createdAt,
                              isNegative: negativeIndex != null,
                            );
                          } else {
                            // work around when use 2 lists
                            searchMessageCreatedAt = item?.replyInfo?.createdAt;
                            _pagingController.refresh();
                          }
                        },
                        onTapUserTag: (final value) async {
                          final taggedUserName = value.split('_').lastOrNull;
                          final taggedUser = conversation?.membersInfo
                              .firstWhereOrNull(
                                (final element) =>
                                    element?.username == taggedUserName,
                              );
                          if (taggedUser != null) {
                            context.router.push(
                              StoryPersonRoute(
                                codeUser: Utils.defaultOnEmpty(taggedUserName),
                              ),
                            );
                          } else {
                            Alert.showAlert(
                              AlertParams(
                                context,
                                context.l10n.searchInfoFailure,
                              ),
                            );
                          }
                        },
                        onReact: (final mess, final icon) {
                          context.read<ChatBloc>().add(
                            ChatReacted(
                              ChatReactRequestParams(
                                action: ChatAction.create.name,
                                id: mess?.id,
                                conversationId: mess?.conversationId,
                                icon: icon,
                              ),
                            ),
                          );
                          Navigator.of(context).pop();
                        },
                        onTapForwardMessage: () async {
                          context.router.push(
                            StoryPersonRoute(
                              codeUser: Utils.defaultOnEmpty(
                                item?.forward?.createdBy,
                              ),
                            ),
                          );
                        },
                        onCopiedSuccess: (final value) {
                          imageClipboard = value;
                        },
                        isPin:
                            pinMessageList.firstWhereOrNull(
                              (final pinMess) => pinMess?.id == item?.id,
                            ) !=
                            null,
                        onPin: (final mess) {
                          context.read<ChatBloc>().add(
                            ChatPinnedMessage(
                              ChatPinMessageRequestParams(
                                conversationId: mess?.conversationId,
                                messageId: mess?.id,
                              ),
                            ),
                          );
                        },
                        onUnpin: (final mess) {
                          context.read<ChatBloc>().add(
                            ChatUnpinnedMessage(
                              ChatUnpinMessageRequestParams(
                                conversationId: mess?.conversationId,
                                messageIds: [mess?.id],
                              ),
                            ),
                          );
                        },
                        onRetractVote: (final params) => context
                            .read<ChatBloc>()
                            .add(ChatVotePolled(params)),
                        onStopVote: (final params) => context
                            .read<ChatBloc>()
                            .add(ChatUpdatePolled(params)),
                        onRetry: (final mess) => _onRetry(mess),
                        onShowUserSeen: (final mess, final position) =>
                            _onShowUserSeen(mess, position),
                        onMultiSelect: (final message) => setState(() {
                          messageFocusNode.unfocus();
                          isMultiSelect = true;
                          selectedMessageList.add(message);
                          selectedMessagesCount.value =
                              selectedMessageList.length;
                        }),
                        onTapSticker: (final stickerSetId) {
                          if (stickerData.isEmpty) {
                            context.read<ChatBloc>().add(
                              ChatGotUserSticker(
                                const ChatGetUserStickerRequestParams(),
                              ),
                            );
                          }
                          _showStickerView(stickerSetId);
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ValueListenableBuilder<int> _buildSelectMessageButton(
    final ChatItems? item,
    final BuildContext context,
  ) {
    return ValueListenableBuilder(
      valueListenable: selectedMessagesCount,
      builder: (final BuildContext _, final value, final Widget? __) {
        final isCheck = selectedMessageList.any((final e) => e?.id == item?.id);

        return Padding(
          padding: const EdgeInsets.only(right: 10),
          child: InkWell(
            onTap: () {
              final isCheck = selectedMessageList.any(
                (final e) => e?.id == item?.id,
              );
              if (!isCheck) {
                selectedMessageList.add(item);
              } else {
                selectedMessageList.removeWhere((final e) => e?.id == item?.id);
                if (selectedMessageList.isEmpty) {
                  isMultiSelect = false;
                  setState(() {});
                }
              }
              selectedMessagesCount.value = selectedMessageList.length;
            },
            child: isCheck
                ? Container(
                    height: 20,
                    width: 20,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Theme.of(context).primaryColor,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.surface,
                      ),
                    ),
                    child: Center(
                      child: EZResources.image(
                        ImageParams(
                          name: AppIcons.icCheck,
                          size: const ImageSize(9, 7),
                          color: Theme.of(context).colorScheme.surface,
                        ),
                      ),
                    ),
                  )
                : Container(
                    height: 20,
                    width: 20,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.surface,
                      ),
                    ),
                  ),
          ),
        );
      },
    );
  }

  Future<void> _onShowUserSeen(
    final ChatItems? mess,
    final RelativeRect position,
  ) async {
    final totalUserSeen = (await ChatBody.completer.future)?.total ?? 0;
    messageGetUserSeen = mess;
    _userSeenPagingController.refresh();
    Future.delayed(Durations.short4, () {
      blurValue.value = 23;
      if (mounted) {
        showMenu<void>(
          context: context,
          color: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          shadowColor: Colors.transparent,
          popUpAnimationStyle: AnimationStyle.noAnimation,
          constraints: BoxConstraints(
            maxWidth: min(MediaQuery.sizeOf(context).width * 0.8, 335),
          ),
          position: position,
          items: [
            const PopupMenuItem(height: 2, child: SizedBox()),
            PopupMenuItem(
              padding: EdgeInsets.zero,
              child: MessageTile.previewMessage(
                message: mess,
                customerId: user?.employeeId,
                previewDataList: previewDataList,
              ),
            ),
            const PopupMenuItem(height: 6, child: SizedBox()),
            PopupMenuItem(
              padding: EdgeInsets.zero,
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  color: Theme.of(
                    context,
                  ).colorScheme.surface.withValues(alpha: 0.8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.arrow_back,
                      color: Color(0xff010101),
                      size: 20,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      context.l10n.previous,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const PopupMenuItem(height: 4, child: SizedBox()),
            PopupMenuItem(
              padding: EdgeInsets.zero,
              child: Container(
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  color: Theme.of(
                    context,
                  ).colorScheme.surface.withValues(alpha: 0.8),
                ),
                child: SizedBox(
                  height: min(
                    totalUserSeen * 65,
                    MediaQuery.sizeOf(context).height / 2,
                  ),
                  width: MediaQuery.sizeOf(context).width - 60,
                  child: BaseInfiniteListView<ChatGetUserSeenDocs?>(
                    pagingController: _userSeenPagingController,
                    padding: EdgeInsets.zero,
                    separator: const Divider(),
                    itemBuilder: (final context, final item, final index) {
                      return GestureDetector(
                        onTap: () {
                          context.router.push(
                            StoryPersonRoute(
                              codeUser: Utils.defaultOnEmpty(item?.username),
                            ),
                          );
                        },
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              child: ListTile(
                                dense: true,
                                leading: ClipOval(
                                  child: EzCachedNetworkImage(
                                    imageUrl: item?.avatar,
                                    width: 28,
                                    height: 28,
                                    errorWidget: RandomAvatarWidget(
                                      name: item?.name,
                                      width: 28,
                                      height: 28,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  item?.name ?? '',
                                  style: const TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                subtitle: Text(
                                  item?.username ?? '',
                                  style: const TextStyle(fontSize: 12),
                                ),
                                trailing: Text(
                                  mess?.reactions
                                          .firstWhereOrNull(
                                            (final element) =>
                                                element?.username ==
                                                item?.username,
                                          )
                                          ?.icon ??
                                      '',
                                  style: const TextStyle(fontSize: 24),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ).whenComplete(() {
          blurValue.value = 0;

          // if (mounted) {
          //   setState(() {});
          // }
        });
      }
    });
  }

  Future<void> _scrollToMessage({
    required final PagingController<int, ChatItems?> controller,
    required final String? messageId,
    required final String? messageCreatedAt,
    final bool callApiIfNotFound = true,
    final bool isHighlight = true,
    final bool isNegative = false,
  }) async {
    int? rootIndex = controller.itemList?.indexWhere(
      (final element) => element?.id == messageId,
    );
    if (rootIndex == -1) {
      rootIndex = null;
    }

    if (rootIndex != null) {
      await autoScrollController.scrollToIndex(
        isNegative ? -(rootIndex + 1) : rootIndex,
        preferPosition: searchMessageCreatedAt != null
            ? AutoScrollPosition.begin
            : AutoScrollPosition.end,
      );
      if (isHighlight) {
        await autoScrollController.highlight(
          isNegative ? -(rootIndex + 1) : rootIndex,
          highlightDuration: const Duration(seconds: 60),
        );
      }
    } else {
      if (callApiIfNotFound) {
        searchMessageCreatedAt = messageCreatedAt;
        _pagingController.refresh();
      }
    }
  }

  Future<void> _scrollToEnd({
    required final PagingController<int, ChatItems?> controller,
  }) async {
    int? rootIndex = controller.itemList?.indexWhere(
      (final element) =>
          element?.id == latestMessage?.id || element?.id == null,
    );
    if (rootIndex == 0 && searchMessageCreatedAt == null) {
      if (autoScrollController.hasClients) {
        await autoScrollController.animateTo(
          0,
          duration: Durations.medium2,
          curve: Curves.fastOutSlowIn,
        );
      }

      return;
    }
    if (rootIndex == -1) {
      rootIndex = null;
    }
    if (rootIndex != null) {
      await autoScrollController.scrollToIndex(
        rootIndex,
        preferPosition: rootIndex != 0
            ? AutoScrollPosition.begin
            : AutoScrollPosition.middle,
      );
    } else {
      searchMessageCreatedAt = null;
      createdAtBefore = null;
      setState(() {});
      await Future.delayed(const Duration(milliseconds: 100));
      _pagingController.refresh();
    }
  }

  void _onReplyMessage(final ChatItems? mess) {
    editMessage = null;
    forwardMessage = null;
    replyMessage = mess?.copyWith(
      id: mess.forward?.id ?? mess.id,
      content: mess.forward?.content ?? mess.content,
      parseMode: mess.forward?.parseMode ?? mess.parseMode,
      deepLink: mess.forward?.deepLink ?? mess.deepLink,
      attachment: (mess.forward?.attachment.isNotEmpty ?? false)
          ? mess.forward?.attachment
          : mess.attachment,
      mention: (mess.forward?.mention.isNotEmpty ?? false)
          ? mess.forward?.mention
          : mess.mention,
      sticker: mess.forward?.sticker ?? mess.sticker,
    );
    if (mounted) {
      setState(() {});
    }
    messageFocusNode.requestFocus();
  }

  void _markMessageSeen(
    final ChatItems? item,
    final VisibilityInfo visibilityInfo,
  ) {
    if (unreadCount.value <= 0) {
      timerSaveLastSeen?.cancel();
      timerSaveLastSeen = Timer(const Duration(milliseconds: 500), () {
        // check if message is among the 10 most recent messages
        // then save as last seen message
        if (searchMessageCreatedAt == null &&
            (((_pagingController.itemList?.length ?? 0) <= 10) ||
                (_pagingController.itemList
                        ?.getRange(0, 10)
                        .any((final e) => e?.id == item?.id) ??
                    false))) {
          EZCache.shared.saveLastSeenMessage(
            conversation?.id ?? '',
            lastSeenMessageTime,
          );
        } else {
          EZCache.shared.saveLastSeenMessage(
            conversation?.id ?? '',
            item?.createdAt,
          );
        }
      });
    }
    if (mentionCount.value > 0) {
      final indexTagMessage =
          conversation?.conversationDetails?.mentionMessages?.indexWhere(
            (final element) => element?.id == item?.id,
          ) ??
          -1;
      if (visibilityInfo.visibleFraction * 100 >= 20 && indexTagMessage != -1) {
        mentionCount.value--;
        conversation?.conversationDetails?.mentionMessages?.removeAt(
          indexTagMessage,
        );

        context.read<ChatBloc>().add(
          ChatConversationDetailsUpdated(
            isUpdateMentionMessage: true,
            ChatConversationDetailsUpdateRequestParams(
              conversationId: conversation?.id,
              conversationType: ConversationType.internal.name,
              mentionMessages: conversation
                  ?.conversationDetails
                  ?.mentionMessages
                  ?.map(
                    (final e) => MentionMessageRequestParams(
                      id: e?.id,
                      createdAt: e?.createdAt,
                    ),
                  )
                  .toList(),
            ),
          ),
        );
      }
    }
    final lastSeenMessage = _pagingController.itemList?.firstWhereOrNull(
      (final element) => element?.id == lastSeenMessageId,
    );
    bool isAfterLastSeen = false;

    try {
      isAfterLastSeen = DateTime.parse(
        item!.createdAt!,
      ).isAfter(DateTime.parse(lastSeenMessage!.createdAt!));
    } catch (_) {
      isAfterLastSeen = true;
    }
    if (visibilityInfo.visibleFraction * 100 >= 20 &&
        isAfterLastSeen &&
        !seenMessageIdList.contains(item?.id)) {
      if (unreadCount.value > 0) {
        if (item?.id == conversation?.lastMessageInfo?.id) {
          unreadCount.value = 0;
          readMessageCount = conversation?.unreadCount ?? 0;
        } else {
          unreadCount.value--;
          readMessageCount++;
        }
      }

      seenMessageIdList.add(item?.id);

      if (mounted &&
          item?.createdAt != null &&
          DateTime.parse(
            item!.createdAt!,
          ).isAfter(DateTime.parse(lastSeenMessageTime!)) &&
          item.id != null) {
        timer?.cancel();
        lastSeenMessageTime = item.createdAt;
        lastSeenMessageId = item.id;

        Future.delayed(const Duration(milliseconds: 200), () {
          final currentContext = getIt<AppRouter>().navigatorKey.currentContext;
          if (currentContext != null && currentContext.mounted) {
            currentContext.read<GeneralBloc>().add(
              GeneralUpdateUnreadCount(
                conversationId: widget.conversationId,
                unreadCount: unreadCount.value,
              ),
            );
          }
        });
        timer = Timer(
          Duration(
            milliseconds: lastSeenMessageId == latestMessage?.id ? 0 : 3000,
          ),
          () {
            try {
              context.read<ChatBloc>().add(
                ChatConversationDetailsUpdated(
                  ChatConversationDetailsUpdateRequestParams(
                    conversationId: conversation?.id,
                    lastSeenMessageId: item.id,
                    lastSeenAt: item.createdAt,
                    conversationType: ConversationType.internal.name,
                    readMessageCount: readMessageCount,
                    mentionMessages: conversation
                        ?.conversationDetails
                        ?.mentionMessages
                        ?.map(
                          (final e) => MentionMessageRequestParams(
                            id: e?.id,
                            createdAt: e?.createdAt,
                          ),
                        )
                        .toList(),
                  ),
                ),
              );
            } catch (_) {}
          },
        );
      }
    }
  }

  Widget _buildMentionMessageButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: ValueListenableBuilder(
        valueListenable: mentionCount,
        builder: (final _, final count, final ___) {
          if (count > 0) {
            return Badge(
              isLabelVisible: (mentionCount.value) > 0,
              alignment: Alignment.topCenter,
              offset: Offset.zero,
              backgroundColor: Theme.of(context).primaryColor,
              label: Text(mentionCount.value.toString()),
              child: FloatingActionButton.small(
                heroTag: 'btnMentionMessage',
                onPressed: () async {
                  mentionCount.value--;
                  if (conversation
                          ?.conversationDetails
                          ?.mentionMessages
                          ?.isNotEmpty ??
                      false) {
                    final mess = conversation
                        ?.conversationDetails
                        ?.mentionMessages
                        ?.firstOrNull;
                    searchMessageCreatedAt = mess?.createdAt;
                    _pagingController.refresh();
                    conversation?.conversationDetails?.mentionMessages
                        ?.removeAt(0);
                    context.read<ChatBloc>().add(
                      ChatConversationDetailsUpdated(
                        isUpdateMentionMessage: true,
                        ChatConversationDetailsUpdateRequestParams(
                          conversationId: conversation?.id,
                          conversationType: ConversationType.internal.name,
                          mentionMessages: conversation
                              ?.conversationDetails
                              ?.mentionMessages
                              ?.map(
                                (final e) => MentionMessageRequestParams(
                                  id: e?.id,
                                  createdAt: e?.createdAt,
                                ),
                              )
                              .toList(),
                        ),
                      ),
                    );
                  }
                },
                backgroundColor: Colors.transparent,
                foregroundColor: Theme.of(context).primaryColor,
                child: SizedBox(
                  width: 34,
                  height: 34,
                  child: Stack(
                    children: [
                      Positioned(
                        left: 0,
                        top: 0,
                        child: Container(
                          width: 34,
                          height: 34,
                          decoration: const ShapeDecoration(
                            color: Color(0xFFF3F3F3),
                            shape: OvalBorder(
                              side: BorderSide(
                                width: 0.35,
                                color: Color(0xFFE3E3E3),
                              ),
                            ),
                          ),
                          child: Center(
                            child: Text(
                              '@',
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w700,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  ValueListenableBuilder<bool> _buildScrollToEndButton() {
    return ValueListenableBuilder(
      valueListenable: isAtTheBottomList,
      builder: (final context, final value, final child) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300), // Animation duration
          transitionBuilder:
              (final Widget child, final Animation<double> animation) {
                return SizeTransition(sizeFactor: animation, child: child);
              },
          child: (!value || (unreadCount.value) > 0)
              ? Align(
                  alignment: Alignment.bottomRight,
                  child: ValueListenableBuilder(
                    valueListenable: unreadCount,
                    builder: (final _, final count, final child) => Badge(
                      key: const ValueKey<bool>(true),
                      isLabelVisible: (unreadCount.value) > 0,
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      alignment: Alignment.topCenter,
                      offset: Offset.zero,
                      backgroundColor: Theme.of(context).primaryColor,
                      label: Text(unreadCount.value.toString()),
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: count.toString().length * 5,
                        ),
                        child: FloatingActionButton.small(
                          heroTag: 'btnScrollToEnd',
                          onPressed: () async {
                            if (latestMessage?.id != lastSeenMessageId ||
                                (unreadCount.value) > 0) {
                              context.read<GeneralBloc>().add(
                                GeneralUpdateUnreadCount(
                                  conversationId: widget.conversationId,
                                  unreadCount: 0,
                                ),
                              );
                              context.read<ChatListBloc>().add(
                                ChatListMarkAsRead(
                                  ChatListMarkAsReadRequestParams([
                                    conversation?.id,
                                  ]),
                                ),
                              );
                            } else {
                              await _scrollToEnd(controller: _pagingController);
                            }
                            unreadCount.value = 0;
                          },
                          backgroundColor: Colors.transparent,
                          foregroundColor: Theme.of(context).primaryColor,
                          child: EZResources.image(
                            ImageParams(name: AppIcons.icScrollEnd),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildDate(final ChatItems? item) {
    String dateString = '';
    final date = item?.createdAt != null
        ? IntlHelper.dateFormatter2.parseUTC(item!.createdAt!).toLocal()
        : null;

    if (date == null) {
      return const SizedBox.shrink();
    }

    if (IntlHelper.convertUTCToddMMyyyy(DateTime.now().toIso8601String()) ==
        dateString) {
      dateString = context.l10n.today;
    } else {
      // check if year is current year
      if (date.year != DateTime.now().year) {
        dateString = '${date.day}/${date.month}/${date.year}';
      } else {
        dateString = '${date.day} ${context.l10n.month} ${date.month}';
      }
    }

    return Center(
      child: IntrinsicWidth(
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 6),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
          decoration: ShapeDecoration(
            color: const Color(0x4C007E00),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(60),
            ),
          ),
          child: Center(
            child: Text(
              dateString,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.surface,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _onRetry(final ChatItems? message) async {
    // send text message
    if (message?.attachment.isEmpty ?? true) {
      final sendId = message?.requestId;

      if (context.mounted) {
        RootMessageRequestParams? rootMessage;
        if (message?.replyInfo != null) {
          rootMessage = RootMessageRequestParams(
            content: message?.replyInfo?.content,
            createdAt: message?.replyInfo?.createdAt,
            createdBy: message?.replyInfo?.createdByInfo?.username,
            id: message?.replyInfo?.id,
            attachment: [],
          );
        }
        if (message?.forward != null) {
          rootMessage = RootMessageRequestParams(
            content: message?.forward?.content,
            createdAt: message?.forward?.createdAt,
            createdBy: message?.forward?.createdByInfo?.username,
            id: message?.forward?.id,
            attachment: [],
          );
        }
        context.read<ChatBloc>().add(
          ChatSent(
            ChatSendRequestParams(
              conversationId: conversation?.id,
              content: message?.content ?? '',
              parseMode: Utils.isHtml(message?.content ?? '') ? 'html' : 'text',
              requestId: sendId,
              attachment: [],
              rootMessage: rootMessage,
              mention:
                  message?.mention
                      .map(
                        (final e) => MentionUserRequestParams(
                          username: e?.username,
                          label: e?.label,
                          text: e?.text,
                        ),
                      )
                      .toList() ??
                  [],
              pollInfo: message?.pollInfo != null
                  ? ChatPollRequestParams(
                      title: message?.pollInfo?.title,
                      anonymous: message?.pollInfo?.anonymous,
                      multipleResponse: message?.pollInfo?.multipleResponse,
                      options: message?.pollInfo?.options
                          .map((final e) => OptionRequestParams(text: e?.text))
                          .toList(),
                    )
                  : null,
            ),
          ),
        );
      }
    }

    // send media message
    if (message?.attachment.isNotEmpty ?? false) {
      final sendId = message?.requestId;

      final attachments =
          message?.attachment
              .map(
                (final element) => PickedFileBundle(
                  name: element?.originalname ?? '',
                  image: const SizedBox.shrink(),
                  fileExtension: getFileExtension(element?.originalname ?? ''),
                  path: element?.link ?? '',
                  size: 0,
                ),
              )
              .toList() ??
          [];

      final List<ChatUploadFileRequestParams> compressFiles = [];
      for (final element in attachments) {
        Uint8List? compressBytes;
        try {
          if ((mime(element.path.toLowerCase())?.contains(Strings.video) ??
                  false) &&
              element.size > 1024 * 1024 * 5) {
            final mediaInfo = await VideoCompress.compressVideo(
              element.path,
              quality: VideoQuality.Res1920x1080Quality,
            );

            if (mediaInfo!.file != null) {
              compressBytes = mediaInfo.file!.readAsBytesSync();
            }
          }
          if (mime(element.path.toLowerCase())?.contains(Strings.image) ??
              false) {
            compressBytes = await FlutterImageCompress.compressWithFile(
              File(element.path).absolute.path,
              minHeight: 1024,
              minWidth: 1024,
            );
          }
        } catch (e) {
          compressBytes = null;
        }

        compressFiles.add(
          ChatUploadFileRequestParams(
            MultipartFile.fromBytes(
              compressBytes ?? File(element.path).readAsBytesSync(),
              filename: element.name,
              contentType: MediaType.parse(
                mime(element.path.toLowerCase()) ?? '',
              ),
            ),
            filename: element.name,
            duration: element.duration.toString(),
            waveform: jsonEncode(element.waveForm ?? []),
          ),
        );
      }
      if (mounted) {
        context.read<ChatBloc>().add(
          ChatUploadFile(
            params: compressFiles,
            sendId: sendId ?? const Uuid().v4(),
          ),
        );
      }
    }
  }

  bool isAttributesNotEmpty(final List<Map<String, dynamic>> dataList) {
    for (final item in dataList) {
      if (item.containsKey('attributes') &&
          item['attributes'] is Map &&
          // ignore: avoid_dynamic_calls
          item['attributes'].isNotEmpty) {
        return true;
      }
    }
    return false;
  }

  Future<void> _onTapSend(
    final BuildContext context, {
    final ChatPollRequestParams? pollInfo,
  }) async {
    if (!userRules.contains(UserChatRule.sendImage.name) &&
        pickedFileBundles.any(
          (final file) => file.fileExtension == FileExtensionType.image,
        )) {
      EzToast.showShortToast(message: context.l10n.noSendImagePermission);
      return;
    }
    if (!userRules.contains(UserChatRule.sendVideo.name) &&
        pickedFileBundles.any(
          (final file) => file.fileExtension == FileExtensionType.video,
        )) {
      EzToast.showShortToast(message: context.l10n.noSendVideoPermission);
      return;
    }
    if (!userRules.contains(UserChatRule.sendAudio.name) &&
        pickedFileBundles.any(
          (final file) => file.fileExtension == FileExtensionType.audio,
        )) {
      EzToast.showShortToast(message: context.l10n.noSendAudioPermission);
      return;
    }
    String content = '';
    String sendId = const Uuid().v4();
    // add mention user
    final obs = messageController.document.toDelta().toJson();
    final result = isAttributesNotEmpty(obs);
    if (result) {
      final filteredDeltaJson = obs.expand((final op) {
        if (op.containsKey('insert') &&
            op['attributes'] != null &&
            // ignore: avoid_dynamic_calls
            op['attributes']['link'] != null) {
          // ignore: avoid_dynamic_calls
          final link = op['attributes']['link'].toString();
          // ignore: avoid_dynamic_calls
          final checkBold = op['attributes']['bold'] != null;
          // ignore: avoid_dynamic_calls
          final checkItalic = op['attributes']['italic'] != null;
          if (link.startsWith('https')) {
            return [
              {
                if (checkBold) 'attributes': {'bold': true},
                if (checkItalic) 'attributes': {'italic': true},
                'insert': link,
              },
            ];
          }
        }
        return [op];
      }).toList();
      final converter = QuillDeltaToHtmlConverter(
        filteredDeltaJson,
        ConverterOptions(),
      );
      content = converter
          .convert()
          .replaceFirst(RegExp('<p>'), '')
          .replaceFirst(RegExp('</p>'), '', converter.convert().length - 4);
    } else {
      content = messageController.plainTextEditingValue.text.trim();
    }
    final regex = RegExp(r'^@[a-zA-Z0-9]+$');
    final listStr = messageController.plainTextEditingValue.text.split(' ');
    for (final element in listStr) {
      if (regex.hasMatch(element)) {
        final userTag = conversation?.membersInfo.firstWhereOrNull(
          (final e) => e?.username == element.substring(1),
        );
        if (userTag != null) {
          tagUserList.add(userTag);
        }
      }
    }
    final clearContent = content
        .replaceAll('&#47;', '/')
        .replaceAll('&amp;', '&');
    final iLast = clearContent.lastIndexOf(RegExp('</p>'));
    if (editMessage != null ||
        forwardMessage != null && forwardMessage?.id != initForwardId) {
      if (editMessage != null) {
        // flow edit message
        context.read<ChatBloc>().add(
          ChatMessageEdited(
            ChatMessageEditRequestParams(
              messageId: editMessage?.id,
              message: MessageEditRequestParams(
                clearContent.substring(0, iLast != -1 ? iLast : null),
                tagUserList
                    .map(
                      (final e) => MentionUserRequestParams(
                        username: e?.username,
                        label: e?.name,
                        text: '@${e?.username}',
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        );
      }

      // flow forward message
      if (forwardMessage != null) {
        if (forwardConversations.isEmpty) {
          EzToast.showShortToast(
            message: context.l10n.pleaseSelectConversation,
          );
          return;
        }
        if (forwardConversations.length > 1) {
          for (final conversation in forwardConversations) {
            needUpdateConversationId.add(conversation?.id);
            if (selectedMessageList.isNotEmpty) {
              sendId = forwardMultiMessages(sendId, context, conversation);
            } else {
              sendId = const Uuid().v4();
              context.read<ChatBloc>().add(
                ChatSent(
                  ChatSendRequestParams(
                    conversationId: conversation?.id,
                    content: messageAction == ChatAction.forward.name
                        ? ''
                        : content,
                    requestId: sendId,
                    attachment: [],
                    rootMessage: rootMessage,
                    action: messageAction,
                    mention: [],
                    parseMode: Utils.isHtml(rootMessage?.content ?? '')
                        ? 'html'
                        : 'text',
                  ),
                ),
              );
              if (content.isNotEmpty) {
                context.read<ChatBloc>().add(
                  ChatSent(
                    ChatSendRequestParams(
                      conversationId: conversation?.id,
                      content: clearContent.substring(
                        0,
                        iLast != -1 ? iLast : null,
                      ),
                      requestId: ChatAction.forward.name,
                      attachment: [],
                      action: ChatAction.create.name,
                      mention: tagUserList
                          .map(
                            (final e) => MentionUserRequestParams(
                              username: e?.username,
                              label: e?.name,
                              text: '@${e?.username}',
                            ),
                          )
                          .toList(),
                      parseMode: Utils.isHtml(content) ? 'html' : 'text',
                    ),
                  ),
                );
              }
            }
          }
        } else {
          needUpdateConversationId.add(forwardConversations.firstOrNull?.id);
          if (selectedMessageList.isNotEmpty) {
            sendId = forwardMultiMessages(
              sendId,
              context,
              forwardConversations.firstOrNull,
            );
          } else {
            EZCache.shared.saveDraftMessage(
              widget.conversationId ?? '',
              Strings.empty,
            );
            await context.router.popAndPush(
              ChatRoute(
                conversationId: forwardConversations.firstOrNull?.id,
                forwardMessage: forwardMessage,
                defaultText: content,
              ),
            );
          }
        }
        selectedMessageList.clear();
      }
    } else {
      if (needUpdateConversationId.contains(widget.conversationId)) {
        needUpdateConversationId.remove(widget.conversationId);
        needUpdateConversationId.add(widget.conversationId);
      }
      if (pickedFileBundles.isEmpty) {
        sendId = const Uuid().v4();
        // add local message
        final newMessage = ChatItems(
          content: clearContent.substring(0, iLast != -1 ? iLast : null),
          parseMode:
              Utils.isHtml(
                clearContent.substring(0, iLast != -1 ? iLast : null),
              )
              ? 'html'
              : 'text',
          createdAt: DateTime.now().toUtc().toIso8601String(),
          createdByInfo: ChatItemsCreatedByInfo(
            username: user?.employeeId,
            id: user?.employeeId,
          ),
          requestId: sendId,
          reactions: [],
          attachment: [],
          userSeen: [],
          forward: ChatItemsForwardFrom(
            content: forwardMessage?.content,
            createdByInfo: ChatItemsForwardFromCreatedByInfo(
              username: forwardMessage?.createdByInfo?.username,
              id: forwardMessage?.createdByInfo?.id,
              avatar: forwardMessage?.createdByInfo?.avatar,
              name: forwardMessage?.createdByInfo?.name,
            ),
            createdBy: forwardMessage?.createdByInfo?.username,
            id: forwardMessage?.id,
            attachment: forwardMessage?.attachment ?? [],
            mention: forwardMessage?.mention ?? [],
          ),
          mention: tagUserList
              .map(
                (final e) => ChatItemsMention(
                  username: e?.username,
                  label: e?.name,
                  text: '@${e?.username}',
                ),
              )
              .toList(),
          replyInfo: ChatItemsReplyFrom(
            content: replyMessage?.content,
            createdByInfo: ChatItemsReplyFromCreatedByInfo(
              username: replyMessage?.createdByInfo?.username,
              id: replyMessage?.createdByInfo?.id,
              avatar: replyMessage?.createdByInfo?.avatar,
              name:
                  replyMessage?.forward?.createdByInfo?.name ??
                  replyMessage?.createdByInfo?.name,
            ),
            createdBy: replyMessage?.createdByInfo?.username,
            id: replyMessage?.id,
            attachment: [],
          ),
          sticker: forwardMessage?.sticker ?? replyMessage?.sticker,
        );

        if (searchMessageCreatedAt == null) {
          _pagingController.itemList?.insert(0, newMessage);
        }

        // send text message
        if (context.mounted &&
                messageController.plainTextEditingValue.text
                    .trim()
                    .isNotEmpty ||
            pollInfo != null) {
          final sticker = (replyMessage?.sticker?.id?.isNotEmpty ?? false)
              ? StickerRequestParams(
                  link: replyMessage?.sticker?.link,
                  mimetype: replyMessage?.sticker?.mimetype,
                  width: replyMessage?.sticker?.width,
                  height: replyMessage?.sticker?.height,
                  id: replyMessage?.sticker?.id,
                  stickerSetId: replyMessage?.sticker?.stickerSetId,
                  size: replyMessage?.sticker?.size,
                )
              : null;
          context.read<ChatBloc>().add(
            ChatSent(
              ChatSendRequestParams(
                conversationId: conversation?.id,
                content: pollInfo == null
                    ? clearContent.substring(0, iLast != -1 ? iLast : null)
                    : '',
                requestId: sendId,
                attachment: [],
                rootMessage: messageAction == ChatAction.reply.name
                    ? rootMessage
                    : null,
                action: messageAction == ChatAction.forward.name
                    ? ChatAction.create.name
                    : messageAction,
                mention: tagUserList
                    .map(
                      (final e) => MentionUserRequestParams(
                        username: e?.username,
                        label: e?.name,
                        text: '@${e?.username}',
                      ),
                    )
                    .toList(),
                pollInfo: pollInfo != null
                    ? ChatPollRequestParams(
                        title: pollInfo.title,
                        anonymous: pollInfo.anonymous,
                        multipleResponse: pollInfo.multipleResponse,
                        options: pollInfo.options,
                      )
                    : null,
                sticker: messageAction == ChatAction.create.name
                    ? sticker
                    : null,
                parseMode: newMessage.parseMode ?? 'text',
              ),
            ),
          );
        }
      }

      // send media message
      if (pickedFileBundles.isNotEmpty) {
        sendId = const Uuid().v4();
        // add local message
        final List<ChatItemsAttachment> localAttachments = [];

        for (int i = 0; i < pickedFileBundles.length; i++) {
          final element = pickedFileBundles[i];
          String? thumbnailPath;
          if (element.fileExtension == FileExtensionType.video) {
            try {
              final String tempPath = (await getTemporaryDirectory()).path;

              thumbnailPath = await VideoThumbnail.thumbnailFile(
                video: element.path,
                imageFormat: ImageFormat.JPEG,
                thumbnailPath: tempPath,
              );
            } catch (_) {}
          }
          localAttachments.add(
            ChatItemsAttachment(
              link: element.path,
              mimetype: mime(element.path.toLowerCase()) ?? '',
              originalname: element.name,
              thumbnail: thumbnailPath,
              size: element.size,
              duration: element.duration,
            ),
          );
        }

        final newMessage = ChatItems(
          attachment: localAttachments,
          createdAt: DateTime.now().toUtc().toIso8601String(),
          createdByInfo: ChatItemsCreatedByInfo(
            username: user?.employeeId,
            id: user?.employeeId,
          ),
          requestId: sendId,
          reactions: [],
          userSeen: [],
          content: clearContent.substring(0, iLast != -1 ? iLast : null),
          mention: tagUserList
              .map(
                (final e) => ChatItemsMention(
                  username: e?.username,
                  label: e?.name,
                  text: '@${e?.username}',
                ),
              )
              .toList(),
          parseMode:
              Utils.isHtml(
                clearContent.substring(0, iLast != -1 ? iLast : null),
              )
              ? 'html'
              : 'text',
        );
        if (searchMessageCreatedAt == null) {
          _pagingController.itemList?.insert(0, newMessage);
        }
        if (context.mounted) {
          setState(() {});
        }
        final attachments = pickedFileBundles
            .map((final element) => element.copyWith())
            .toList();

        _clearDataOnSentMessage();

        final List<ChatUploadFileRequestParams> compressFiles = [];
        for (final element in attachments) {
          Uint8List? compressBytes;
          try {
            if ((mime(element.path.toLowerCase())?.contains(Strings.video) ??
                    false) &&
                element.size > 1024 * 1024 * 5) {
              final mediaInfo = await VideoCompress.compressVideo(
                element.path,
                quality: VideoQuality.Res1920x1080Quality,
              );

              if (mediaInfo!.file != null) {
                compressBytes = mediaInfo.file!.readAsBytesSync();
              }
            }
            if (mime(element.path.toLowerCase())?.contains(Strings.image) ??
                false) {
              compressBytes = await FlutterImageCompress.compressWithFile(
                File(element.path).absolute.path,
                minHeight: 1024,
                minWidth: 1024,
              );
            }
          } catch (e) {
            compressBytes = null;
          }

          compressFiles.add(
            ChatUploadFileRequestParams(
              MultipartFile.fromBytes(
                compressBytes ?? File(element.path).readAsBytesSync(),
                filename: element.name,
                contentType: MediaType.parse(
                  mime(element.path.toLowerCase()) ?? '',
                ),
              ),
              filename: element.name,
              duration: element.duration.toString(),
              waveform: jsonEncode(element.waveForm ?? []),
            ),
          );
        }
        if (context.mounted) {
          context.read<ChatBloc>().add(
            ChatUploadFile(
              params: compressFiles,
              sendId: sendId,
              content: newMessage.content ?? '',
            ),
          );
        }
      }

      //send forward message
      if (rootMessage != null && messageAction == ChatAction.forward.name) {
        if (context.mounted) {
          context.read<ChatBloc>().add(
            ChatSent(
              ChatSendRequestParams(
                conversationId: conversation?.id,
                content: null,
                requestId: sendId,
                attachment: rootMessage?.attachment ?? [],
                rootMessage: rootMessage,
                action: ChatAction.forward.name,
                mention: [],
                parseMode: 'text',
              ),
            ),
          );
          forwardMessage = null;
          // initForwardId = null;
        }
      }
    }
    _clearDataOnSentMessage();
  }

  String forwardMultiMessages(
    String sendId,
    final BuildContext context,
    final ChatListItems? conversation,
  ) {
    for (final element in selectedMessageList) {
      forwardMessage = element?.copyWith(
        id: element.forward?.id ?? element.id,
        createdByInfo: element.forward?.createdByInfo?.id != null
            ? ChatItemsCreatedByInfo(
                avatar: element.forward?.createdByInfo?.avatar,
                name: element.forward?.createdByInfo?.name,
                username: element.forward?.createdByInfo?.username,
                id: element.forward?.createdByInfo?.id,
              )
            : element.createdByInfo,
      );
      final rootMessage = RootMessageRequestParams(
        content: forwardMessage?.content,
        createdAt: forwardMessage?.createdAt,
        createdBy: forwardMessage?.createdByInfo?.username,
        id: forwardMessage?.id,
        attachment: forwardMessage?.attachment
            .map(
              (final e) => AttachmentRequestParams(
                link: e?.link,
                mimetype: e?.mimetype,
                originalname: e?.originalname,
                thumbnail: e?.thumbnail,
                size: e?.size,
                duration: e?.duration,
                waveform: e?.waveform,
                isRecord: e?.duration != null && e!.duration! > 0,
              ),
            )
            .toList(),
      );
      sendId = const Uuid().v4();

      context.read<ChatBloc>().add(
        ChatSent(
          ChatSendRequestParams(
            conversationId: conversation?.id,
            content: '',
            requestId: sendId,
            attachment: [],
            rootMessage: rootMessage,
            action: messageAction,
            mention: [],
            parseMode: Utils.isHtml(rootMessage.content ?? '')
                ? 'html'
                : 'text',
          ),
        ),
      );
    }

    if (messageController.plainTextEditingValue.text.trim().isNotEmpty) {
      String content = '';
      final obs = messageController.document.toDelta().toJson();
      final result = isAttributesNotEmpty(obs);
      if (result) {
        final filteredDeltaJson = obs.expand((final op) {
          if (op.containsKey('insert') &&
              op['attributes'] != null &&
              // ignore: avoid_dynamic_calls
              op['attributes']['link'] != null) {
            // ignore: avoid_dynamic_calls
            final link = op['attributes']['link'].toString();
            if (link.startsWith('https')) {
              // Thay bằng chính link đó như văn bản
              return [
                {'insert': link},
              ];
            }
          }
          return [op];
        }).toList();
        final converter = QuillDeltaToHtmlConverter(
          filteredDeltaJson,
          ConverterOptions(),
        );

        content = converter
            .convert()
            .replaceFirst(RegExp('<p>'), '')
            .replaceFirst(RegExp('</p>'), '', converter.convert().length - 4);
      } else {
        content = messageController.plainTextEditingValue.text.trim();
      }
      final clearContent = content
          .replaceAll('&#47;', '')
          .replaceAll('&amp;', '&');
      final iLast = clearContent.lastIndexOf(RegExp('</p>'));
      context.read<ChatBloc>().add(
        ChatSent(
          ChatSendRequestParams(
            conversationId: conversation?.id,
            content: (clearContent.isEmpty || iLast == -1)
                ? clearContent
                : clearContent.substring(0, iLast),
            requestId: ChatAction.forward.name,
            attachment: [],
            action: ChatAction.create.name,
            mention: tagUserList
                .map(
                  (final e) => MentionUserRequestParams(
                    username: e?.username,
                    label: e?.name,
                    text: '@${e?.username}',
                  ),
                )
                .toList(),
            parseMode:
                Utils.isHtml(
                  (content.isEmpty || iLast == -1)
                      ? content
                      : content.substring(0, iLast),
                )
                ? 'html'
                : 'text',
          ),
        ),
      );
    }
    return sendId;
  }

  void _clearDataOnSentMessage() {
    if (!messageController.document.documentChangeObserver.isClosed) {
      messageController.clear();
    }
    pickedFileBundles.clear();
    tagUserList.clear();
    replyMessage = null;
    editMessage = null;
    forwardMessage = null;
    // initForwardId = null;
    forwardConversations.clear();
    selectedAssetList.clear();
  }

  RootMessageRequestParams? get rootMessage {
    if (replyMessage != null) {
      return RootMessageRequestParams(
        content: replyMessage?.content,
        createdAt: replyMessage?.createdAt,
        createdBy: (replyMessage?.forward?.id?.isNotEmpty ?? false)
            ? replyMessage?.forward?.createdByInfo?.username
            : replyMessage?.createdByInfo?.username,
        id: replyMessage?.id,
        attachment: [],
      );
    }
    if (forwardMessage != null) {
      return RootMessageRequestParams(
        content: forwardMessage?.content,
        createdAt: forwardMessage?.createdAt,
        createdBy: forwardMessage?.createdByInfo?.username,
        id: forwardMessage?.id,
        attachment: forwardMessage?.attachment
            .map(
              (final e) => AttachmentRequestParams(
                link: e?.link,
                mimetype: e?.mimetype,
                originalname: e?.originalname,
                thumbnail: e?.thumbnail,
                size: e?.size,
                duration: e?.duration,
                waveform: e?.waveform,
                isRecord: e?.duration != null && e!.duration! > 0,
              ),
            )
            .toList(),
      );
    }
    return null;
  }

  String? get messageAction {
    if (replyMessage != null) {
      return ChatAction.reply.name;
    }
    if (forwardMessage != null) {
      return ChatAction.forward.name;
    }
    return null;
  }

  void onTapRemoveFile(final int index) {
    setState(() {
      pickedFileBundles.removeAt(index);
    });
  }

  // Future<void> onTapAddFile(final BuildContext context) async {
  //   FocusScope.of(context).unfocus();
  //   onTapAttachFileButton(AttachmentOption.file);
  // }

  bool isShowingAvatar(final int index, final List<ChatItems?> itemList) {
    // if (!(conversation?.isGroup ?? true)) {
    //   return false;
    // }

    if (index == 0) {
      return true;
    }

    return itemList[index]?.createdByInfo?.id !=
        itemList[index - 1]?.createdByInfo?.id;
  }

  bool isShowingName(final int index, final List<ChatItems?> itemList) {
    // if (!(conversation?.isGroup ?? true)) {
    //   return false;
    // }

    try {
      // note: keep position of condition
      if (itemList[index]?.createdByInfo?.id !=
              itemList[index + 1]?.createdByInfo?.id &&
          itemList[index]?.createdByInfo?.id !=
              itemList[index - 1]?.createdByInfo?.id) {
        return true;
      }

      // note: keep position of condition
      if (itemList[index]?.createdByInfo?.id !=
              itemList[index + 1]?.createdByInfo?.id &&
          itemList[index]?.createdByInfo?.id ==
              itemList[index - 1]?.createdByInfo?.id) {
        return true;
      }

      return false;
    } catch (_) {
      return true;
    }
  }

  bool isTheSameDay(final String? date1, final String? date2) {
    try {
      final d1 = date1!
          .getDateTime(IntlHelper.dateFormatter2, isUtc: true)!
          .toLocal();

      final d2 = date2!
          .getDateTime(IntlHelper.dateFormatter2, isUtc: true)!
          .toLocal();
      return d1.isTheSameDay(d2);
    } catch (e) {
      return false;
    }
  }

  // Future<void> _makeCall() async {
  //   if (conversation?.membersInfo.length == 2) {
  //     final callUser = conversation?.membersInfo
  //         .firstWhereOrNull((final e) => e?.username != user?.employeeId);
  //     final to = callUser?.username;
  //     final toName = callUser?.name;
  //     PlatformChannelHelper.onMakeCallNotifier(to: to, toName: toName);
  //   }
  // }

  Widget buildRecorder(final BuildContext context) {
    if (recording) {
      return RecorderWidget(
        path: recordPath,
        onTapRecord: onTapRecord,
        onTapDeleteRecord: onTapDeleteRecord,
        onTapSendRecord: onTapSendRecord,
      );
    }

    return const SizedBox.shrink();
  }

  void onTapRecord(final String value, final int duration) {
    return setState(() {
      recordPath = value;
      recordDuration = duration;
    });
  }

  void onTapDeleteRecord() {
    return setState(() {
      recording = false;
    });
  }

  Future<void> onTapSendRecord() async {
    if (recordPath.isNotEmpty && recording) {
      pickedFileBundles.add(
        PickedFileBundle(
          name: recordPath.split('/').lastOrNull ?? '',
          image: Icon(
            Icons.audio_file_outlined,
            color: Theme.of(context).hintColor,
          ),
          fileExtension: getFileExtension(recordPath),
          path: recordPath,
          size: File(recordPath).lengthSync(),
          duration: recordDuration,
          waveForm: _extractWaveformData(File(recordPath).readAsBytesSync()),
        ),
      );
      recording = false;
      _onTapSend(context);
    }
  }

  List<int> _extractWaveformData(final Uint8List audioBytes) {
    final List<int> waveData = [];
    final int step = (audioBytes.length / 63).floor();
    for (int i = 0; i < audioBytes.length; i += step) {
      waveData.add(audioBytes[i]);
    }
    waveData.add(audioBytes[audioBytes.length - 1]);
    return waveData;
  }

  Future<void> onTapRecordButton() async {
    final hasPermission = await Permission.microphone.request().isGranted;
    if (!hasPermission && mounted) {
      Alert.showAlertSetting(
        AlertSettingParams(
          context,
          context.l10n.alert,
          context.l10n.requestPermissionMicro,
          closeButton: context.l10n.close,
          openSettingButton: context.l10n.openSettings,
        ),
      );
      return;
    }
    if (audioPlayerGlobal.state == PlayerState.playing) {
      audioPlayerGlobal.pause();
    }
    return setState(() {
      recording = true;
    });
  }

  GridView _buildStickerTab(final ChatGetUserStickerItems? stickerModel) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
      ),
      itemCount: stickerModel?.stickers.length ?? 0,
      itemBuilder: (final BuildContext _, final int index) {
        final sticker = stickerModel?.stickers[index];
        return GestureDetector(
          onTap: () {
            _onStickerSelected(sticker);
            Navigator.of(context).pop();
          },
          child: (sticker?.mimetype?.contains('video') ?? false)
              ? AnimatedStickerWidget(
                  ChatSticker(
                    link: sticker?.link,
                    mimetype: sticker?.mimetype,
                    width: sticker?.width,
                    height: sticker?.height,
                    id: sticker?.id,
                    stickerSetId: sticker?.stickerSetId,
                    size: sticker?.size,
                  ),
                )
              : EzCachedNetworkImage(imageUrl: sticker?.link),
        );
      },
    );
  }

  void _onStickerSelected(final ChatGetUserStickerItemsStickers? sticker) {
    stickersRecent.add(
      sticker ?? const ChatGetUserStickerItemsStickers(tags: []),
    );
    final sendId = const Uuid().v4();
    final requestParams = StickerRequestParams(
      link: sticker?.link,
      mimetype: sticker?.mimetype,
      width: sticker?.width,
      height: sticker?.height,
      id: sticker?.id,
      stickerSetId: sticker?.stickerSetId,
      size: sticker?.size,
    );
    final newMessage = ChatItems(
      sticker: ChatSticker(
        link: sticker?.link,
        mimetype: sticker?.mimetype,
        width: sticker?.width,
        height: sticker?.height,
        id: sticker?.id,
        stickerSetId: sticker?.stickerSetId,
        size: sticker?.size,
      ),
      attachment: [],
      createdAt: DateTime.now().toUtc().toIso8601String(),
      createdByInfo: ChatItemsCreatedByInfo(
        username: user?.employeeId,
        id: user?.employeeId,
      ),
      requestId: sendId,
    );
    if (searchMessageCreatedAt == null) {
      _pagingController.itemList?.insert(0, newMessage);
    }
    context.read<ChatBloc>().add(
      ChatSent(
        ChatSendRequestParams(
          conversationId: conversation?.id,
          content: null,
          requestId: sendId,
          mention: [],
          attachment: [],
          sticker: requestParams,
          parseMode: 'text',
        ),
      ),
    );
    EZCache.shared.saveStickerRecent(
      stickersRecent.map((final e) => e.toJson()).toList(),
    );
  }
}
