// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_resources/ez_resources.dart';

const product_color = Color(0xffe4f1eb);
const product_color2 = Color(0xfff1f3f9);

TextStyle? styleTitle(final BuildContext context) => Theme.of(
  context,
).textTheme.labelLarge?.copyWith(color: Theme.of(context).primaryColor);

const gradientBg = LinearGradient(
  begin: Alignment.topRight,
  end: Alignment.bottomLeft,
  colors: [
    product_color,
    product_color,
    product_color2,
    product_color2,
    product_color2,
    product_color2,
    product_color2,
    product_color2,
  ],
);

const gradient_bg2 = LinearGradient(
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
  colors: [product_color, product_color2, Colors.white],
);

class BaseLayout extends StatelessWidget {
  const BaseLayout({
    super.key,
    required this.body,
    this.bottomAppBarChild,
    this.actions = const [],
    required this.title,
    this.leading,
    this.bottomNavigationBar,
    this.isDivider = false,
  });
  final Widget? leading;
  final Widget title;
  final Widget body;
  final Widget? bottomAppBarChild;
  final List<Widget> actions;
  final Widget? bottomNavigationBar;
  final bool isDivider;
  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      height: MediaQuery.sizeOf(context).height,
      child: Stack(
        children: [
          Positioned.fill(
            child: DecoratedBox(
              decoration: const BoxDecoration(gradient: gradientBg),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top,
                      left: 4.0,
                      right: 4.0,
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: kToolbarHeight,
                      child: Stack(
                        children: [
                          Align(
                            alignment: Alignment.centerLeft,
                            child:
                                leading ??
                                IconButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  icon: EZResources.image(
                                    ImageParams(
                                      name: AppIcons.icBack,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ),
                          ),
                          Align(child: title),
                          Align(
                            alignment: Alignment.centerRight,
                            child: SizedBox(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: actions,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (bottomAppBarChild != null)
                    bottomAppBarChild ?? const SizedBox(),
                  if (isDivider) const Divider(thickness: .35),
                  const SizedBox(height: 12),
                  Expanded(child: body),
                  if (bottomNavigationBar != null) const SizedBox(height: 76),
                ],
              ),
            ),
          ),
          if (bottomNavigationBar != null)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: SizedBox(child: bottomNavigationBar ?? const SizedBox()),
            ),
        ],
      ),
    );
  }
}
