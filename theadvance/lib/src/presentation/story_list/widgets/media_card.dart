// Dart imports:

// Flutter imports:

// Dart imports:
import 'dart:math';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:animations/animations.dart';
import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:extended_image/extended_image.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:video_player/video_player.dart';

// Project imports:
import '../../../core/params/story_write_info_request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../services/nd_downloader/nd_downloader.dart';
import '../../ticket/widgets/ticket_audio_play.dart';
import '../../widgets/widgets.dart';
import '../view/story_image_detail_page.dart';
import 'story_image.dart';
import 'story_video.dart';
import 'story_video_in_page.dart';

class MediaCard extends StatefulWidget {
  const MediaCard({
    super.key,
    required this.item,
    required this.idStory,
    required this.heightPost,
  });
  final String idStory;
  final List<Attachment?>? item;
  final int heightPost;
  @override
  State<MediaCard> createState() => _MediaCardState();
}

class _MediaCardState extends State<MediaCard> {
  final List<String> tags = [];
  final List<String> urls = [];
  final List<String> fileNames = [];
  double factoryHeight(final List<Attachment?>? items) {
    return (widget.heightPost <= 250 && items?.length == 1)
        ? 250
        // 1 media => 350
        : (widget.heightPost > 250 &&
              widget.heightPost < 425 &&
              items?.length == 1)
        ? 260
        : (widget.heightPost > 425 &&
              items?.length == 1 &&
              checkFileImage(items?.first?.mimetype))
        ? 480
        : (widget.heightPost > 425 &&
              items?.length == 1 &&
              checkFileVideo(items?.first?.mimetype))
        ? 500
        // 2 media => 200
        : (widget.heightPost <= 200 && items?.length == 2)
        ? 200
        // 2 media => 250
        : (widget.heightPost > 450 && items?.length == 2)
        ? 320
        // 3 media => 320
        : (widget.heightPost > 450 &&
              (items?.length == 3 || items?.length == 4))
        ? 320
        :
          // 3 media => 410
          (widget.heightPost < 250 &&
              (items?.length == 3 || items?.length == 4))
        ? 410
        : 320;
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      final itemsVideo = widget.item
          ?.where((final e) => checkFileImage(e?.mimetype))
          .toList();

      if ((itemsVideo?.length ?? 0) >= 1) {
        Future.wait([
          ...itemsVideo?.map((final e) async {
                if (mounted) {
                  await precacheImage(
                    CachedNetworkImageProvider(e?.thumbnail ?? ''),
                    context,
                  );
                }
              }) ??
              [],
        ]);
      }

      final items = widget.item
          ?.where((final e) => checkFileImage(e?.mimetype))
          .toList();
      Future.wait([
        ...items?.map((final e) async {
              if (mounted) {
                await precacheImage(
                  CachedNetworkImageProvider(e?.link ?? ''),
                  context,
                );
              }
            }) ??
            [],
      ]);
    });
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    final items = widget.item
        ?.where(
          (final e) =>
              checkFileImage(e?.mimetype) || checkFileVideo(e?.mimetype),
        )
        .toList();
    final itemFiles = widget.item
        ?.where((final e) => checkFileDocument(e?.mimetype))
        .toList();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          color: Theme.of(context).dividerColor,
          constraints: BoxConstraints(
            maxHeight: min(
              (items?.length ?? 0) == 1 ? 700 : 400,
              MediaQuery.sizeOf(context).height * 0.8,
            ),
          ),
          child: (items?.length ?? 0) == 1
              ? _buildSingleMedia(context, items)
              : (items?.length ?? 0) <= 2
              ? _buildThreeMediaRow(items)
              : (items?.length ?? 0) == 3 || (items?.length ?? 0) == 4
              ? SizedBox(
                  child: widget.heightPost > 450
                      ? _buildThreeMediaRow(items)
                      : _buildThreeMediaColumn(items),
                )
              : (items?.length ?? 0) >= 5
              ? _buildFiveMedia(items)
              : const SizedBox(),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              ...List.generate(itemFiles?.length ?? 0, (final i) {
                return _buildFile(itemFiles?[i]);
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFile(final Attachment? file) {
    return Padding(
      padding: const EdgeInsets.only(right: 8, top: 8),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border.all(color: const Color(0xffDEE3ED)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: SizedBox(
          width: 100,
          height: 120,
          child: GestureDetector(
            onTap: () async {
              if (file?.mimetype?.contains('audio/') ?? false) {
                showModal(
                  context: context,
                  builder: (final builder) => GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: AlertDialog(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(0),
                      ),
                      backgroundColor: Colors.transparent,
                      insetPadding: EdgeInsets.zero,
                      contentPadding: EdgeInsets.zero,
                      clipBehavior: Clip.antiAliasWithSaveLayer,
                      content: SizedBox(
                        width: double.maxFinite,
                        height: double.maxFinite,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [TicketAudioPlay(attachment: file)],
                        ),
                      ),
                    ),
                  ),
                );
              } else {
                ServiceDownloader.instance.onTapDownload(
                  context,
                  file?.link ?? '',
                  TaskInfo(name: file?.originalname),
                  file?.originalname,
                );
              }
            },
            child: SizedBox(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (file?.mimetype?.contains('audio/') ?? false)
                      DecoratedBox(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          color: Theme.of(context).primaryColor,
                        ),
                        child: const SizedBox.square(
                          dimension: 36,
                          child: Icon(Icons.play_arrow, color: Colors.white),
                        ),
                      )
                    else
                      EZResources.image(ImageParams(name: AppIcons.icFileDown)),
                    ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 100),
                      child: Text(
                        file?.originalname ?? '',
                        style: const TextStyle(overflow: TextOverflow.ellipsis),
                      ),
                    ),
                    Text(formatSize(file?.size ?? 0)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String formatSize(final int sizeInKB) {
    final double sizeInBytes = sizeInKB.toDouble();

    if (sizeInBytes < 1024) {
      return '$sizeInBytes Bytes';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(2)} KB';
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }

  Stack buildThumbnail(final Attachment? item) {
    return Stack(
      children: [
        Positioned.fill(
          child: CachedNetworkImage(
            imageUrl: item?.thumbnail ?? '',
            errorWidget: (final context, final url, final error) =>
                const Center(child: Icon(Icons.error)),
          ),
        ),
        const Center(
          child: Icon(Icons.play_circle, color: Colors.white, size: 40),
        ),
      ],
    );
  }

  void handleMedia(final List<Attachment?>? items, final String tag) {
    final safeImageList = items?.map((final e) => e ?? Attachment()).toList();
    final listWidgetPreview = safeImageList?.map((final eWidget) {
      if (checkFileImage(eWidget.mimetype)) {
        return Center(
          child: EzCachedNetworkImage(imageUrl: eWidget.link ?? ''),
        );
      }
      if (checkFileVideo(eWidget.mimetype)) {
        return EzCachedNetworkImage(imageUrl: eWidget.thumbnail ?? '');
      }
      return const SizedBox(child: ColoredBox(color: Colors.grey));
    }).toList();
    StoryImageDetailPage.listMediaPreview.setValue(listWidgetPreview);
    final listWidgetAttachment = safeImageList?.map((final eWidget) {
      if (checkFileImage(eWidget.mimetype)) {
        return StoryImage(
          originalWidth: eWidget.width ?? 1,
          originalHeight: eWidget.height ?? 1,
          tags: tag,
          imageUrl: '${eWidget.link}',
          fit: BoxFit.contain,
          disableGestures: null,
          initialScale: PhotoViewComputedScale.contained,
          minScale: PhotoViewComputedScale.contained,
          maxScale: PhotoViewComputedScale.contained * 1.1,
        );
      }
      if (checkFileVideo(eWidget.mimetype)) {
        return ExtendedImageSlidePageHandler(
          child: StoryVideoInPage(
            fileName: eWidget.originalname ?? '',
            idStory: tag,
            urlVideo: eWidget.link ?? '',
            thumbnail: eWidget.thumbnail ?? '',
            playerController: VideoPlayerController.networkUrl(
              Uri.parse('${eWidget.link}'),
            ),
            originalWidth: eWidget.width ?? 1,
            originalHeight: eWidget.height ?? 1,
          ),
          heroBuilderForSlidingPage: (final result) {
            return Hero(
              tag: tag,
              child: result,
              flightShuttleBuilder:
                  (
                    final flightContext,
                    final animation,
                    final flightDirection,
                    final fromHeroContext,
                    final toHeroContext,
                  ) {
                    final Hero hero =
                        (flightDirection == HeroFlightDirection.pop
                                ? fromHeroContext.widget
                                : toHeroContext.widget)
                            as Hero;

                    return hero.child;
                  },
            );
          },
        );
      }
      return const SizedBox(child: ColoredBox(color: Colors.grey));
    }).toList();
    StoryImageDetailPage.listMedia.setValue(listWidgetAttachment);
  }

  Widget _buildSeeMore(
    final BuildContext context,
    final List<Attachment?>? items,
  ) {
    return Positioned.fill(
      child: GestureDetector(
        onTap: () async {
          items?.sublist(5).forEach((final e) {
            final micro = DateTime.now().microsecondsSinceEpoch.toString();
            final tag = '${e?.link}$micro';
            tags.add(tag);
            urls.add('${e?.link}');
            fileNames.add('${e?.originalname}');
          });
          handleMedia(items, tags[4]);
          context.router.push(
            StoryImageDetailRoute(
              file: items?.firstOrNull,
              tags: tags,
              initIndex: 4,
              url: urls,
              fileName: fileNames,
            ),
          );
        },
        child: SizedBox(
          child: ColoredBox(
            color: Colors.black.withValues(alpha: .5),
            child: Center(
              child: Text(
                '+${items?.sublist(5).length}',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThreeMediaColumn(final List<Attachment?>? items) {
    return Column(
      children: [
        ...List.generate(items?.length ?? 0, (final i) {
          final item = items?[i];

          final micro = DateTime.now().microsecondsSinceEpoch.toString();
          final tag = '${item?.link}$micro';
          tags.add(tag);
          urls.add('${item?.link}');
          fileNames.add('${item?.originalname}');
          return i == 0
              ? Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(0.0),
                      child: SizedBox.expand(
                        child: checkFileImage(items?.firstOrNull?.mimetype)
                            ? GestureDetector(
                                onTap: () async {
                                  handleMedia(items, tags[i]);
                                  pushStoryImageDetail(context, items?[i], i);
                                },
                                child: StoryImage(
                                  originalWidth: items?.firstOrNull?.width ?? 1,
                                  originalHeight:
                                      items?.firstOrNull?.height ?? 1,
                                  tags: tags[i],
                                  imageUrl: '${items?.firstOrNull?.link}',
                                ),
                              )
                            : checkFileVideo(items?.firstOrNull?.mimetype)
                            ? StoryVideo(
                                idStory: widget.idStory,
                                urlVideo: items?.firstOrNull?.link ?? '',
                                fileName:
                                    items?.firstOrNull?.originalname ?? '',
                                thumbnail: items?.firstOrNull?.thumbnail ?? '',
                                playerController:
                                    VideoPlayerController.networkUrl(
                                      Uri.parse(items?.firstOrNull?.link ?? ''),
                                    ),
                                heightVideo: factoryHeight(items),
                                originalWidth: items?.firstOrNull?.width ?? 1,
                                originalHeight: items?.firstOrNull?.height ?? 1,
                                onStoryDetail: () async {
                                  handleMedia(items, tags[i]);
                                  pushStoryImageDetail(context, items?[i], i);
                                },
                              )
                            : const SizedBox(),
                      ),
                    ),
                  ),
                )
              : i == 1
              ? Expanded(
                  child: Row(
                    children: [
                      ...List.generate(items?.sublist(1).length ?? 0, (
                        final i2,
                      ) {
                        final item = items?.sublist(1)[i2];

                        final micro = DateTime.now().microsecondsSinceEpoch
                            .toString();
                        final tag = '${item?.link}$micro';
                        tags.add(tag);
                        urls.add('${item?.link}');
                        fileNames.add('${item?.originalname}');
                        return Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: i2 == 0 ? 0 : 2,
                              // top: 2,
                              bottom: 2,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(0),
                              child: SizedBox.expand(
                                child: checkFileImage(item?.mimetype)
                                    ? Stack(
                                        fit: StackFit.expand,
                                        children: [
                                          StoryImage(
                                            originalWidth: item?.width ?? 1,
                                            originalHeight: item?.height ?? 1,
                                            tags: tag[i2],
                                            imageUrl: '${item?.link}',
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              handleMedia(items, tags[i2]);
                                              context.router.push(
                                                StoryImageDetailRoute(
                                                  file: items?.first,
                                                  tags: tags,
                                                  initIndex: 1 + i2,
                                                  url: urls,
                                                  fileName: fileNames,
                                                ),
                                              );
                                            },
                                            child: _buildOverlay(item),
                                          ),
                                        ],
                                      )
                                    : checkFileVideo(item?.mimetype)
                                    ? buildThumbnail(item)
                                    : const SizedBox(),
                              ),
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                )
              : const SizedBox();
        }),
      ],
    );
  }

  Widget _buildFiveMedia(final List<Attachment?>? items) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            children: [
              ...List.generate(items?.sublist(0, 2).length ?? 0, (final i2) {
                final item = items?.sublist(0, 2)[i2];
                final micro = DateTime.now().microsecondsSinceEpoch.toString();
                final tag = '${item?.link}$micro';
                tags.add(tag);
                urls.add('${item?.link}');
                fileNames.add('${item?.originalname}');
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 2, bottom: 2, right: 2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(0.0),
                      child: SizedBox.expand(
                        child: checkFileImage(item?.mimetype)
                            ? Stack(
                                fit: StackFit.passthrough,
                                children: [
                                  StoryImage(
                                    originalWidth: item?.width ?? 1,
                                    originalHeight: item?.height ?? 1,
                                    tags: tag[i2],
                                    imageUrl: '${item?.link}',
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      handleMedia(items, tags[i2]);
                                      context.router.push(
                                        StoryImageDetailRoute(
                                          file: items?.first,
                                          tags: tags,
                                          initIndex: i2,
                                          url: urls,
                                          fileName: fileNames,
                                        ),
                                      );
                                    },
                                    child: _buildOverlay(item),
                                  ),
                                ],
                              )
                            : checkFileVideo(item?.mimetype)
                            ? StoryVideo(
                                idStory: widget.idStory,
                                urlVideo: item?.link ?? '',
                                fileName: item?.originalname ?? '',
                                thumbnail: item?.thumbnail ?? '',
                                playerController:
                                    VideoPlayerController.networkUrl(
                                      Uri.parse(item?.link ?? ''),
                                    ),
                                onStoryDetail: () async {
                                  handleMedia(items, tags[i2]);
                                  pushStoryImageDetail(context, items?[i2], i2);
                                },
                                originalWidth: item?.width ?? 1,
                                originalHeight: item?.height ?? 1,
                              )
                            : const SizedBox(),
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        Expanded(
          flex: 2,
          child: Column(
            children: [
              ...List.generate(items?.sublist(2, 5).length ?? 0, (final i2) {
                final item = items?.sublist(2, 5)[i2];
                final micro = DateTime.now().microsecondsSinceEpoch.toString();
                final tag = '${item?.link}$micro';
                tags.add(tag);
                urls.add('${item?.link}');
                fileNames.add('${item?.originalname}');
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 2, top: 2, bottom: 2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(0.0),
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          Positioned.fill(
                            child: checkFileVideo(item?.mimetype)
                                ? buildThumbnail(item)
                                : SizedBox(
                                    width: double.infinity,
                                    child: StoryImage(
                                      originalWidth: item?.width ?? 1,
                                      originalHeight: item?.height ?? 1,
                                      tags: tags[2 + i2],
                                      imageUrl: '${item?.link}',
                                    ),
                                  ),
                          ),
                          if ((items?.length ?? 0) > 5 && i2 == 2)
                            _buildSeeMore(context, items)
                          else
                            GestureDetector(
                              onTap: () {
                                if ((items?.length ?? 0) > 5) {
                                  items?.sublist(5).forEach((final e) {
                                    final micro = DateTime.now()
                                        .microsecondsSinceEpoch
                                        .toString();
                                    final tag = '${e?.link}$micro';
                                    tags.add(tag);
                                    urls.add('${e?.link}');
                                    fileNames.add('${e?.originalname}');
                                  });
                                }
                                handleMedia(items, tags[2 + i2]);
                                context.router.push(
                                  StoryImageDetailRoute(
                                    file: items?.first,
                                    tags: tags,
                                    initIndex: 2 + i2,
                                    url: urls,
                                    fileName: fileNames,
                                  ),
                                );
                              },
                              child: _buildOverlay(item),
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSingleMedia(
    final BuildContext context,
    final List<Attachment?>? items,
  ) {
    return Row(
      children: [
        ...List.generate(items?.length ?? 0, (final i) {
          final item = items?[i];
          final micro = DateTime.now().microsecondsSinceEpoch.toString();
          final tag = '${item?.link}$micro';
          tags.add(tag);
          urls.add('${item?.link}');
          fileNames.add('${item?.originalname}');
          return Expanded(
            flex: (items?.length ?? 0) == 4 ? 3 : 1,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(0.0),
                child: checkFileImage(item?.mimetype)
                    ? Stack(
                        fit: StackFit.passthrough,
                        // (items?.length ?? 0) == 1
                        //     ? StackFit.passthrough
                        //     : StackFit.expand,
                        children: [
                          StoryImage(
                            originalWidth: item?.width ?? 1,
                            originalHeight: item?.height ?? 1,
                            tags: tag[i],
                            imageUrl: '${item?.link}',
                          ),
                          GestureDetector(
                            onTap: () {
                              handleMedia(items, tags[i]);
                              pushStoryImageDetail(context, item, i);
                            },
                            child: _buildOverlay(item),
                          ),
                        ],
                      )
                    : checkFileVideo(item?.mimetype)
                    ? StoryVideo(
                        idStory: widget.idStory,
                        urlVideo: item?.link ?? '',
                        fileName: item?.originalname ?? '',
                        thumbnail: item?.thumbnail ?? '',
                        playerController: VideoPlayerController.networkUrl(
                          Uri.parse(item?.link ?? ''),
                        ),
                        heightVideo: factoryHeight(items),
                        onStoryDetail: items?.length == 1
                            ? null
                            : () async {
                                handleMedia(items, tags[i]);
                                pushStoryImageDetail(context, items?[i], i);
                              },
                        originalWidth: item?.width ?? 1,
                        originalHeight: item?.height ?? 1,
                      )
                    : const SizedBox(),
              ),
            ),
          );
        }),
      ],
    );
  }

  AspectRatio _buildOverlay(final Attachment? item) {
    return AspectRatio(
      aspectRatio:
          ((item?.width ?? 1) == 0 ? 1 : (item?.width ?? 1)) /
          ((item?.height ?? 1) == 0 ? 1 : (item?.height ?? 1)),
      child: Container(color: Colors.transparent),
    );
  }

  Widget _buildThreeMediaRow(final List<Attachment?>? items) {
    final is2Media =
        (items?.length ?? 0) == 2 &&
        items?.where((final e) => (e?.height ?? 0) < 480).toList().length == 2;
    return is2Media
        ? Column(
            children: [
              ...List.generate(items?.length ?? 0, (final i) {
                final item = items?[i];
                final micro = DateTime.now().microsecondsSinceEpoch.toString();
                final tag = '${item?.link}$micro';
                tags.add(tag);
                urls.add('${item?.link}');
                fileNames.add('${item?.originalname}');
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(0.0),
                      child: SizedBox.expand(
                        child: checkFileImage(item?.mimetype)
                            ? Stack(
                                fit: StackFit.expand,
                                children: [
                                  StoryImage(
                                    originalWidth: item?.width ?? 1,
                                    originalHeight: item?.height ?? 1,
                                    tags: tag[i],
                                    imageUrl: '${item?.link}',
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      handleMedia(items, tags[i]);
                                      pushStoryImageDetail(context, item, i);
                                    },
                                    child: _buildOverlay(item),
                                  ),
                                ],
                              )
                            : checkFileVideo(item?.mimetype)
                            ? StoryVideo(
                                idStory: widget.idStory,
                                urlVideo: item?.link ?? '',
                                fileName: item?.originalname ?? '',
                                thumbnail: item?.thumbnail ?? '',
                                playerController:
                                    VideoPlayerController.networkUrl(
                                      Uri.parse(item?.link ?? ''),
                                    ),
                                heightVideo: factoryHeight(items),
                                onStoryDetail: items?.length == 1
                                    ? null
                                    : () async {
                                        handleMedia(items, tags[i]);
                                        pushStoryImageDetail(
                                          context,
                                          items?[i],
                                          i,
                                        );
                                      },
                                originalWidth: item?.width ?? 1,
                                originalHeight: item?.height ?? 1,
                              )
                            : const SizedBox(),
                      ),
                    ),
                  ),
                );
              }),
            ],
          )
        : Row(
            children: [
              ...List.generate(items?.length ?? 0, (final i) {
                final item = items?[i];
                final micro = DateTime.now().microsecondsSinceEpoch.toString();
                final tag = '${item?.link}$micro';
                tags.add(tag);
                urls.add('${item?.link}');
                fileNames.add('${item?.originalname}');
                return i == 0
                    ? Expanded(
                        flex: (items?.length ?? 0) == 4 ? 3 : 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(0.0),
                            child: SizedBox.expand(
                              child: checkFileImage(item?.mimetype)
                                  ? Stack(
                                      fit: StackFit.expand,
                                      children: [
                                        StoryImage(
                                          originalWidth: item?.width ?? 1,
                                          originalHeight: item?.height ?? 1,
                                          tags: tag[i],
                                          imageUrl: '${item?.link}',
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            handleMedia(items, tags[i]);
                                            pushStoryImageDetail(
                                              context,
                                              item,
                                              i,
                                            );
                                          },
                                          child: _buildOverlay(item),
                                        ),
                                      ],
                                    )
                                  : checkFileVideo(item?.mimetype)
                                  ? StoryVideo(
                                      idStory: widget.idStory,
                                      urlVideo: item?.link ?? '',
                                      fileName: item?.originalname ?? '',
                                      thumbnail: item?.thumbnail ?? '',
                                      playerController:
                                          VideoPlayerController.networkUrl(
                                            Uri.parse(item?.link ?? ''),
                                          ),
                                      heightVideo: factoryHeight(items),
                                      onStoryDetail: items?.length == 1
                                          ? null
                                          : () async {
                                              handleMedia(items, tags[i]);
                                              pushStoryImageDetail(
                                                context,
                                                items?[i],
                                                i,
                                              );
                                            },
                                      originalWidth: item?.width ?? 1,
                                      originalHeight: item?.height ?? 1,
                                    )
                                  : const SizedBox(),
                            ),
                          ),
                        ),
                      )
                    : i == 1
                    ? Expanded(
                        flex: (items?.length ?? 0) == 4 ? 2 : 1,
                        child: Column(
                          children: [
                            ...List.generate(items?.sublist(1).length ?? 0, (
                              final i2,
                            ) {
                              final item = items?.sublist(1)[i2];
                              return Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    left: 2,
                                    top: 2,
                                    bottom: 2,
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(0),
                                    child: SizedBox.expand(
                                      child: checkFileImage(item?.mimetype)
                                          ? _buildImagePrivate(
                                              item,
                                              tag,
                                              i2,
                                              items,
                                              i,
                                            )
                                          : checkFileVideo(item?.mimetype)
                                          ? _buildVideothumnail(
                                              items,
                                              i2,
                                              i,
                                              item,
                                            )
                                          : const SizedBox(),
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                      )
                    : const SizedBox();
              }),
            ],
          );
  }

  GestureDetector _buildVideothumnail(
    final List<Attachment?>? items,
    final int i2,
    final int i,
    final Attachment? item,
  ) {
    return GestureDetector(
      onTap: () {
        handleMedia(items, tags[i2]);

        context.router.push(
          StoryImageDetailRoute(
            file: items?[i + i2],
            tags: tags,
            initIndex: i + i2,
            url: urls,
            fileName: fileNames,
          ),
        );
      },
      child: buildThumbnail(item),
    );
  }

  Stack _buildImagePrivate(
    final Attachment? item,
    final String tag,
    final int i2,
    final List<Attachment?>? items,
    final int i,
  ) {
    return Stack(
      fit: StackFit.expand,
      children: [
        StoryImage(
          originalWidth: item?.width ?? 1,
          originalHeight: item?.height ?? 1,
          tags: tag[i2],
          imageUrl: '${item?.link}',
        ),
        GestureDetector(
          onTap: () {
            handleMedia(items, tags[i2]);

            context.router.push(
              StoryImageDetailRoute(
                file: items?[i + i2],
                tags: tags,
                initIndex: i + i2,
                url: urls,
                fileName: fileNames,
              ),
            );
          },
          child: _buildOverlay(item),
        ),
      ],
    );
  }

  Future<void> pushStoryImageDetail(
    final BuildContext context,
    final Attachment? item,
    final int i,
  ) async {
    context.router.push(
      StoryImageDetailRoute(
        file: item,
        tags: tags,
        initIndex: i,
        url: urls,
        fileName: fileNames,
      ),
    );
  }

  bool checkFileImage(final String? type) {
    if (type == 'image/jpeg' || type == 'image/png' || type == 'image/webp') {
      return true;
    }

    return false;
  }

  bool checkFileVideo(final String? type) {
    if (type == 'video/mp4' ||
        type == 'video/quicktime' ||
        type == 'video/mpeg') {
      return true;
    }
    return false;
  }

  bool checkFileDocument(final String? type) {
    if (!checkFileImage(type) && !checkFileVideo(type)) {
      return true;
    }
    return false;
  }
}
