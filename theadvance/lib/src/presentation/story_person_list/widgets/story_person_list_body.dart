// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:linkfy_text/linkfy_text.dart';
import 'package:video_player/video_player.dart';

// Project imports:
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/nd_progresshud/loading_dialog.dart';
import '../../../core/params/progress_params.dart';
import '../../../core/params/request_params.dart';
import '../../../core/params/story_person_list_request_params.dart';
import '../../../core/params/story_person_request_params.dart';
import '../../../core/params/story_update_request_params.dart';
import '../../../core/params/story_write_info_request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/routes/routes.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../core/utils/platform_channel_helper.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/created_by_info.dart';
import '../../../domain/entities/entities.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../../domain/entities/like_story_success.dart';
import '../../../domain/entities/react_info.dart';
import '../../../domain/entities/story_list.dart';
import '../../../injector/injector.dart';
import '../../account/widgets/account_body.dart';
import '../../collaborator/profile/view/profile_gallery_page.dart';
import '../../create_chat_group/bloc/bloc.dart';
import '../../story_list/view/story_image_detail_page.dart';
import '../../story_list/widgets/story_all_body.dart';
import '../../story_list/widgets/story_body.dart';
import '../../story_list/widgets/story_boss_body.dart';
import '../../story_list/widgets/story_image.dart';
import '../../story_list/widgets/story_loading_more.dart';
import '../../tag_by_result_image_list/view/preview_image_page.dart';
import '../../widgets/widgets.dart';
import '../bloc/story_person_list_bloc.dart';
import 'story_person_contact.dart';
import 'story_person_loading.dart';
import 'story_person_loading2.dart';
import 'story_person_post_card.dart';
import 'story_person_progress.dart';

class StoryPersonListBody extends StatefulWidget {
  const StoryPersonListBody({final Key? key, this.codeUser}) : super(key: key);
  final String? codeUser;

  static ValueNotifierList<StoryListItems> items = ValueNotifierList([]);
  static ValueNotifier<CreatedByInfo?> user = ValueNotifier(null);
  static bool isFromBody = false;
  @override
  State<StoryPersonListBody> createState() => _StoryPersonListBodyState();
}

class _StoryPersonListBodyState extends State<StoryPersonListBody> {
  final ScrollController scrollController = ScrollController();
  final ValueNotifier<bool> hidenShowTile = ValueNotifier(false);
  ValueNotifier<VideoPlayerController?> videoPlayerController = ValueNotifier(
    null,
  );
  LikeStorySuccess? dataReactInfo;
  int page = 1;
  @override
  void initState() {
    super.initState();
    StoryPersonListBody.user.value = null;
    scrollController.addListener(() {
      final maxScroll = scrollController.position.maxScrollExtent;
      final currentScroll = scrollController.offset;
      if (maxScroll > 300) {
        _lazyStoryAll(maxScroll, currentScroll);
      }
      hidenShowTile.value =
          (scrollController.offset > 280 - kToolbarHeight) &&
          scrollController.hasClients;
    });
  }

  void _lazyStoryAll(final double maxScroll, final double currentScroll) {
    if (!isEndList.value) {
      if (!isLoadingMore.value) {
        if (currentScroll >= (maxScroll * 0.9)) {
          page++;
          context.read<StoryPersonListBloc>().add(
            StoryPersonListGetMore(
              params: StoryPersonRequestParams(
                id: widget.codeUser ?? Strings.empty,
                dataContent: StoryPersonListRequestParams(page: page),
              ),
            ),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    scrollController.dispose();
    StoryPersonListBody.isFromBody = false;
    super.dispose();
  }

  final ValueNotifier<bool> isLoadingMore = ValueNotifier(false);
  final ValueNotifier<bool> isEndList = ValueNotifier(false);
  StoryList? dataList;
  StoryListItems? dataNew;
  CreatedByInfo? dataUser;
  int countFile = 0;
  StoryList? dataMore;
  @override
  Widget build(final BuildContext context) {
    return BlocListener<CreateChatGroupBloc, CreateChatGroupState>(
      listener: (final context, final state) {
        if (state.status == CreateChatGroupStatus.success) {
          final data = Utils.getData<CreateChatGroup>(state.data);
          unawaited(
            context.router.push(
              ChatRoute(conversationId: data?.conversation?.id),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: RefreshIndicator(
          onRefresh: () async {
            page = 1;
            context.read<StoryPersonListBloc>().add(
              StoryPersonListStarted(
                StoryPersonRequestParams(
                  id: widget.codeUser ?? Strings.empty,
                  dataContent: StoryPersonListRequestParams(page: page),
                ),
              ),
            );
          },
          child: CustomScrollView(
            controller: scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: <Widget>[
              BlocConsumer<StoryPersonListBloc, StoryPersonListState>(
                listener: (final context, final state) {
                  if (state.status == StoryPersonListStatus.getUsersuccess) {
                    StoryPersonListBody.user.value = Utils.getData(state.data);
                  }
                },
                builder: (final context, final state) {
                  if (state.status == StoryPersonListStatus.loadingUser ||
                      state.status == StoryPersonListStatus.init) {
                    return const StoryPersonLoading();
                  } else {
                    return ValueListenableBuilder(
                      valueListenable: hidenShowTile,
                      builder: (final context, final isShow, final child) {
                        return SliverAppBar(
                          leading: IconButton(
                            style: IconButton.styleFrom(
                              backgroundColor: isShow
                                  ? Colors.white
                                  : Colors.black.withValues(alpha: .5),
                            ),
                            onPressed: () async {
                              context.router.popForced();
                            },
                            icon: EZResources.image(
                              ImageParams(
                                name: AppIcons.icBack,
                                color: !isShow ? Colors.white : Colors.black,
                              ),
                            ),
                          ),
                          actions: !isShow
                              ? const []
                              : EZCache.shared.getUserProfile()?.employeeId !=
                                    dataUser?.username
                              ? [
                                  InkWell(
                                    onTapDown: (final details) async =>
                                        _onCallHandler(details.globalPosition),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: EZResources.image(
                                        ImageParams(name: AppIcons.icCallChat),
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      _pushToChatPage(context);
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: EZResources.image(
                                        ImageParams(
                                          name: AppIcons.icGreyMess,
                                          size: const ImageSize(24, 24),
                                        ),
                                      ),
                                    ),
                                  ),
                                ]
                              : [],
                          centerTitle: false,
                          pinned: true,
                          stretch: true,
                          title: !isShow
                              ? const SizedBox()
                              : SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(32),
                                        child: SizedBox(
                                          width: 36,
                                          height: 36,
                                          child: EzCachedNetworkImage(
                                            imageUrl:
                                                StoryPersonListBody
                                                    .user
                                                    .value
                                                    ?.avatar ??
                                                '',
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8.0),
                                      Text(
                                        dataUser?.name ?? '',
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                          expandedHeight: 250.0,
                          flexibleSpace: FlexibleSpaceBar(
                            collapseMode: CollapseMode.pin,
                            background: Stack(
                              children: [
                                Column(
                                  children: [
                                    ValueListenableBuilder(
                                      valueListenable: StoryPersonListBody.user,
                                      builder:
                                          (
                                            final context,
                                            final vDataUser,
                                            final child,
                                          ) {
                                            return Expanded(
                                              child: _buildBackground(
                                                vDataUser,
                                                context,
                                              ),
                                            );
                                          },
                                    ),
                                    const SizedBox(
                                      width: double.infinity,
                                      height: 60,
                                      child: ColoredBox(
                                        color: Color(0xffF6F7FB),
                                      ),
                                    ),
                                  ],
                                ),
                                _buildAlignStoryUser(),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  }
                },
              ),
              BlocConsumer<StoryPersonListBloc, StoryPersonListState>(
                listener: (final context, final state) {
                  if (state.status == StoryPersonListStatus.getUsersuccess) {
                    dataUser = Utils.getData(state.data);
                  }
                },
                builder: (final context, final state) {
                  if (state.status == StoryPersonListStatus.loadingUser ||
                      state.status == StoryPersonListStatus.init) {
                    return const SliverToBoxAdapter();
                  } else {
                    return SliverToBoxAdapter(
                      child: SizedBox(
                        child: ColoredBox(
                          color: const Color(0xffF6F7FB),
                          child: Column(
                            children: [
                              Text(
                                dataUser?.name ?? '',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              if (dataUser != null &&
                                  EZCache.shared.getUserProfile()?.employeeId !=
                                      dataUser?.username)
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      StoryPersonContact(
                                        pathIcon: AppIcons.icPhone,
                                        lable: context.l10n.call2,
                                        onTap: (final offset) async =>
                                            _onCallHandler(offset),
                                      ),
                                      const SizedBox(width: 16),
                                      StoryPersonContact(
                                        pathIcon: AppIcons.icGreenMess,
                                        lable: context.l10n.messenger,
                                        onTap: (final offset) {
                                          _pushToChatPage(context);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                ),
                                child: Text(
                                  dataUser?.bio ?? '',
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ),
                              const SizedBox(height: 8),
                              if (dataUser != null &&
                                  EZCache.shared.getUserProfile()?.employeeId ==
                                      dataUser?.username)
                                _buildStoryWrite(context),
                              const SizedBox(height: 8),
                            ],
                          ),
                        ),
                      ),
                    );
                  }
                },
              ),
              BlocConsumer<StoryPersonListBloc, StoryPersonListState>(
                listener: (final context, final state) {
                  if (state.status == StoryPersonListStatus.success) {
                    dataList = Utils.getData(state.data);
                    final listSafety = dataList?.items
                        .map(
                          (final e) =>
                              e ?? StoryListItems(attachment: [], tags: []),
                        )
                        .toList();
                    StoryPersonListBody.items.setValue(listSafety);
                  }
                  if (state.status == StoryPersonListStatus.loadingPost ||
                      state.status == StoryPersonListStatus.loadingUpdate) {
                    StoryBody.countProgress.value = ProgressParams(
                      count: 0,
                      total: 0,
                      pageCurrent: StoryPersonListBody.isFromBody
                          ? PageCurrent.storyPerson
                          : PageCurrent.storyBody,
                    );
                  }
                  if (state.status == StoryPersonListStatus.deleteSuccess) {
                    final iWhere = StoryPersonListBody.items.value.indexWhere(
                      (final e) => e.id == state.id,
                    );
                    final iWhereAll = StoryAllBody.items.value.indexWhere(
                      (final e) => e.id == state.id,
                    );
                    final iWhereBoss = StoryBossBody.items.value.indexWhere(
                      (final e) => e.id == state.id,
                    );
                    if (iWhereAll != -1) {
                      StoryAllBody.items.removeIndex(iWhereAll);
                    }
                    if (iWhereBoss != -1) {
                      StoryBossBody.items.removeIndex(iWhereBoss);
                    }
                    StoryPersonListBody.items.removeIndex(iWhere);
                  }
                  if (state.status == StoryPersonListStatus.postSuccess) {
                    StoryBody.listAttachMent.setValue([]);
                    dataNew = Utils.getData(state.data);
                    StoryPersonListBody.items.addFirstItem(
                      dataNew ?? StoryListItems(attachment: [], tags: []),
                    );
                  }
                  if (state.status == StoryPersonListStatus.updateSuccess) {
                    StoryBody.listAttachMent.setValue([]);
                    dataNew = Utils.getData(state.data);
                    final iWhere = StoryPersonListBody.items.value.indexWhere(
                      (final e) => e.id == dataNew?.id,
                    );
                    final StoryListItems itemNew = StoryPersonListBody
                        .items
                        .value[iWhere]
                        .copyWith(
                          attachment: dataNew?.attachment,
                          tags: dataNew?.tags,
                          updatedAt: dataNew?.updatedAt,
                          content: dataNew?.content,
                          location: dataNew?.location,
                          emoji: dataNew?.emoji,
                          theme: dataNew?.theme,
                          mention: dataNew?.mention,
                          tagsInfo: dataNew?.tagsInfo,
                        );
                    StoryPersonListBody.items.updateValuebyIndex(
                      iWhere,
                      itemNew,
                    );
                  }
                  if (state.status == StoryPersonListStatus.likeSuccess) {
                    dataReactInfo = Utils.getData(state.data);
                    final iWhere = StoryPersonListBody.items.value.indexWhere(
                      (final e) => e.id == dataReactInfo?.postId,
                    );
                    final iWhereAll = StoryAllBody.items.value.indexWhere(
                      (final e) => e.id == dataReactInfo?.postId,
                    );
                    final iWhereBoss = StoryBossBody.items.value.indexWhere(
                      (final e) => e.id == dataReactInfo?.postId,
                    );
                    final ReactInfo? reactInfo =
                        dataReactInfo?.isReaction ?? false
                        ? ReactInfo(
                            postId: dataReactInfo?.postId,
                            reactType: dataReactInfo?.reactionType,
                            createdBy: dataReactInfo?.reactionBy,
                          )
                        : null;
                    if (iWhereAll != -1) {
                      StoryAllBody.items.value[iWhereAll] = StoryAllBody
                          .items
                          .value[iWhereAll]
                          .copyWith(
                            reactInfo: reactInfo,
                            reactCount: dataReactInfo?.reactCount,
                          );
                      StoryAllBody.items.setValue(StoryAllBody.items.value);
                    }
                    if (iWhereBoss != -1) {
                      StoryBossBody.items.value[iWhereBoss] = StoryBossBody
                          .items
                          .value[iWhereBoss]
                          .copyWith(
                            reactInfo: reactInfo,
                            reactCount: dataReactInfo?.reactCount,
                          );
                      StoryBossBody.items.setValue(StoryBossBody.items.value);
                    }
                    StoryPersonListBody.items.value[iWhere] =
                        StoryPersonListBody.items.value[iWhere].copyWith(
                          reactInfo: reactInfo,
                          reactCount: dataReactInfo?.reactCount,
                        );
                    StoryPersonListBody.items.setValue(
                      StoryPersonListBody.items.value,
                    );
                  }
                  if (state.status == StoryPersonListStatus.loadingMore) {
                    isLoadingMore.value = true;
                  }
                  if (state.status == StoryPersonListStatus.loadMoreSuccess) {
                    dataMore = Utils.getData(state.data);
                    if (dataMore?.items.isEmpty ?? false) {
                      isEndList.value = true;
                    }
                    final listSafety = dataMore?.items
                        .map(
                          (final e) =>
                              e ?? StoryListItems(attachment: [], tags: []),
                        )
                        .toList();
                    StoryPersonListBody.items.addLastList(listSafety);
                    isLoadingMore.value = false;
                  }
                  if (state.status == StoryPersonListStatus.failure) {
                    StoryBody.listAttachMent.setValue([]);
                    unawaited(
                      ApiErrorDialog.show(
                        ApiErrorParams(context, Utils.getData(state.data)),
                      ),
                    );
                  }
                },
                builder: (final context, final state) {
                  if (state.status == StoryPersonListStatus.loading) {
                    return const StoryPersonLoading2();
                  }
                  return ValueListenableBuilder<List<StoryListItems>>(
                    valueListenable: StoryPersonListBody.items,
                    builder: (final context, final vItems, final child) {
                      return SliverMainAxisGroup(
                        slivers: [
                          ValueListenableBuilder(
                            valueListenable: StoryBody.countProgress,
                            builder:
                                (final context, final vProgress, final child) {
                                  if (vProgress.count != 0 &&
                                      (vProgress.count ?? 0) <
                                          (vProgress.total ?? 0)) {
                                    return StoryPersonProgress(
                                      count: vProgress.count?.toDouble() ?? 0,
                                      total: vProgress.total?.toDouble() ?? 0,
                                    );
                                  } else if (vProgress.count != 0 &&
                                      (vProgress.count ?? 0) ==
                                          (vProgress.total ?? 0)) {
                                    if (vProgress.isDone) {
                                      final dataContent =
                                          StoryWriteInfoRequestParams(
                                            content:
                                                vProgress.storyInfo?.content ??
                                                Strings.empty,
                                            attachment: StoryBody
                                                .listAttachMent
                                                .value
                                                .map(
                                                  (final e) => Attachment(
                                                    originalname:
                                                        e.originalname,
                                                    id: e.id,
                                                    size: e.size,
                                                    mimetype: e.mimetype,
                                                    thumbnail: e.thumbnail,
                                                    link: e.link,
                                                  ),
                                                )
                                                .toList(),
                                            tags:
                                                vProgress.storyInfo?.tags ?? [],
                                            location:
                                                vProgress.storyInfo?.location,
                                            theme: vProgress.storyInfo?.theme,
                                            mention:
                                                vProgress.storyInfo?.mention,
                                            emoji: vProgress.storyInfo?.emoji,
                                          );
                                      if (vProgress.isUpdate) {
                                        context.read<StoryPersonListBloc>().add(
                                          UpdateStoryPersonEvent(
                                            params: StoryUpdateRequestParams(
                                              id: StoryBody.idPost,
                                              dataContent: dataContent,
                                            ),
                                          ),
                                        );
                                      } else {
                                        if (!StoryPersonListBody.isFromBody) {
                                          context
                                              .read<StoryPersonListBloc>()
                                              .add(
                                                PostStoryPersonEvent(
                                                  params: dataContent,
                                                ),
                                              );
                                        } else {
                                          if (vProgress.pageCurrent ==
                                              PageCurrent.storyPerson) {
                                            context
                                                .read<StoryPersonListBloc>()
                                                .add(
                                                  PostStoryPersonEvent(
                                                    params: dataContent,
                                                  ),
                                                );
                                          }
                                        }
                                      }
                                    }
                                    return const StoryPersonProgress(
                                      count: 1,
                                      total: 1,
                                    );
                                  }
                                  return const SliverToBoxAdapter(
                                    child: SizedBox(),
                                  );
                                },
                          ),
                          _buildStoryPersonListBody(),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  SizedBox _buildBackground(
    final CreatedByInfo? vDataUser,
    final BuildContext context,
  ) {
    return SizedBox(
      width: double.infinity,
      child: GestureDetector(
        onTap: () async {
          if (vDataUser?.username ==
              EZCache.shared.getUserProfile()?.employeeId) {
            openOptionImage(vDataUser?.username ?? '');
          } else {
            final listWidgetAttachment =
                [
                  ImageByComboTagImages(
                    imageUrl: vDataUser?.coverImage ?? '',
                    tags: [],
                  ),
                ].map((final eWidget) {
                  return StoryImage(
                    originalWidth: 1,
                    originalHeight: 1,
                    tags: vDataUser?.coverImage ?? '',
                    imageUrl: '${eWidget.imageUrl}',
                    fit: BoxFit.contain,
                    disableGestures: null,
                    initialScale: PhotoViewComputedScale.contained,
                    minScale: PhotoViewComputedScale.contained,
                    maxScale: PhotoViewComputedScale.contained * 1.1,
                  );
                }).toList();
            PreviewImagePage.listMedia.setValue(listWidgetAttachment);
            context.router.push(PreviewImageRoute(initIndex: 0, roomName: ''));
          }
        },
        child: EzCachedNetworkImage(
          imageUrl: vDataUser?.coverImage ?? '',
          errorWidget: backgroundError(vDataUser?.username ?? ''),
        ),
      ),
    );
  }

  ValueListenableBuilder<List<StoryListItems>> _buildStoryPersonListBody() {
    return ValueListenableBuilder<List<StoryListItems>>(
      valueListenable: StoryPersonListBody.items,
      builder: (final context, final vItems, final child) {
        return vItems.isEmpty
            ? SliverToBoxAdapter(
                child: Center(child: Text(context.l10n.listIsEmpty)),
              )
            : ValueListenableBuilder<bool>(
                valueListenable: isEndList,
                builder: (final context, final vEndList, final child) {
                  return ValueListenableBuilder<bool>(
                    valueListenable: isLoadingMore,
                    builder: (final context, final vLoadingMore, final child) {
                      return _buildStoryPersonList(
                        vEndList: vEndList,
                        vItems: vItems,
                        vLoadingMore: vLoadingMore,
                      );
                    },
                  );
                },
              );
      },
    );
  }

  Align _buildAlignStoryUser() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: ValueListenableBuilder(
        valueListenable: StoryPersonListBody.user,
        builder: (final context, final vDataUser, final child) {
          return _buildStoryUser(context, vDataUser);
        },
      ),
    );
  }

  Container _buildStoryUser(
    final BuildContext context,
    final CreatedByInfo? vDataUser,
  ) {
    return Container(
      width: 120,
      height: 120,
      padding: const EdgeInsets.all(5),
      decoration: const ShapeDecoration(
        color: Colors.white,
        shape: CircleBorder(),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(64),
        child: ColoredBox(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: GestureDetector(
            onTap: () async {
              if (vDataUser?.username ==
                  EZCache.shared.getUserProfile()?.employeeId) {
                openOptionAvatar(vDataUser?.username ?? '');
              } else {
                final listWidgetAttachment =
                    [
                      ImageByComboTagImages(
                        imageUrl: vDataUser?.avatar ?? '',
                        tags: [],
                      ),
                    ].map((final eWidget) {
                      return StoryImage(
                        originalWidth: 1,
                        originalHeight: 1,
                        tags: vDataUser?.avatar ?? '',
                        imageUrl: '${eWidget.imageUrl}',
                        fit: BoxFit.contain,
                        disableGestures: null,
                        initialScale: PhotoViewComputedScale.contained,
                        minScale: PhotoViewComputedScale.contained,
                        maxScale: PhotoViewComputedScale.contained * 1.1,
                      );
                    }).toList();
                PreviewImagePage.listMedia.setValue(listWidgetAttachment);
                context.router.push(
                  PreviewImageRoute(initIndex: 0, roomName: ''),
                );
              }
            },
            child: EzCachedNetworkImage(
              imageUrl: vDataUser?.avatar ?? '',
              errorWidget: GestureDetector(
                onTap:
                    vDataUser?.username ==
                        EZCache.shared.getUserProfile()?.employeeId
                    ? () async {
                        openUpdateAvatar();
                      }
                    : null,
                child: _buildAvatarGadient(context, vDataUser?.username ?? ''),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget backgroundError(final String userName) {
    return GestureDetector(
      onTap: () async {
        openUpdateBackgroundImage(userName);
      },
      child: _buildDecorationGadient(userName),
    );
  }

  Widget _buildDecorationGadient(final String name) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Utils.randomColor(name).withValues(alpha: 0.5),
            Utils.randomColor(name),
          ],
        ),
      ),
      child: const SizedBox(),
    );
  }

  Future<void> openUpdateAvatar() async {
    showModalBottomSheet(
      context: context,
      builder: (final contextBottomSheet) => ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(context.l10n.updateAvatar),
              onTap: () async {
                Navigator.of(context).pop();
                context.router.pushNamed(Routes.gallery);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> openImage() async {
    Navigator.of(context).pop();
    StoryImageDetailPage.listMediaPreview.setValue([]);
    StoryImageDetailPage.listMedia.setValue([
      Material(
        color: Colors.white.withValues(alpha: 0),
        child: Center(
          child: StoryImage(
            originalWidth: 1,
            originalHeight: 1,
            tags: '${dataUser?.username}',
            imageUrl:
                StoryPersonListBody.user.value?.avatar ??
                dataUser?.avatar ??
                '',
            fit: BoxFit.contain,
            disableGestures: null,
            initialScale: PhotoViewComputedScale.contained,
            minScale: PhotoViewComputedScale.contained,
            maxScale: PhotoViewComputedScale.contained * 1.1,
          ),
        ),
      ),
    ]);
    Navigator.push(
      context,
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (final _, final __, final ___) => StoryImageDetailPage(
          tags: [dataUser?.username ?? ''],
          file: Attachment(
            link: dataUser?.avatar ?? '',
            originalname: dataUser?.avatar?.split('/').lastOrNull ?? '',
          ),
          initIndex: 0,
          url: [dataUser?.avatar ?? ''],
          fileName: const [''],
        ),
      ),
    );
  }

  Future<void> openOptionAvatar(final String id) async {
    showModalBottomSheet(
      context: context,
      builder: (final contextBottomSheet) => ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(context.l10n.watchImage),
              onTap: () async {
                openImage();
              },
            ),
            if (id == EZCache.shared.getUserProfile()?.employeeId)
              ListTile(
                title: Text(context.l10n.updateAvatar),
                onTap: () async {
                  Navigator.of(context).pop();
                  context.router.pushNamed(Routes.gallery);
                },
              ),
          ],
        ),
      ),
    );
  }

  Future<void> openOptionImage(final String id) async {
    showModalBottomSheet(
      context: context,
      builder: (final contextBottomSheet) => ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(context.l10n.watchImage),
              onTap: () async {
                Navigator.of(context).pop();
                StoryImageDetailPage.listMediaPreview.setValue([]);
                StoryImageDetailPage.listMedia.setValue([
                  Material(
                    color: Colors.white.withValues(alpha: 0),
                    child: Center(
                      child: StoryImage(
                        originalWidth: 1,
                        originalHeight: 1,
                        tags: '${dataUser?.username}',
                        imageUrl:
                            StoryPersonListBody.user.value?.coverImage ??
                            dataUser?.coverImage ??
                            '',
                        fit: BoxFit.contain,
                        disableGestures: null,
                        initialScale: PhotoViewComputedScale.contained,
                        minScale: PhotoViewComputedScale.contained,
                        maxScale: PhotoViewComputedScale.contained * 1.1,
                      ),
                    ),
                  ),
                ]);
                Navigator.push(
                  context,
                  PageRouteBuilder(
                    opaque: false,
                    pageBuilder: (final _, final __, final ___) =>
                        StoryImageDetailPage(
                          tags: [dataUser?.username ?? ''],
                          file: Attachment(
                            link: dataUser?.coverImage ?? '',
                            originalname:
                                dataUser?.coverImage?.split('/').lastOrNull ??
                                '',
                          ),
                          initIndex: 0,
                          url: [dataUser?.coverImage ?? ''],
                          fileName: const [''],
                        ),
                  ),
                );
              },
            ),
            if (id == EZCache.shared.getUserProfile()?.employeeId)
              ListTile(
                title: Text(context.l10n.updateBackground),
                onTap: () async {
                  Navigator.of(context).pop();
                  context.router.push(
                    ProfileGalleryRoute(isUpdateBackground: true),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Future<void> openUpdateBackgroundImage(final String id) async {
    showModalBottomSheet(
      context: context,
      builder: (final contextBottomSheet) => ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (id == EZCache.shared.getUserProfile()?.employeeId)
              ListTile(
                title: Text(context.l10n.updateBackground),
                onTap: () async {
                  Navigator.of(context).pop();
                  context.router.push(
                    ProfileGalleryRoute(isUpdateBackground: true),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  SliverList _buildStoryPersonList({
    required final List<StoryListItems> vItems,
    required final bool vLoadingMore,
    required final bool vEndList,
  }) {
    return SliverList.builder(
      itemCount: (vLoadingMore || vEndList) ? vItems.length + 1 : vItems.length,
      itemBuilder: (final context, final i) {
        if (i == vItems.length && vLoadingMore) {
          return const StoryLoadingMore();
        } else if (i == vItems.length && vEndList) {
          return Center(
            child: Text(
              context.l10n.isEnd,
              style: TextStyle(
                color: Theme.of(context).hintColor.withValues(alpha: .7),
              ),
            ),
          );
        } else {
          final item = vItems[i];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: StoryPersonPostCard(key: ValueKey(item.id), item: item),
          );
        }
      },
    );
  }

  Future<void> _onCallHandler(final Offset offset) async {
    showMenu(
      context: context,
      color: Theme.of(context).scaffoldBackgroundColor,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy,
        MediaQuery.sizeOf(context).width - offset.dx,
        MediaQuery.sizeOf(context).height - offset.dy,
      ),
      items: [
        PopupMenuItem(
          onTap: () async {
            final canCall =
                (await EZCache.shared.configurations)?.canCall ?? true;
            if (!canCall) {
              if (mounted) {
                Alert.showAlert(
                  AlertParams(context, context.l10n.callIsBlocked),
                );
              }
              return;
            }

            final to = widget.codeUser;
            final toName = dataUser?.name;
            if (mounted) {
              LoadingDialog.instance
                  .show(context)
                  .then(
                    (_) => Future.delayed(const Duration(seconds: 2), () {
                      if (LoadingDialog.isShowing) {
                        final context =
                            getIt<AppRouter>().navigatorKey.currentContext;
                        if (context != null) {
                          // need to use currentContext
                          // ignore: use_build_context_synchronously
                          LoadingDialog.instance.hide(context);
                        }
                      }
                    }),
                  );
            }
            PlatformChannelHelper.onMakeCallNotifier(
              to: to,
              toName: toName,
              avatarUrl: dataUser?.avatar,
            );
          },
          child: Row(
            children: [
              Icon(Icons.wifi, color: Theme.of(context).hintColor, size: 20),
              const SizedBox(width: 8),
              Text(
                context.l10n.callByApp,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        PopupMenuItem(
          onTap: () => Utils.onTapDynamicLink(
            link: dataUser?.phone,
            linkType: LinkType.phone,
          ),
          child: Row(
            children: [
              Icon(
                Icons.phone_iphone_outlined,
                size: 20,
                color: Theme.of(context).hintColor,
              ),
              const SizedBox(width: 8),
              Text(
                dataUser?.phone ?? '',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _pushToChatPage(final BuildContext context) {
    final user = EZCache.shared.getUserProfile();
    context.read<CreateChatGroupBloc>().add(
      CreateChatGroupStarted(
        CreateChatGroupRequestParams(
          avatar: dataUser?.avatar,
          name: dataUser?.name,
          members: [user?.employeeId, widget.codeUser],
          isGroup: false,
        ),
      ),
    );
  }

  Widget _buildStoryWrite(final BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        height: 72,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              GestureDetector(
                onTap: () async {
                  context.router.pushNamed(Routes.storyWrite).then((
                    final value,
                  ) {
                    if (value != null) {
                      countFile = value as int;
                    }
                  });
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(45.0),
                  child: ValueListenableBuilder(
                    valueListenable: AccountBody.user,
                    builder: (final context, final vDataUser, final child) {
                      return SizedBox(
                        width: 40.0,
                        height: 40.0,
                        child: EzCachedNetworkImage(
                          imageUrl:
                              vDataUser?.avatar ??
                              dataUser?.avatar ??
                              EZCache.shared.getUserProfile()?.avatar ??
                              Strings.empty,
                        ),
                      );
                    },
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: .5),
                      ),
                    ),
                    child: GestureDetector(
                      onTap: () async {
                        context.router.pushNamed(Routes.storyWrite).then((
                          final value,
                        ) {
                          if (value != null) {
                            countFile = value as int;
                          }
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text(context.l10n.statusHint),
                      ),
                    ),
                  ),
                ),
              ),
              IconButton(
                onPressed: () async {
                  context.router.push(
                    ProfileGalleryRoute(
                      isFromProfile: FromPage.storyBody,
                      isMultiple: true,
                    ),
                  );
                },
                icon: EZResources.image(
                  ImageParams(name: AppIcons.icGreenImage),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatarGadient(
    final BuildContext context,
    final String userName,
  ) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Utils.randomColor(userName).withValues(alpha: 0.5),
            Utils.randomColor(userName),
          ],
        ),
      ),
      child: SizedBox(
        width: 40,
        height: 40,
        child: Center(
          child: Text(
            dataUser?.name?.getLastWord().firstOrEmpty() ?? '',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.white),
          ),
        ),
      ),
    );
  }
}
