// Dart imports:
import 'dart:async';
import 'dart:math';
import 'dart:ui';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:app_config/app_config.dart';
import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' show PreviewData;
import 'package:flutter_link_previewer/flutter_link_previewer.dart' hide Size;
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:linkfy_text/linkfy_text.dart';
import 'package:path/path.dart' show extension;
import 'package:pro_image_editor/pro_image_editor.dart' hide LoadingDialog;

// Project imports:
import '../../../config/enums/conversation_type.dart';
import '../../../config/enums/file_extension_type.dart';
import '../../../config/enums/group_chat_action.dart';
import '../../../config/enums/user_chat_role.dart';
import '../../../config/enums/user_chat_rule.dart';
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/nd_progresshud/loading_dialog.dart';
import '../../../core/nd_video_player/video_player.dart';
import '../../../core/params/chat_get_by_id_request_params.dart';
import '../../../core/params/request_params.dart' hide TaskInfo;
import '../../../core/routes/app_router.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../core/utils/platform_channel_helper.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/entities.dart' hide ChatUploadFile;
import '../../../injector/injector.dart';
import '../../../services/nd_downloader/nd_downloader.dart';
import '../../_blocs/general_bloc/general_bloc.dart';
import '../../widgets/chat_search_bar.dart';
import '../../widgets/widgets.dart';
import '../bloc/group_chat_detail_bloc.dart';
import 'add_admin_bottom_sheet.dart';
import 'add_exception_view.dart';
import 'admins_view.dart';
import 'invite_link_view.dart';
import 'member_permission_view.dart';
import 'member_tile.dart';

class GroupChatDetailBody extends StatefulWidget {
  const GroupChatDetailBody({final Key? key, required this.conversation})
    : super(key: key);

  final ChatListItems? conversation;

  @override
  State<GroupChatDetailBody> createState() => _GroupChatDetailBodyState();
}

class _GroupChatDetailBodyState extends State<GroupChatDetailBody>
    with SingleTickerProviderStateMixin {
  bool editMode = false;
  TextEditingController groupNameController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  String? groupAvatar;

  final PagingController<int, GroupChatDetailFileLoadItems?>
  _filePagingController = PagingController(firstPageKey: 1);
  final PagingController<int, GroupChatDetailMediaLoadItems?>
  _mediaPagingController = PagingController(firstPageKey: 1);
  final PagingController<int, GroupChatDetailLinkLoadItems?>
  _linkPagingController = PagingController(firstPageKey: 1);
  final PagingController<int, GroupChatDetailMemberInfoLoadItems?>
  _memberPagingController = PagingController(firstPageKey: 1);
  Map<String, PreviewData> previewDataList = {};
  ScrollController scrollController = ScrollController();
  late TabController _tabController;
  int currentPage = 1;
  int memberPage = 1;
  String? lastUpdate;
  final ValueNotifier<bool> _isVisibleSearchMedia = ValueNotifier(false);
  bool isSearchMode = false;
  Timer? timer;
  double blurValue = 0;

  GroupChatDetailGetRuleByRoleRole? adminRole;
  GroupChatDetailGetRuleByRoleRole? memberRole;
  List<String?> userRules = [];
  String phone = '';
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      vsync: this,
      length: (widget.conversation?.isGroup ?? false) ? 4 : 3,
    );
    groupNameController = TextEditingController(
      text: widget.conversation?.name,
    );
    groupAvatar = widget.conversation?.avatar;
    phone =
        widget.conversation?.membersInfo
            .firstWhereOrNull(
              (final e) =>
                  e?.username != EZCache.shared.getUserProfile()?.employeeId,
            )
            ?.phone ??
        '';

    _filePagingController.addPageRequestListener((final pageKey) {
      currentPage = pageKey;
      if (currentPage == 1) {
        lastUpdate = '';
      }

      context.read<GroupChatDetailBloc>().add(
        GroupChatDetailFileLoaded(
          GroupChatDetailFileLoadRequestParams(
            conversationId: widget.conversation?.id,
            createdAtBefore: lastUpdate,
            search: searchController.text,
          ),
        ),
      );
    });

    _mediaPagingController.addPageRequestListener((final pageKey) {
      currentPage = pageKey;
      if (currentPage == 1) {
        lastUpdate = '';
      }

      context.read<GroupChatDetailBloc>().add(
        GroupChatDetailMediaLoaded(
          GroupChatDetailMediaLoadRequestParams(
            conversationId: widget.conversation?.id,
            createdAtBefore: lastUpdate,
            search: searchController.text,
          ),
        ),
      );
    });

    _linkPagingController.addPageRequestListener((final pageKey) {
      currentPage = pageKey;
      if (currentPage == 1) {
        lastUpdate = '';
      }

      context.read<GroupChatDetailBloc>().add(
        GroupChatDetailLinkLoaded(
          GroupChatDetailLinkLoadRequestParams(
            conversationId: widget.conversation?.id,
            createdAtBefore: lastUpdate,
            search: searchController.text,
          ),
        ),
      );
    });

    _memberPagingController.addPageRequestListener((final pageKey) {
      memberPage = pageKey;

      context.read<GroupChatDetailBloc>().add(
        GroupChatDetailMemberInfoLoaded(
          GroupChatDetailMemberInfoLoadRequestParams(
            conversationId: widget.conversation?.id,
            page: memberPage,
            search: searchController.text,
          ),
        ),
      );
    });

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        _refreshPage();
      }
    });
  }

  @override
  void dispose() {
    _filePagingController.dispose();
    _mediaPagingController.dispose();
    _linkPagingController.dispose();
    _memberPagingController.dispose();
    scrollController.dispose();
    _tabController.dispose();
    groupNameController.dispose();
    searchController.dispose();
    _isVisibleSearchMedia.dispose();
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<GroupChatDetailBloc, GroupChatDetailState>(
      listener: (final context, final state) {
        if (state.status == GroupChatDetailStatus.failure) {
          unawaited(
            ApiErrorDialog.show(
              ApiErrorParams(context, Utils.getData(state.data)),
            ),
          );
        }
        if (state.status == GroupChatDetailStatus.updateAvatarSuccess) {
          final GroupChatDetailUpdate response = Utils.getData(state.data);
          groupAvatar = response.conversation?.avatar;
        }
        if (state.status == GroupChatDetailStatus.updateSuccess) {
          widget.conversation?.name = groupNameController.text;
          unawaited(
            Alert.showAlert(
              AlertParams(
                context,
                context.l10n.updateSuccess,
                onPressOK: () {
                  editMode = false;
                  setState(() {});
                },
              ),
            ),
          );
        }
        if (state is GroupChatDetailRemoveMemberSuccess) {
          _memberPagingController.itemList?.removeWhere(
            (final element) => element?.username == state.username,
          );
          widget.conversation?.membersInfo.removeWhere(
            (final element) => element?.username == state.username,
          );
          Navigator.of(context).pop();
        }
        if (state.status == GroupChatDetailStatus.leaveGroupSuccess ||
            state.status == GroupChatDetailStatus.deleteGroupSuccess) {
          Navigator.of(context)
            ..pop()
            ..pop(widget.conversation?.id);
        }
        if (state.status == GroupChatDetailStatus.fileLoadSuccess) {
          final GroupChatDetailFileLoad? data = Utils.getData(state.data);
          final newItems = data?.items ?? [];
          if (newItems.length < 10) {
            _filePagingController.appendLastPage(newItems);
          } else {
            lastUpdate = newItems.lastOrNull?.createdAt;
            currentPage++;
            _filePagingController.appendPage(newItems, currentPage);
          }
        }

        if (state.status == GroupChatDetailStatus.mediaLoadSuccess) {
          final GroupChatDetailMediaLoad? data = Utils.getData(state.data);
          final newItems = data?.items ?? [];
          if (newItems.length < 10) {
            _mediaPagingController.appendLastPage(newItems);
          } else {
            lastUpdate = newItems.lastOrNull?.createdAt;
            currentPage++;
            _mediaPagingController.appendPage(newItems, currentPage);
          }
        }
        if (state.status == GroupChatDetailStatus.linkLoadSuccess) {
          final GroupChatDetailLinkLoad? data = Utils.getData(state.data);
          final newItems = data?.items ?? [];
          if (newItems.length < 10) {
            _linkPagingController.appendLastPage(newItems);
          } else {
            lastUpdate = newItems.lastOrNull?.createdAt;
            currentPage++;
            _linkPagingController.appendPage(newItems, currentPage);
          }
        }

        if (state.status == GroupChatDetailStatus.memberInfoLoadSuccess) {
          final GroupChatDetailMemberInfoLoad? data = Utils.getData(state.data);
          final newItems = data?.items ?? [];
          if (newItems.length < 10) {
            _memberPagingController.appendLastPage(newItems);
          } else {
            memberPage++;
            _memberPagingController.appendPage(newItems, memberPage);
          }
        }
        if (state.status == GroupChatDetailStatus.getRuleByRoleSuccess) {
          final GroupChatDetailGetRuleByRole? data = Utils.getData(state.data);
          if (data?.role?.name == UserChatRole.admin.name) {
            adminRole = data?.role;
          }
          if (data?.role?.name == UserChatRole.member.name) {
            memberRole = data?.role;
          }
        }

        if (state.status == GroupChatDetailStatus.getUserRulesSuccess) {
          final GroupChatDetailGetUserRules? data = Utils.getData(state.data);
          if (data?.role?.name == UserChatRole.owner.name) {
            userRules = UserChatRule.values.map((final e) => e.name).toList();
          } else {
            userRules =
                data?.role?.rules.map((final e) => e?.actions).toList() ?? [];
            if (data?.role?.name == UserChatRole.admin.name) {
              userRules.addAll(
                defaultMemberRules.map((final e) => e.name).toList(),
              );
            }
          }
        }

        if (state.status == GroupChatDetailStatus.updateAdminRuleSuccess ||
            state.status == GroupChatDetailStatus.changeOwnerSuccess) {
          if (state.status == GroupChatDetailStatus.changeOwnerSuccess) {
            widget.conversation?.conversationDetails?.role =
                UserChatRole.admin.name;
          }
          unawaited(
            Alert.showAlert(
              AlertParams(
                context,
                context.l10n.updateSuccess,
                onPressOK: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
          );
        }
      },
      builder: (final context, final state) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (final didPop, final _) {
            if (didPop) {
              return;
            }
            Navigator.of(context).pop(groupAvatar);
          },
          child: Scaffold(
            backgroundColor: const Color(0xffF3F3F3),
            appBar: isSearchMode
                ? AppBar(
                    backgroundColor: const Color(0xffF3F3F3),
                    automaticallyImplyLeading: false,
                    title: ChatSearchBar(
                      center: false,
                      controller: searchController,
                      autofocus: true,
                      onChanged: (final keySearch) {
                        timer?.cancel();
                        timer = Timer(
                          const Duration(milliseconds: 800),
                          () async {
                            _filePagingController.refresh();
                            _mediaPagingController.refresh();
                            _linkPagingController.refresh();
                            _memberPagingController.refresh();
                          },
                        );
                      },
                    ),
                    actions: [
                      IconButton(
                        onPressed: () {
                          isSearchMode = false;
                          searchController.clear();
                          setState(() {});
                          _filePagingController.refresh();
                          _mediaPagingController.refresh();
                          _linkPagingController.refresh();
                          _memberPagingController.refresh();
                        },
                        icon: Text(
                          context.l10n.cancel,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                fontSize: 15,
                                color: Theme.of(context).primaryColor,
                              ),
                        ),
                      ),
                    ],
                  )
                : AppBar(
                    backgroundColor: const Color(0xffF3F3F3),
                    leading: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        if (editMode) {
                          editMode = false;
                          setState(() {});
                          return;
                        }
                        Navigator.of(context).pop(groupAvatar);
                      },
                      child: editMode
                          ? SizedBox(
                              width: 24,
                              height: 24,
                              child: Center(
                                child: Text(
                                  context.l10n.cancel,
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(
                                        fontSize: 15,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                ),
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(right: 28),
                              child: Icon(
                                Icons.arrow_back_ios_new_rounded,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                    ),
                    actions: [
                      ValueListenableBuilder(
                        valueListenable: _isVisibleSearchMedia,
                        builder: (final _, final value, final __) {
                          if (value) {
                            return IconButton(
                              onPressed: () {
                                isSearchMode = true;
                                setState(() {});
                              },
                              icon: EZResources.image(
                                ImageParams(
                                  name: AppIcons.icSearch3,
                                  size: const ImageSize.square(24),
                                ),
                              ),
                            );
                          }
                          if (widget.conversation?.isGroup ?? false) {
                            return IconButton(
                              onPressed: () {
                                if (editMode) {
                                  if (groupNameController.text !=
                                      widget.conversation?.name) {
                                    context.read<GroupChatDetailBloc>().add(
                                      GroupChatDetailUpdated(
                                        GroupChatDetailUpdateRequestParams(
                                          id: widget.conversation?.id,
                                          name: groupNameController.text,
                                          action:
                                              GroupChatAction.updateName.name,
                                        ),
                                      ),
                                    );
                                  } else {
                                    editMode = false;
                                    setState(() {});
                                  }
                                } else {
                                  editMode = true;
                                  setState(() {});
                                }
                              },
                              icon: Text(
                                editMode
                                    ? context.l10n.done
                                    : context.l10n.edit,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      fontSize: 15,
                                      fontWeight: editMode
                                          ? FontWeight.w700
                                          : FontWeight.w400,
                                      color: Theme.of(context).primaryColor,
                                    ),
                              ),
                            );
                          }
                          return Container();
                        },
                      ),
                    ],
                  ),
            body: SafeArea(
              child: editMode
                  ? _buildGroupInfoView(context)
                  : DefaultTabController(
                      length: (widget.conversation?.isGroup ?? false) ? 4 : 3,
                      child: NestedScrollView(
                        controller: scrollController,
                        headerSliverBuilder: (final context, final value) {
                          return [
                            _buildGroupInfo(context),
                            _buildTabBar(context),
                          ];
                        },
                        body: LayoutBuilder(
                          builder: (final _, final constraints) {
                            if (constraints.maxHeight >
                                MediaQuery.sizeOf(context).height * 0.65) {
                              Future.delayed(
                                const Duration(milliseconds: 300),
                                () {
                                  _isVisibleSearchMedia.value = true;
                                },
                              );
                            } else {
                              Future.delayed(
                                const Duration(milliseconds: 300),
                                () {
                                  _isVisibleSearchMedia.value = false;
                                },
                              );
                            }

                            return _buildTabBarView();
                          },
                        ),
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }

  SliverAppBar _buildGroupInfo(final BuildContext context) {
    return SliverAppBar(
      expandedHeight:
          235 +
          (userRules.contains(UserChatRule.addMember.name) ? 55 : 0) +
          ((!(widget.conversation?.isGroup ?? false) && phone.isNotEmpty)
              ? 50
              : 0),
      automaticallyImplyLeading: false,
      surfaceTintColor: Theme.of(context).scaffoldBackgroundColor,
      backgroundColor: const Color(0xffF3F3F3),
      flexibleSpace: LayoutBuilder(
        builder: (final context, final constraints) {
          return FlexibleSpaceBar(
            titlePadding: EdgeInsets.zero,
            background: _buildGroupInfoView(context),
          );
        },
      ),
    );
  }

  Padding _buildGroupInfoView(final BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        child: Column(
          children: [
            if (Utils.isURL(groupAvatar ?? ''))
              _buildAvatarWithUrl(context)
            else
              _buildEmptyAvatar(context),
            if (editMode) ...[
              const SizedBox(height: 8),
              SizedBox(
                height: 48,
                child: TextField(
                  controller: groupNameController,
                  readOnly:
                      !(userRules.contains(UserChatRule.updateName.name) ||
                          widget.conversation?.conversationDetails?.role ==
                              UserChatRole.owner.name),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    fillColor: Theme.of(context).colorScheme.surface,
                    filled: true,
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 34),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  children: <Widget>[
                    _buildMenuButton(
                      context,
                      label: context.l10n.groupType,
                      subLabel: context.l10n.private,
                      iconPath: AppIcons.icGroupType,
                      onTap: () {},
                    ),
                    const Row(
                      children: <Widget>[
                        SizedBox(width: 56),
                        Expanded(child: Divider(height: 18)),
                      ],
                    ),
                    _buildMenuButton(
                      context,
                      label: context.l10n.chatHistory,
                      subLabel: context.l10n.show,
                      iconPath: AppIcons.icChatHistory,
                      onTap: () {},
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  children: <Widget>[
                    _buildMenuButton(
                      context,
                      label: context.l10n.inviteLink,
                      subLabel: '1',
                      iconPath: AppIcons.icInviteLink,
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          useSafeArea: true,
                          backgroundColor: const Color(0xffF3F3F3),
                          barrierColor: const Color(0xffF3F3F3),
                          builder: (final _) {
                            return InviteLinkView(
                              inviteLink: widget.conversation?.inviteLink,
                            );
                          },
                        );
                      },
                    ),
                    const Row(
                      children: <Widget>[
                        SizedBox(width: 56),
                        Expanded(child: Divider(height: 18)),
                      ],
                    ),
                    _buildMenuButton(
                      context,
                      label: context.l10n.react,
                      subLabel: context.l10n.all,
                      iconPath: AppIcons.icReaction,
                      onTap: () {},
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  children: <Widget>[
                    if (widget.conversation?.conversationDetails?.role ==
                            UserChatRole.owner.name ||
                        widget.conversation?.conversationDetails?.role ==
                            UserChatRole.admin.name) ...[
                      _buildMenuButton(
                        context,
                        label: context.l10n.permissions,
                        iconPath: AppIcons.icKey,
                        onTap: () {
                          _showSetPermissionMemberView(context);
                        },
                      ),
                      const Row(
                        children: <Widget>[
                          SizedBox(width: 56),
                          Expanded(child: Divider(height: 18)),
                        ],
                      ),
                    ],
                    if (userRules.contains(UserChatRule.addAdmin.name)) ...[
                      _buildMenuButton(
                        context,
                        label: context.l10n.admin,
                        iconPath: AppIcons.icChatAdmin,
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            useSafeArea: true,
                            backgroundColor: const Color(0xffF3F3F3),
                            barrierColor: const Color(0xffF3F3F3),
                            builder: (final _) {
                              return BlocProvider.value(
                                value: context.read<GroupChatDetailBloc>(),
                                child: AdminsView(
                                  conversationId: widget.conversation?.id,
                                  role: adminRole,
                                  onTapAddException: () => _setPermission(
                                    context,
                                    UserChatRole.admin,
                                  ),
                                  memberRulesOfGroup:
                                      widget.conversation?.memberRules ?? [],
                                  canTransferOwnership:
                                      widget
                                          .conversation
                                          ?.conversationDetails
                                          ?.role ==
                                      UserChatRole.owner.name,
                                ),
                              );
                            },
                          );
                        },
                      ),
                      const Row(
                        children: <Widget>[
                          SizedBox(width: 56),
                          Expanded(child: Divider(height: 18)),
                        ],
                      ),
                    ],
                    _buildMenuButton(
                      context,
                      label: context.l10n.member,
                      subLabel: widget.conversation?.membersInfo.length
                          .toString(),
                      iconPath: AppIcons.icGroupMember,
                      onTap: () async => _onTapMemberField(context),
                    ),
                    // if (widget.conversation?.conversationDetails?.role ==
                    //         UserChatRole.owner.name ||
                    //     widget.conversation?.conversationDetails?.role ==
                    //         UserChatRole.admin.name)
                    //   _buildMenuButton(
                    //     context,
                    //     label: context.l10n.removeMember,
                    //     iconPath: AppIcons.icDeleteMember,
                    //     onTap: () {},
                    //   ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => _onTapDeleteGroup(context),
                child: Container(
                  height: 42,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      widget.conversation?.conversationDetails?.role ==
                              UserChatRole.owner.name
                          ? context.l10n.deleteGroup
                          : context.l10n.leaveGroup,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 15,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ] else ...[
              Text(
                groupNameController.text,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w400),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (widget.conversation?.isGroup ?? false)
                Text(
                  '${widget.conversation?.membersInfo.length}'
                  '${Strings.space}${context.l10n.member.toLowerCase()}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 15,
                    color: Theme.of(context).hintColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              const SizedBox(height: 12),
              SizedBox(
                height: 58,
                child: Row(
                  spacing: 7,
                  children: [
                    if (!(widget.conversation?.isGroup ?? false))
                      _buildActionButton(
                        icon: EZResources.image(
                          ImageParams(
                            name: AppIcons.icPhone,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        label: context.l10n.call,
                        onTap: () async {
                          final canCall =
                              (await EZCache.shared.configurations)?.canCall ??
                              true;
                          if (!canCall) {
                            if (context.mounted) {
                              Alert.showAlert(
                                AlertParams(
                                  context,
                                  context.l10n.callIsBlocked,
                                ),
                              );
                            }
                            return;
                          }
                          if (context.mounted) {
                            LoadingDialog.instance
                                .show(context)
                                .then(
                                  (_) => Future.delayed(
                                    const Duration(seconds: 2),
                                    () {
                                      if (LoadingDialog.isShowing) {
                                        final context = getIt<AppRouter>()
                                            .navigatorKey
                                            .currentContext;
                                        if (context != null) {
                                          // need to use currentContext
                                          // ignore: use_build_context_synchronously
                                          LoadingDialog.instance.hide(context);
                                        }
                                      }
                                    },
                                  ),
                                );
                          }

                          final opposite = widget.conversation?.membersInfo
                              .firstWhereOrNull(
                                (final e) =>
                                    e?.username !=
                                    EZCache.shared.getUserProfile()?.employeeId,
                              );
                          PlatformChannelHelper.onMakeCallNotifier(
                            to: opposite?.username,
                            toName: widget.conversation?.name,
                            avatarUrl: opposite?.avatar,
                          );
                        },
                      ),
                    _buildActionButton(
                      icon: EZResources.image(
                        ImageParams(
                          name:
                              (widget
                                      .conversation
                                      ?.conversationDetails
                                      ?.isMute ??
                                  false)
                              ? AppIcons.icBellProfileMute
                              : AppIcons.icBellProfile,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      label:
                          (widget.conversation?.conversationDetails?.isMute ??
                              false)
                          ? context.l10n.turnOff
                          : context.l10n.turnOn,
                      onTap: () {
                        widget.conversation?.conversationDetails?.isMute =
                            !(widget
                                    .conversation
                                    ?.conversationDetails
                                    ?.isMute ??
                                false);
                        context.read<GroupChatDetailBloc>().add(
                          GroupChatDetailStarted(
                            GroupChatDetailRequestParams(
                              isMute: widget
                                  .conversation
                                  ?.conversationDetails
                                  ?.isMute,
                              conversationId: widget.conversation?.id,
                              conversationType: ConversationType.internal.name,
                            ),
                          ),
                        );
                      },
                    ),
                    _buildActionButton(
                      icon: EZResources.image(
                        ImageParams(
                          name: AppIcons.icSearch3,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      label: context.l10n.search,
                      onTap: () =>
                          Navigator.of(context).pop(GroupChatAction.search),
                    ),
                    if (widget.conversation?.isGroup ?? false)
                      _buildActionButton(
                        color:
                            widget.conversation?.conversationDetails?.role ==
                                UserChatRole.owner.name
                            ? Theme.of(context).colorScheme.error
                            : Theme.of(context).primaryColor,
                        icon: EZResources.image(
                          ImageParams(
                            name:
                                widget
                                        .conversation
                                        ?.conversationDetails
                                        ?.role ==
                                    UserChatRole.owner.name
                                ? AppIcons.icTrash_2
                                : AppIcons.icLogOut,
                            color:
                                widget
                                        .conversation
                                        ?.conversationDetails
                                        ?.role ==
                                    UserChatRole.owner.name
                                ? Theme.of(context).colorScheme.error
                                : Theme.of(context).primaryColor,
                          ),
                        ),
                        label:
                            widget.conversation?.conversationDetails?.role ==
                                UserChatRole.owner.name
                            ? context.l10n.deleteGroup
                            : context.l10n.leaveGroup,
                        onTap: () async {
                          _onTapDeleteGroup(context);
                        },
                      )
                    else
                      _buildActionButton(
                        icon: EZResources.image(
                          ImageParams(
                            name: AppIcons.icInfoProfile,
                            size: const ImageSize.square(24),
                          ),
                        ),
                        label: context.l10n.information,
                        onTap: () async => _pushToProfilePage(context),
                      ),
                  ],
                ),
              ),
              if (!(widget.conversation?.isGroup ?? false)) ...[
                const SizedBox(height: 12),
                _buildUserInfo(context),
              ] else ...[
                const SizedBox(height: 12),
                if (userRules.contains(UserChatRule.addMember.name))
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async => _onTapAddMember(context),
                    child: Container(
                      decoration: ShapeDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          EZResources.image(
                            ImageParams(
                              name: AppIcons.icAddContact,
                              color: Theme.of(context).primaryColor,
                              size: const ImageSize.square(24),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            context.l10n.addMember,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 15,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  void _showSetPermissionMemberView(final BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: const Color(0xffF3F3F3),
      barrierColor: const Color(0xffF3F3F3),
      builder: (final _) {
        return BlocProvider.value(
          value: context.read<GroupChatDetailBloc>(),
          child: MemberPermissionView(
            conversationId: widget.conversation?.id,
            role: memberRole,
            currentMemberRules: widget.conversation?.memberRules ?? [],
            onTapAddException: () =>
                _setPermission(context, UserChatRole.member),
          ),
        );
      },
    ).whenComplete(() {
      if (context.mounted) {
        context.read<GroupChatDetailBloc>().add(
          GroupChatDetailGotRuleByRole(
            GroupChatDetailGetRuleByRoleRequestParams(UserChatRole.member.name),
          ),
        );
      }
    });
  }

  void _onTapDeleteGroup(final BuildContext context) {
    Alert.showAlertConfirm(
      AlertConfirmParams(
        context,
        message:
            widget.conversation?.conversationDetails?.role ==
                UserChatRole.owner.name
            ? context.l10n.warningDeleteGroup
            : context.l10n.warningLeaveGroup,
        onPressed: () {
          if (widget.conversation?.conversationDetails?.role ==
              UserChatRole.owner.name) {
            context.read<GroupChatDetailBloc>().add(
              GroupChatDeleted(
                ChatGetByIdRequestParams(id: widget.conversation?.id),
              ),
            );
          } else {
            context.read<GroupChatDetailBloc>().add(
              GroupChatDetailUpdated(
                GroupChatDetailUpdateRequestParams(
                  action: GroupChatAction.leaveGroup.name,
                  id: widget.conversation?.id,
                ),
              ),
            );
          }
          Navigator.of(context).pop();
        },
        confirmText: context.l10n.accept,
        cancelButton: context.l10n.cancel,
      ),
    );
  }

  Widget _buildMenuButton(
    final BuildContext context, {
    required final String label,
    final String? subLabel,
    required final void Function() onTap,
    required final String iconPath,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: <Widget>[
            EZResources.image(
              ImageParams(name: iconPath, size: const ImageSize.square(24)),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                label,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontSize: 15),
              ),
            ),
            if (subLabel != null)
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Text(
                  subLabel,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 15,
                    color: Theme.of(context).hintColor,
                  ),
                ),
              ),
            EZResources.image(
              ImageParams(
                name: AppIcons.icForward,
                size: const ImageSize.square(18),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pushToProfilePage(final BuildContext context) async {
    final userName = widget.conversation?.membersInfo
        .firstWhereOrNull(
          (final e) =>
              e?.username != EZCache.shared.getUserProfile()?.employeeId,
        )
        ?.username;
    context.router.push(
      StoryPersonRoute(codeUser: Utils.defaultOnEmpty(userName)),
    );
  }

  Widget _buildTabBarView() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBarView(
        controller: _tabController,
        children: [
          if (widget.conversation?.isGroup ?? false)
            _buildMemberListTabBarView(),
          _buildFileListTabBarView(),
          _buildMediaListTabBarView(),
          _buildLinkListTabBarView(),
        ],
      ),
    );
  }

  Widget _buildMemberListTabBarView() {
    return ColoredBox(
      color: Theme.of(context).colorScheme.surface,
      child: BaseInfiniteListView(
        pagingController: _memberPagingController,
        itemBuilder: (final _, final item, final index) {
          return MemberTile(
            item,
            role: widget.conversation?.conversationDetails?.role,
            userRules: userRules,
            onTap: (final userName) async {
              context.router.push(
                StoryPersonRoute(codeUser: Utils.defaultOnEmpty(userName)),
              );
            },
            onTapRemove: () async {
              Alert.showAlertConfirm(
                AlertConfirmParams(
                  context,
                  message: context.l10n.warningRemoveMember,
                  onPressed: () {
                    context.read<GroupChatDetailBloc>().add(
                      GroupChatDetailUpdated(
                        GroupChatDetailUpdateRequestParams(
                          id: widget.conversation?.id,
                          action: GroupChatAction.removeMember.name,
                          members: [item?.username ?? ''],
                        ),
                      ),
                    );
                  },
                  confirmText: context.l10n.delete,
                  cancelButton: context.l10n.cancel,
                ),
              );
            },
            addAdmin: (final member) async => showModalBottomSheet(
              context: context,
              useSafeArea: true,
              isScrollControlled: true,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(top: Radius.circular(14)),
              ),
              builder: (final _) => Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).primaryColorDark.withValues(alpha: 0),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(14),
                    topRight: Radius.circular(14),
                  ),
                ),
                height: MediaQuery.of(context).size.height * 0.9,
                child: BlocProvider.value(
                  value: context.read<GroupChatDetailBloc>(),
                  child: AddAdminBottomSheet(
                    member: member,
                    conversationId: widget.conversation?.id,
                    role: adminRole,
                    memberRulesOfGroup: widget.conversation?.memberRules ?? [],
                    canTransferOwnership:
                        widget.conversation?.conversationDetails?.role ==
                        UserChatRole.owner.name,
                  ),
                ),
              ),
            ).whenComplete(() => _memberPagingController.refresh()),
            onShowMenuPopup: ({required final bool isShowing}) {
              if (isShowing) {
                blurValue = 8;
              } else {
                blurValue = 0;
              }
            },
          );
        },
        separator: const Divider(height: 24),
      ),
    );
  }

  Container _buildLinkListTabBarView() {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: BaseInfiniteListView(
        itemBuilder: (final context, final item, final index) {
          final links = item?.links.map((final e) => e).toList() ?? [];
          return InkWell(
            onTap: () => Navigator.of(context).pop(
              ChatItems(
                conversationId: item?.conversationId,
                createdAt: item?.createdAt,
              ),
            ),
            child: LinkPreview(
              padding: EdgeInsets.zero,
              enableAnimation: true,
              textStyle: Theme.of(context).textTheme.bodyLarge,
              onPreviewDataFetched: (final data) {
                if (previewDataList[item?.links.firstOrNull] == null) {
                  previewDataList[item?.links.firstOrNull ?? ''] = data;
                  setState(() {});
                }
              },
              previewData: previewDataList[item?.links.firstOrNull ?? ''],
              text: links.map((final e) => '${e ?? ''}\n').join(),
              textWidget: _buildLinkPreviewNoData(context, item, links),
              previewBuilder: (final _, final data) =>
                  _buildLinkPreview(context, data, item, links),
              width: MediaQuery.sizeOf(context).width,
            ),
          );
        },
        pagingController: _linkPagingController,
      ),
    );
  }

  Container _buildLinkPreviewNoData(
    final BuildContext context,
    final GroupChatDetailLinkLoadItems? item,
    final List<String?> links,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: <Widget>[
          Container(
            width: 44,
            height: 44,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              gradient: LinearGradient(
                colors: [
                  Utils.randomColor(
                    item?.links.firstOrNull ?? '',
                  ).withValues(alpha: 0.5),
                  Utils.randomColor(item?.links.firstOrNull ?? ''),
                ],
              ),
            ),
            child: Center(
              child: Text(
                Utils.defaultOnEmpty(
                  Uri.parse(item?.links.firstOrNull ?? '').host.isNotEmpty
                      ? Uri.parse(item?.links.firstOrNull ?? '').host
                      : item?.links.firstOrNull,
                ).firstOrEmpty().toUpperCase(),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.surface,
                ),
              ),
            ),
          ),
          Expanded(
            child: ListTile(
              title: Text(
                Uri.parse(item?.links.firstOrNull ?? '').host.isNotEmpty
                    ? Uri.parse(item?.links.firstOrNull ?? '').host
                    : (item?.links.firstOrNull ?? ''),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: links
                    .map(
                      (final link) => InkWell(
                        onTap: () async => Utils.onTapDynamicLink(
                          link: link,
                          linkType: LinkType.url,
                        ),
                        child: Text(
                          link ?? '',
                          maxLines: item?.links.length,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(color: AppColors.link),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Container _buildLinkPreview(
    final BuildContext context,
    final PreviewData data,
    final GroupChatDetailLinkLoadItems? item,
    final List<String?> links,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: <Widget>[
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: EzCachedNetworkImage(
              imageUrl: data.image?.url,
              width: 44,
              height: 44,
            ),
          ),
          Expanded(
            child: ListTile(
              title: Text(
                data.title ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: links
                    .map(
                      (final link) => InkWell(
                        onTap: () async => Utils.onTapDynamicLink(
                          link: link,
                          linkType: LinkType.url,
                        ),
                        child: Text(
                          link ?? '',
                          maxLines: item?.links.length,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(color: AppColors.link),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Container _buildMediaListTabBarView() {
    if (searchController.text.isNotEmpty) {
      return Container(
        color: Theme.of(context).colorScheme.surface,
        child: BaseInfiniteListView<GroupChatDetailMediaLoadItems?>(
          pagingController: _mediaPagingController,
          itemBuilder: (final context, final item, final index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onLongPressStart: (final details) => _showMenuAction(
                context: context,
                details: details,
                conversationId: item?.conversationId,
                createdAt: item?.messageInfo?.createdAt,
                link: item?.link,
                fileName: item?.originalname,
              ),
              onTap: () {
                if (item?.mimetype?.startsWith(FileExtensionType.video.name) ??
                    false) {
                  previewPage(item);
                } else {
                  showGeneralDialog(
                    context: context,
                    transitionBuilder:
                        (final _, final animation, final __, final child) {
                          return FadeTransition(
                            opacity: animation,
                            child: ScaleTransition(
                              scale: CurvedAnimation(
                                parent: animation,
                                curve: Curves.easeOutBack,
                              ),
                              child: child,
                            ),
                          );
                        },
                    pageBuilder: (_, final __, final ___) {
                      return ImageFullView(
                        imageUrls: [
                          ImageProperty(
                            url: AppConfig.useGlobalDomain
                                ? Utils.replaceGlobalDomain(item?.link)
                                : item?.link,
                          ),
                        ],
                        actionIcon: (final currentImage) => Row(
                          children: [
                            _buildDownloadButton(context, currentImage, item),
                            const SizedBox(width: 4),
                            _buildEditImageButton(context, item),
                          ],
                        ),
                      );
                    },
                  );
                }
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: ShapeDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  children: <Widget>[
                    Stack(
                      children: [
                        EzCachedNetworkImage(
                          imageUrl: item?.thumbnail ?? '',
                          width: 44,
                          height: 44,
                        ),
                        if (item?.mimetype?.startsWith(
                              FileExtensionType.video.name,
                            ) ??
                            false)
                          Positioned.fill(
                            child: Center(
                              child: Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: Colors.grey.withValues(alpha: 0.8),
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.play_arrow,
                                    size: 24,
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.surface,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            item?.originalname ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                          Text.rich(
                            TextSpan(
                              text: Utils.getDisplaySizeFile(item?.size),
                              children: [
                                const TextSpan(text: '  •  '),
                                TextSpan(
                                  text: item?.createdAt != null
                                      ? DateTime.parse(
                                          item!.createdAt!,
                                        ).toLocal().formatByDateFormat(
                                          IntlHelper.dateFormatter5,
                                        )
                                      : '',
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    }

    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: PagedGridView(
        padding: const EdgeInsets.all(12),
        builderDelegate:
            PagedChildBuilderDelegate<GroupChatDetailMediaLoadItems?>(
              itemBuilder: (final context, final item, final index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onLongPressStart: (final details) => _showMenuAction(
                    context: context,
                    details: details,
                    conversationId: item?.conversationId,
                    createdAt: item?.messageInfo?.createdAt,
                    link: item?.link,
                    fileName: item?.originalname,
                  ),
                  child:
                      (item?.mimetype?.startsWith(
                            FileExtensionType.video.name,
                          ) ??
                          false)
                      ? AppVideoPlayer(
                          source: item?.link ?? '',
                          thumbnail: item?.thumbnail ?? '',
                          fileName: item?.originalname ?? '',
                        )
                      : EzCachedNetworkImageWithHeroAnimation(
                          tag: item?.id ?? DateTime.now().toString(),
                          imageUrl: item?.link ?? '',
                          memCacheHeight: Utils.resizeImageToHD(
                            height: item?.height ?? 0,
                            width: item?.width ?? 0,
                            targetHDHeight: 512,
                            targetHDWidth: 512,
                          )?.height.toInt(),
                          memCacheWidth: Utils.resizeImageToHD(
                            height: item?.height ?? 0,
                            width: item?.width ?? 0,
                            targetHDHeight: 512,
                            targetHDWidth: 512,
                          )?.width.toInt(),
                          imageUrlList:
                              _mediaPagingController.itemList
                                  ?.where(
                                    (final e) =>
                                        e?.mimetype?.contains('image') ?? false,
                                  )
                                  .map(
                                    (final e) => ImageProperty(
                                      url: e?.link,
                                      fileNameWithExtension: e?.originalname,
                                    ),
                                  )
                                  .toList() ??
                              [],
                          width: MediaQuery.sizeOf(context).width / 3,
                          height: MediaQuery.sizeOf(context).width / 3,
                          actionIcon: (final currentImage) => Row(
                            children: [
                              _buildDownloadButton(context, currentImage, item),
                              const SizedBox(width: 4),
                              _buildEditImageButton(context, item),
                            ],
                          ),
                        ),
                );
              },
              firstPageProgressIndicatorBuilder: (final context) => Center(
                child: SpinKitCircle(color: Theme.of(context).primaryColor),
              ),
              newPageProgressIndicatorBuilder: (final context) => Center(
                child: SpinKitCircle(color: Theme.of(context).primaryColor),
              ),
              noItemsFoundIndicatorBuilder: (final context) =>
                  Center(child: Text(context.l10n.noData)),
            ),
        pagingController: _mediaPagingController,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 2,
          crossAxisSpacing: 2,
        ),
      ),
    );
  }

  Container _buildEditImageButton(
    final BuildContext context,
    final GroupChatDetailMediaLoadItems? item,
  ) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).primaryColorDark.withValues(alpha: 0.3),
      ),
      child: IconButton(
        onPressed: () async {
          showGeneralDialog(
            context: context,
            pageBuilder: (final _, final __, final ___) {
              return Container(
                color: Colors.black,
                child: ProImageEditor.network(
                  item?.link ?? '',
                  callbacks: ProImageEditorCallbacks(
                    onImageEditingComplete: (final bytes) async {
                      EzToast.showShortToast(message: context.l10n.sentMessage);
                      context.read<GeneralBloc>().add(
                        GeneralSendImageMessage(bytes),
                      );
                      Navigator.of(context)
                        ..pop()
                        ..pop();
                    },
                  ),
                ),
              );
            },
          );
        },
        icon: const Icon(Icons.brush_sharp, color: Colors.white),
      ),
    );
  }

  Future<void> _showMenuAction({
    required final BuildContext context,
    required final LongPressStartDetails details,
    required final String? conversationId,
    required final String? createdAt,
    required final String? link,
    required final String? fileName,
  }) {
    return showMenu(
      context: context,
      color: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.transparent,
      popUpAnimationStyle: AnimationStyle.noAnimation,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx, // X-axis where tapped
        details.globalPosition.dy, // Y-axis where tapped
        MediaQuery.sizeOf(context).width -
            details.globalPosition.dx, // Right boundary
        MediaQuery.sizeOf(context).height -
            details.globalPosition.dy, // Bottom boundary
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      constraints: BoxConstraints(
        maxWidth: min(MediaQuery.sizeOf(context).width * 0.8, 335),
      ),
      items: <PopupMenuEntry<void>>[
        PopupMenuItem(
          padding: EdgeInsets.zero,
          onTap: () => Navigator.of(context).pop(
            ChatItems(conversationId: conversationId, createdAt: createdAt),
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: Theme.of(context).dividerColor.withValues(alpha: 0.5),
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: _buildMenuItem(
              label: context.l10n.showInChat,
              iconPath: AppIcons.icShowInChat,
            ),
          ),
        ),
        const PopupMenuDivider(height: 0),
        PopupMenuItem(
          padding: EdgeInsets.zero,
          onTap: () => ServiceDownloader.instance.onTapDownload(
            context,
            link ?? '',
            TaskInfo(name: fileName),
            fileName,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: Theme.of(context).dividerColor.withValues(alpha: 0.5),
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: _buildMenuItem(
              label: context.l10n.download,
              iconPath: AppIcons.icDownload,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required final String label,
    required final String iconPath,
  }) {
    return Row(
      children: [
        Expanded(child: Text(label)),
        const SizedBox(width: 6),
        EZResources.image(
          ImageParams(
            name: iconPath,
            size: const ImageSize.square(24),
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
      ],
    );
  }

  Container _buildDownloadButton(
    final BuildContext context,
    final ImageProperty? currentImage,
    final GroupChatDetailMediaLoadItems? item,
  ) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).primaryColorDark.withValues(alpha: 0.3),
      ),
      child: IconButton(
        onPressed: () async {
          ServiceDownloader.instance.onTapDownload(
            context,
            currentImage?.url ?? item?.link ?? '',
            TaskInfo(
              name: currentImage?.fileNameWithExtension ?? item?.originalname,
            ),
            currentImage?.fileNameWithExtension ?? item?.originalname,
          );
        },
        icon: const Icon(Icons.download, color: Colors.white),
      ),
    );
  }

  Container _buildFileListTabBarView() {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: BaseInfiniteListView<GroupChatDetailFileLoadItems?>(
        pagingController: _filePagingController,
        itemBuilder: (final context, final item, final index) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onLongPressStart: (final details) => _showMenuAction(
              context: context,
              details: details,
              conversationId: item?.conversationId,
              createdAt: item?.messageInfo?.createdAt,
              link: item?.link,
              fileName: item?.originalname,
            ),
            onTap: () async => ServiceDownloader.instance.onTapDownload(
              context,
              item?.link ?? '',
              TaskInfo(),
              item?.originalname,
            ),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: ShapeDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                children: <Widget>[
                  Column(
                    children: [
                      Icon(
                        Icons.insert_drive_file,
                        color: Theme.of(context).hintColor,
                      ),
                      Text(
                        extension(item?.originalname ?? ''),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          item?.originalname ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        Text.rich(
                          TextSpan(
                            text: Utils.getDisplaySizeFile(item?.size),
                            children: [
                              const TextSpan(text: '  •  '),
                              TextSpan(
                                text: item?.createdAt != null
                                    ? DateTime.parse(
                                        item!.createdAt!,
                                      ).toLocal().formatByDateFormat(
                                        IntlHelper.dateFormatter5,
                                      )
                                    : '',
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> previewPage(final GroupChatDetailMediaLoadItems? item) async {
    SystemChrome.setPreferredOrientations(<DeviceOrientation>[
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (final BuildContext context, final _, final __) =>
            Scaffold(
              appBar: AppBar(
                backgroundColor: Theme.of(context).colorScheme.onSurface,
                foregroundColor: Theme.of(context).colorScheme.surface,
                title: Text(context.l10n.video),
                centerTitle: true,
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.download,
                      color: Theme.of(context).colorScheme.surface,
                    ),
                    onPressed: () async {
                      if (Utils.isURL(item?.link ?? '')) {
                        await ServiceDownloader.instance.onTapDownload(
                          context,
                          item?.link ?? '',
                          TaskInfo(name: item?.originalname),
                          item?.originalname,
                        );
                      }
                    },
                  ),
                ],
              ),
              backgroundColor: Colors.black,
              body: Center(
                child: CachedVideoPlayer(
                  source: item?.link,
                  fileExtension: item?.originalname?.split('.').lastOrNull,
                ),
              ),
            ),
        opaque: false,
      ),
    ).then((final _) {
      SystemChrome.setPreferredOrientations(<DeviceOrientation>[
        DeviceOrientation.portraitUp,
      ]);
    });
  }

  SliverPersistentHeader _buildTabBar(final BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: SliverAppBarDelegate(
        minHeight: 45,
        maxHeight: 45,
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).appBarTheme.backgroundColor,
          ),
          child: TabBar(
            controller: _tabController,
            indicatorSize: TabBarIndicatorSize.tab,
            tabAlignment: (widget.conversation?.isGroup ?? false)
                ? TabAlignment.start
                : null,
            isScrollable: widget.conversation?.isGroup ?? false,
            labelStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
            unselectedLabelStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
            unselectedLabelColor: Theme.of(context).hintColor,
            labelColor: Theme.of(context).primaryColor,
            indicatorColor: Theme.of(context).primaryColor,
            tabs: [
              if (widget.conversation?.isGroup ?? false)
                _buildTabBarItem(context.l10n.member),
              _buildTabBarItem(context.l10n.document),
              _buildTabBarItem(context.l10n.picture),
              _buildTabBarItem(context.l10n.link),
            ],
          ),
        ),
      ),
    );
  }

  Tab _buildTabBarItem(final String text) {
    return Tab(
      child: Text(
        text,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        textScaler: TextScaler.noScaling,
      ),
    );
  }

  Widget _buildMemberList(
    final BuildContext context,
    final void Function(void Function()) modalState, {
    final bool isShowTitle = true,
    final UserChatRole? role,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            if (isShowTitle)
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  context.l10n.member,
                  style: const TextStyle(fontWeight: AppFontWeight.medium),
                ),
              ),
            const SizedBox(height: 4),
            Expanded(
              child: Container(
                decoration: ShapeDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: BaseInfiniteListView(
                  pagingController: _memberPagingController,
                  itemBuilder: (final _, final item, final index) {
                    return MemberTile(
                      item,
                      role: widget.conversation?.conversationDetails?.role,
                      userRules: userRules,
                      onTap: (final userName) async {
                        if (role != null) {
                          if (userName ==
                                  EZCache.shared.getUserProfile()?.employeeId ||
                              item?.role == UserChatRole.owner.name ||
                              (item?.role == UserChatRole.admin.name &&
                                  widget
                                          .conversation
                                          ?.conversationDetails
                                          ?.role !=
                                      UserChatRole.owner.name)) {
                            return;
                          }
                          if (role == UserChatRole.member) {
                            showModalBottomSheet(
                              context: context,
                              useSafeArea: true,
                              isScrollControlled: true,
                              backgroundColor: Theme.of(
                                context,
                              ).scaffoldBackgroundColor,
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(14),
                                ),
                              ),
                              builder: (final _) => Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).primaryColorDark.withValues(alpha: 0),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(14),
                                    topRight: Radius.circular(14),
                                  ),
                                ),
                                height:
                                    MediaQuery.of(context).size.height * 0.9,
                                child: BlocProvider.value(
                                  value: context.read<GroupChatDetailBloc>(),
                                  child: AddExceptionView(
                                    member: _memberPagingController.itemList
                                        ?.firstWhereOrNull(
                                          (final element) =>
                                              element?.username == userName,
                                        ),
                                    conversationId: widget.conversation?.id,
                                    role: memberRole,
                                  ),
                                ),
                              ),
                            ).then((final value) {
                              if (context.mounted && value == null) {
                                Navigator.of(context).pop();
                              }
                            });
                          }
                          if (role == UserChatRole.admin) {
                            showModalBottomSheet(
                              context: context,
                              useSafeArea: true,
                              isScrollControlled: true,
                              backgroundColor: Theme.of(
                                context,
                              ).scaffoldBackgroundColor,
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(14),
                                ),
                              ),
                              builder: (final _) => Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).primaryColorDark.withValues(alpha: 0),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(14),
                                    topRight: Radius.circular(14),
                                  ),
                                ),
                                height:
                                    MediaQuery.of(context).size.height * 0.9,
                                child: BlocProvider.value(
                                  value: context.read<GroupChatDetailBloc>(),
                                  child: AddAdminBottomSheet(
                                    member: _memberPagingController.itemList
                                        ?.firstWhereOrNull(
                                          (final element) =>
                                              element?.username == userName,
                                        ),
                                    conversationId: widget.conversation?.id,
                                    role: adminRole,
                                    memberRulesOfGroup:
                                        widget.conversation?.memberRules ?? [],
                                    canTransferOwnership:
                                        widget
                                            .conversation
                                            ?.conversationDetails
                                            ?.role ==
                                        UserChatRole.owner.name,
                                  ),
                                ),
                              ),
                            ).then((final value) {
                              _memberPagingController.refresh();
                              if (context.mounted && value == null) {
                                Navigator.of(context).pop();
                              }
                            });
                          }
                        } else {
                          context.router.push(
                            StoryPersonRoute(
                              codeUser: Utils.defaultOnEmpty(userName),
                            ),
                          );
                        }
                      },
                      onTapRemove: () async {
                        Alert.showAlertConfirm(
                          AlertConfirmParams(
                            context,
                            message: context.l10n.warningRemoveMember,
                            onPressed: () {
                              context.read<GroupChatDetailBloc>().add(
                                GroupChatDetailUpdated(
                                  GroupChatDetailUpdateRequestParams(
                                    id: widget.conversation?.id,
                                    action: GroupChatAction.removeMember.name,
                                    members: [item?.username ?? ''],
                                  ),
                                ),
                              );
                            },
                            confirmText: context.l10n.delete,
                            cancelButton: context.l10n.cancel,
                          ),
                        );
                      },
                      addAdmin: (final member) async => showModalBottomSheet(
                        context: context,
                        useSafeArea: true,
                        isScrollControlled: true,
                        backgroundColor: Theme.of(
                          context,
                        ).scaffoldBackgroundColor,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(14),
                          ),
                        ),
                        builder: (final _) => Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColorDark.withValues(alpha: 0),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(14),
                              topRight: Radius.circular(14),
                            ),
                          ),
                          height: MediaQuery.of(context).size.height * 0.9,
                          child: BlocProvider.value(
                            value: context.read<GroupChatDetailBloc>(),
                            child: AddAdminBottomSheet(
                              member: member,
                              conversationId: widget.conversation?.id,
                              role: adminRole,
                              memberRulesOfGroup:
                                  widget.conversation?.memberRules ?? [],
                              canTransferOwnership:
                                  widget
                                      .conversation
                                      ?.conversationDetails
                                      ?.role ==
                                  UserChatRole.owner.name,
                            ),
                          ),
                        ),
                      ).whenComplete(() => _memberPagingController.refresh()),
                      onShowMenuPopup: ({required final bool isShowing}) {
                        if (isShowing) {
                          blurValue = 8;
                        } else {
                          blurValue = 0;
                        }
                        if (mounted) {
                          modalState(() {});
                        }
                      },
                    );
                  },
                  separator: const Divider(height: 24),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyAvatar(final BuildContext context) {
    return Column(
      children: [
        Container(
          width: 98,
          height: 98,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                Utils.randomColor(
                  widget.conversation?.id ?? '',
                ).withValues(alpha: 0.5),
                Utils.randomColor(widget.conversation?.id ?? ''),
              ],
            ),
          ),
          child: Center(
            child: Text(
              Utils.defaultOnEmpty(
                widget.conversation?.name,
              ).firstOrEmpty().toUpperCase(),
              style: Theme.of(context).textTheme.displaySmall?.copyWith(
                color: Theme.of(context).colorScheme.surface,
                fontWeight: AppFontWeight.medium,
              ),
            ),
          ),
        ),
        if (editMode &&
            (userRules.contains(UserChatRule.updateAvatar.name) ||
                widget.conversation?.conversationDetails?.role ==
                    UserChatRole.owner.name))
          _buildButtonEditAvatar(context)
        else
          const SizedBox(height: 12),
      ],
    );
  }

  Widget _buildAvatarWithUrl(final BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(98),
          child: EzCachedNetworkImageWithHeroAnimation(
            width: 98,
            height: 98,
            imageUrl: groupAvatar ?? '',
            actionIcon: (final _) => Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(
                  context,
                ).primaryColorDark.withValues(alpha: 0.5),
              ),
              child: InkWell(
                onTap: () async {
                  ServiceDownloader.instance.onTapDownload(
                    context,
                    groupAvatar ?? '',
                    TaskInfo(),
                    '${DateTime.now().millisecondsSinceEpoch}.jpg',
                  );
                },
                child: const Padding(
                  padding: EdgeInsets.all(15),
                  child: Icon(Icons.download, color: Colors.white),
                ),
              ),
            ),
          ),
        ),
        if (editMode &&
            (userRules.contains(UserChatRule.updateAvatar.name) ||
                widget.conversation?.conversationDetails?.role ==
                    UserChatRole.owner.name))
          _buildButtonEditAvatar(context)
        else
          const SizedBox(height: 12),
      ],
    );
  }

  Widget _buildButtonEditAvatar(final BuildContext context) {
    return IconButton(
      onPressed: () async {
        showBaseBottomSheet(
          context: context,
          height: 300,
          title: context.l10n.avatar,
          child: SelectImageBottomSheet(
            isCropStyleCircle: true,
            onSelected: (final path) {
              context.read<GroupChatDetailBloc>().add(
                GroupChatDetailUpdated(
                  GroupChatDetailUpdateRequestParams(
                    avatar: path,
                    id: widget.conversation?.id,
                    action: GroupChatAction.updateAvatar.name,
                  ),
                ),
              );
            },
          ),
        );
      },
      icon: Text(
        context.l10n.setNewPhoto,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).primaryColor,
          fontSize: 15,
        ),
      ),
    );
  }

  Widget _buildUserInfo(final BuildContext context) {
    if (phone.isEmpty) {
      return const SizedBox.shrink();
    }
    return Container(
      width: double.maxFinite,
      decoration: ShapeDecoration(
        color: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              context.l10n.phoneNumber,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontSize: 13),
            ),
            GestureDetector(
              onTap: () =>
                  Utils.onTapDynamicLink(linkType: LinkType.phone, link: phone),
              child: Text(
                phone,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 15,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _setPermission(
    final BuildContext context,
    final UserChatRole role,
  ) {
    return showModalBottomSheet(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(14)),
      ),
      builder: (final _) => Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColorDark.withValues(alpha: 0),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(14),
            topRight: Radius.circular(14),
          ),
        ),
        height: MediaQuery.sizeOf(context).height * 0.9,
        child: StatefulBuilder(
          builder: (final _, final modalState) {
            return Stack(
              children: [
                Scaffold(
                  backgroundColor: const Color(0xffF3F3F3),
                  appBar: AppBar(
                    backgroundColor: const Color(0xffF3F3F3),
                    elevation: 0,
                    centerTitle: true,
                    title: Text(
                      context.l10n.member,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                      ),
                    ),
                    leading: IconButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      icon: Text(
                        context.l10n.close,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 15,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ),
                  body: BlocProvider.value(
                    value: context.read<GroupChatDetailBloc>(),
                    child:
                        BlocBuilder<GroupChatDetailBloc, GroupChatDetailState>(
                          builder: (final context, final state) {
                            return Column(
                              children: <Widget>[
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 14,
                                  ),
                                  child: ChatSearchBar(
                                    center: false,
                                    controller: searchController,
                                    autofocus: true,
                                    onChanged: (final keySearch) {
                                      timer?.cancel();
                                      timer = Timer(
                                        const Duration(milliseconds: 800),
                                        () async {
                                          _memberPagingController.refresh();
                                        },
                                      );
                                    },
                                  ),
                                ),
                                _buildMemberList(
                                  context,
                                  modalState,
                                  isShowTitle: false,
                                  role: role,
                                ),
                              ],
                            );
                          },
                        ),
                  ),
                ),
                BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: blurValue,
                    sigmaY: blurValue,
                  ),
                  child: const SizedBox.shrink(),
                ),
              ],
            );
          },
        ),
      ),
    ).whenComplete(() {
      searchController.clear();
      _memberPagingController.refresh();
      if (context.mounted) {
        context.read<GroupChatDetailBloc>().add(
          GroupChatDetailGetUserExceptioned(
            GroupChatDetailGetUserExceptionRequestParams(
              conversationId: widget.conversation?.id,
              roles: [role.name],
            ),
          ),
        );
      }
    });
  }

  Future<void> _onTapMemberField(final BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      barrierColor: Theme.of(context).scaffoldBackgroundColor,
      builder: (final _) => StatefulBuilder(
        builder: (final _, final modalState) {
          return Stack(
            children: [
              Scaffold(
                appBar: AppBar(
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(4.0),
                    child: Container(
                      color: Theme.of(context).dividerColor,
                      height: 1,
                    ),
                  ),
                  title: Text(
                    editMode ? Strings.empty : context.l10n.member,
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  centerTitle: true,
                ),
                body: BlocProvider.value(
                  value: context.read<GroupChatDetailBloc>(),
                  child: BlocBuilder<GroupChatDetailBloc, GroupChatDetailState>(
                    builder: (final context, final state) {
                      return Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          children: <Widget>[
                            if (userRules.contains(UserChatRule.addMember.name))
                              _buildAddMemberButton(context),
                            _buildMemberList(
                              context,
                              modalState,
                              isShowTitle: false,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: blurValue, sigmaY: blurValue),
                child: const SizedBox.shrink(),
              ),
            ],
          );
        },
      ),
    ).whenComplete(() => setState(() {}));
  }

  Widget _buildAddMemberButton(final BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () async => _onTapAddMember(context),
      child: Container(
        decoration: ShapeDecoration(
          color: Theme.of(context).colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: Theme.of(context).dividerColor),
          ),
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          children: <Widget>[
            Container(
              width: 48,
              height: 48,
              decoration: const ShapeDecoration(
                shape: CircleBorder(),
                color: AppColors.primaryBackground,
              ),
              child: Icon(Icons.add, color: Theme.of(context).primaryColor),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.l10n.addMember,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onTapAddMember(final BuildContext context) {
    return context.router
        .push(
          UserListRoute(
            conversation: widget.conversation,
            currentUserList:
                _memberPagingController.itemList
                    ?.map(
                      (final user) => CreateChatGroupUserLoadItems(
                        username: user?.username,
                        name: user?.name,
                        avatar: user?.avatar,
                        phone: user?.phone,
                        departmentName: user?.departmentName,
                      ),
                    )
                    .toList() ??
                [],
          ),
        )
        .then((final value) {
          if (value is List<CreateChatGroupUserLoadItems?>) {
            _memberPagingController.itemList?.addAll(
              value
                  .map(
                    (final e) => GroupChatDetailMemberInfoLoadItems(
                      username: e?.username,
                      name: e?.name,
                      avatar: e?.avatar,
                      phone: e?.phone,
                      departmentName: e?.departmentName,
                    ),
                  )
                  .toList(),
            );
            widget.conversation?.membersInfo.add(
              ChatListItemsMembersInfo(
                username: value.first?.username,
                name: value.first?.name,
                avatar: value.first?.avatar,
                phone: value.first?.phone,
                departmentName: value.first?.departmentName,
              ),
            );
            setState(() {});
          }
        });
  }

  Widget _buildActionButton({
    required final String label,
    required final Widget icon,
    final Color? color,
    final void Function()? onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: Container(
          decoration: ShapeDecoration(
            color: Theme.of(context).colorScheme.surface,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Column(
            children: <Widget>[
              const Spacer(),
              icon,
              const SizedBox(height: 6),
              AutoSizeText(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color ?? Theme.of(context).primaryColor,
                ),
                maxLines: 1,
              ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  void _refreshPage() {
    if (_tabController.index == 0) {
      _filePagingController.refresh();
    }
    if (_tabController.index == 1) {
      _mediaPagingController.refresh();
    }
    if (_tabController.index == 2) {
      _linkPagingController.refresh();
    }
    lastUpdate = '';
    scrollController.jumpTo(scrollController.position.maxScrollExtent - 50);
  }
}
