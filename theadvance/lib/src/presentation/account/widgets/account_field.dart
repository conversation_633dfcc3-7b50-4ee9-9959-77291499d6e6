// Flutter imports:
import 'package:flutter/material.dart';

class AccountField extends StatelessWidget {
  const AccountField({
    super.key,
    this.isOnlyReady = true,
    this.maxLines,
    required this.controller,
    required this.label,
    required this.hintText,
    this.iconRight,
    this.maxLength,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.bottomPadding = 8.0,
    this.radius = 8.0,
    this.filled = true,
    this.autoFocus = false,
    this.typeInput = TextInputType.text,
    this.isShowPass = false,
  });
  final bool isShowPass;
  final bool isOnlyReady;
  final int? maxLines;
  final TextEditingController controller;
  final String label;
  final String hintText;
  final Widget? iconRight;
  final int? maxLength;
  final void Function()? onTap;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final double bottomPadding;
  final double radius;
  final bool filled;
  final bool autoFocus;
  final TextInputType typeInput;

  @override
  Widget build(final BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: const Color(0xffDEE3ED)),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            contentPadding: EdgeInsets.all(14.0),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(radius),
          child: Padding(
            padding: ((maxLines != null) && !isOnlyReady)
                ? EdgeInsets.only(bottom: (maxLines == 1 ? 0 : bottomPadding))
                : EdgeInsets.zero,
            child: TextField(
              obscureText: isShowPass,
              keyboardType: typeInput,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                decorationColor: Colors.white.withValues(alpha: 0),
              ),
              controller: controller,
              maxLines: maxLines,
              readOnly: isOnlyReady,
              maxLength: maxLength,
              onTap: onTap,
              onChanged: onChanged,
              autofocus: autoFocus,
              decoration: InputDecoration(
                label: label.isEmpty ? null : Text(label),
                labelStyle: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(color: Colors.black45),
                suffixIcon: iconRight,
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                hintText: hintText,
                hintStyle: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(color: Colors.black45),
                counterText: isOnlyReady ? '' : null,
                counterStyle: TextStyle(
                  color: Colors.grey.withValues(alpha: .9),
                ),
                filled: filled,
                fillColor: Colors.white,
              ),
              onSubmitted: onSubmitted,
            ),
          ),
        ),
      ),
    );
  }
}
