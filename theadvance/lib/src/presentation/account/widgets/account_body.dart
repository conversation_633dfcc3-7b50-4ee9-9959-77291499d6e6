// Dart imports:

// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/params/request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/routes/routes.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../data/models/nd_models.dart';
import '../../../domain/usecases/user/save_enable_online_logger_usecase.dart';
import '../../../injector/injector.dart';
import '../../_blocs/collaborator_user/collaborator_user_bloc.dart';
import '../../collaborator/collaborator.dart';
import '../../collaborator/more/widgets/widgets.dart';
import 'account_avatar.dart';

class AccountBody extends StatefulWidget {
  const AccountBody({final Key? key}) : super(key: key);
  static ValueNotifier<UserModel?> user = ValueNotifier(null);
  @override
  State<AccountBody> createState() => _AccountBodyState();
}

class _AccountBodyState extends State<AccountBody> {
  ProfileItemsModel? data;
  var enableLoggerCount = 0;
  bool enableLogger = false;
  String version = '';
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      final user = EZCache.shared.getUserProfile();
      AccountBody.user.value = user;
    });
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocListener<SettingBloc, SettingState>(
      listener: (final context, final state) {
        if (state is SettingLoadSuccess) {
          version = state.version;
        }
        if (state is SettingLoadFailure) {
          unawaited(
            Alert.showAlert(
              AlertParams(
                context,
                context.l10n.unknown,
                acceptButton: context.l10n.accept,
              ),
            ),
          );
        }
      },
      child: BlocConsumer<ProfileBloc, ProfileState>(
        listener: (final context, final state) async {
          if (state is ProfileHomeFetchSuccess) {
            HomeBody.services.setValue(state.services?.menuItems ?? []);
          }
          if (state is ProfileUpdateFailure) {
            Alert.showAlert(
              AlertParams(
                context,
                state.apiError?.message,
                acceptButton: context.l10n.close,
              ),
            );
          }
          if (state is ProfileLoadFailure) {
            ApiErrorDialog.show(
              ApiErrorParams(
                context,
                state.apiError,
                acceptButtonTitle: context.l10n.accept,
              ),
            );
          }

          if (state is ProfileUpdateSuccess) {
            context.read<CollaboratorUserBloc>().add(
              CollaboratorUserSynced(state.user),
            );
          }
          if (state is ProfileLoadSuccess) {
            data = state.itemsProfile;
            AccountBody.user.value = data?.userBundle?.user;
          }
        },
        builder: (final context, final state) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      spacing: 24,
                      children: [
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _buildAvatar(),
                        ),
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SizedBox(
                            child: Column(
                              children: [
                                const SizedBox(height: 12),
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: buildItem(
                                    iconPath: AppIcons.icProfileInfo,
                                    title: context.l10n.personInfo,
                                    router: ProfileRoute(
                                      info: data?.userBundle,
                                    ),
                                    isLast: true,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SizedBox(
                            child: Column(
                              children: [
                                const SizedBox(height: 12),
                                buildItem(
                                  iconPath: AppIcons.icAsset,
                                  title: context.l10n.asset,
                                  router: AccountAssetRoute(asset: data?.asset),
                                ),
                                buildItem(
                                  iconPath: AppIcons.icSocialInsurance,
                                  title: context.l10n.socialInsurance,
                                  router: AccountPreviewRoute(
                                    asset: data?.socialInsurance,
                                  ),
                                ),
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: buildItem(
                                    iconPath: AppIcons.icWorkContract,
                                    title: context.l10n.laborContract,
                                    router: AccountPreviewRoute(
                                      asset: data?.workingContract,
                                    ),
                                    isLast: true,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              const SizedBox(height: 12),
                              if (enableLogger)
                                buildMenuItem(
                                  title: context.l10n.devFeatures,
                                  icon: Icon(
                                    Icons.developer_mode_rounded,
                                    color: Theme.of(context).primaryColor,
                                    size: 20,
                                  ),
                                  onTap: () async => AutoRouter.of(
                                    context,
                                  ).pushNamed(Routes.dev),
                                ),
                              buildItem(
                                iconPath: AppIcons.icMainScreen,
                                title: context.l10n.mainScreen,
                                router: const EditHomeMenuRoute(),
                              ),
                              buildItem(
                                iconPath: AppIcons.icCheckinReminder,
                                title: context.l10n.checkinReminder,
                                router: const CheckinReminderRoute(),
                              ),
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: buildItem(
                                  iconPath: AppIcons.icSetting,
                                  title: context.l10n.settings,
                                  router: SettingRoute(),
                                  isLast: true,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: GestureDetector(
                  onDoubleTap: () async {
                    if (enableLoggerCount < 2) {
                      enableLoggerCount++;
                    } else {
                      enableLogger = !enableLogger;
                      getIt<SaveEnableOnlineLoggerUseCase>()(
                        params: enableLogger,
                      );
                      EzToast.showToast(message: 'Online logger $enableLogger');
                      setState(() {});
                    }
                  },
                  child: Text('${context.l10n.version} $version'),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget buildItem({
    final String? iconPath,
    final bool isLast = false,
    final String title = Strings.empty,
    final PageRouteInfo<dynamic>? router,
  }) {
    return GestureDetector(
      onTap: () async {
        if (router != null) {
          context.router.push(router);
        }
      },
      child: SizedBox(
        width: double.maxFinite,
        child: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 16),
            child: Column(
              spacing: 12,
              children: [
                Row(
                  spacing: 8,
                  children: [
                    if (iconPath != null)
                      EZResources.image(ImageParams(name: iconPath)),
                    Expanded(
                      child: Column(
                        spacing: 12,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(title),
                                EZResources.image(
                                  ImageParams(
                                    name: AppIcons.icForward,
                                    size: const ImageSize.square(24),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (!isLast)
                  const Padding(
                    padding: EdgeInsets.only(left: 40),
                    child: Divider(thickness: .35),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildMenuItem({
    required final String title,
    required final Widget icon,
    required final Function() onTap,
  }) {
    return Column(
      children: [
        InkWell(
          onTap: () => onTap(),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: MenuSettingItem(image: icon, title: title),
          ),
        ),
        const Divider(thickness: .35),
      ],
    );
  }

  Widget _buildAvatar() {
    return SizedBox(
      width: double.maxFinite,
      child: Padding(
        padding: const EdgeInsets.only(left: 12, top: 12, bottom: 12, right: 8),
        child: Row(
          spacing: 8,
          children: [
            ValueListenableBuilder(
              valueListenable: AccountBody.user,
              builder: (final context, final vUser, final child) {
                return AccountAvatar(
                  size: 48,
                  tag: vUser?.employeeId ?? '',
                  url: vUser?.avatar ?? Strings.empty,
                );
              },
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ValueListenableBuilder(
                    valueListenable: AccountBody.user,
                    builder: (final context, final vUser, final child) {
                      return GestureDetector(
                        onTap: () async => context.router.push(
                          StoryPersonRoute(
                            codeUser: vUser?.employeeId ?? Strings.empty,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              vUser?.name ?? Strings.empty,
                              textAlign: TextAlign.center,
                              style: Theme.of(
                                context,
                              ).textTheme.labelLarge?.copyWith(fontSize: 18),
                            ),
                            Text(
                              context.l10n.personInfo,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.black54),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  EZResources.image(
                    ImageParams(
                      name: AppIcons.icForward,
                      size: const ImageSize.square(24),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
