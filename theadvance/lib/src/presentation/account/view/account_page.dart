// Flutter imports:
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../injector/injector.dart';
import '../../_blocs/authentication/authentication_bloc.dart';
import '../../collaborator/profile/bloc/profile_bloc.dart';
import '../../collaborator/setting/setting.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../widgets/account_body.dart';

class AccountPage extends StatelessWidget {
  const AccountPage({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (final context) => getIt<ProfileBloc>()
            ..add(ProfileHomeFetched())
            ..add(ProfileLoaded()),
        ),
        BlocProvider(
          create: (final context) =>
              getIt<SettingBloc>()..add(SettingStarted()),
        ),
      ],
      child: BaseLayout(
        leading: const SizedBox(),
        title: Text(
          context.l10n.account,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              context.read<AuthenticationBloc>().add(AuthenticationLoggedOut());
            },
            tooltip: context.l10n.logout,
            icon: EZResources.image(ImageParams(name: AppIcons.icLogout)),
          ),
        ],
        body: const AccountBody(),
      ),
    );
  }
}
