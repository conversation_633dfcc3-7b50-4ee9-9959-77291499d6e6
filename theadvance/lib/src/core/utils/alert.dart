// ignore_for_file: long-parameter-list, use_build_context_synchronously

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:animations/animations.dart';
import 'package:ez_alert/ez_alert.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_feedback/ez_feedback.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../domain/usecases/helper/get_access_token_usecase.dart';
import '../../injector/injector.dart';
import '../../presentation/_blocs/general_bloc/general_bloc.dart';
import '../../presentation/widgets/report_floating_action.dart';
import '../params/request_params.dart';
import '../routes/app_router.dart';
import 'screen_record_helper.dart';

class Alert {
  static Future<void> showAlertActionCollaborator(
    final AlertActionCollaboratorParams params,
  ) async {
    final config = await EZCache.shared.configurations;
    final allowFeedback = config?.reportBug ?? false;
    final accessToken = await getIt<GetAccessTokenUseCase>().call();

    if (params.context.mounted) {
      return EzAlert.showAlertActionCollaborator(
        EzAlertActionCollaboratorParams(
          params.context,
          image: params.image,
          title: params.title,
          message: params.message,
          acceptButton: params.acceptButton,
          closeButton: params.closeButton,
          confirm: params.confirm,
          actiontype: params.actiontype,
          onPressed: params.onPressed,
          acceptIcon: params.acceptIcon,
          onCancel: params.onCancel,
          acceptButton2: params.acceptButton2,
          acceptIcon2: params.acceptIcon2,
          onPressed2: params.onPressed2,
          onFeedback: allowFeedback && accessToken.isNotEmpty
              ? showFeedbackView
              : null,
        ),
      );
    }
  }

  static Future<void> showAlertCollaborator(
    final BuildContext context, {
    final String title = '',
    final String message = '',
    final double? heightDialog,
    required final Widget image,
    final Function()? onAccept,
    final bool barrierDismissible = true,
  }) async {
    final config = await EZCache.shared.configurations;
    final allowFeedback = config?.reportBug ?? false;
    final accessToken = await getIt<GetAccessTokenUseCase>().call();

    if (context.mounted) {
      return EzAlert.showAlertCollaborator(
        context,
        image: image,
        title: title,
        message: message,
        barrierDismissible: barrierDismissible,
        heightDialog: heightDialog,
        onAccept: onAccept,
        onFeedback: allowFeedback && accessToken.isNotEmpty
            ? showFeedbackView
            : null,
      );
    }
  }

  static FadeScaleTransitionConfiguration fadeScaleConfig() =>
      const FadeScaleTransitionConfiguration(
        transitionDuration: Duration(milliseconds: 500),
        reverseTransitionDuration: Duration(milliseconds: 200),
        barrierDismissible: false,
      );

  static Future<void> showAlertError(final AlertErrorParams params) async {
    EzAlert.showAlertWithIcon(
      EzAlertWithIconParams(
        params.context,
        params.message,
        title: params.title,
        textColor: Theme.of(params.context).colorScheme.error,
        iconWidget: params.icon,
        closeButton: params.closeButton,
      ),
    );
  }

  static Future<void> showAlertSuccess(final AlertErrorParams params) async {
    EzAlert.showAlertWithIcon(
      EzAlertWithIconParams(
        params.context,
        params.message,
        title: params.title,
        textColor: Theme.of(params.context).primaryColor,
        iconWidget: params.icon,
        closeButton: params.closeButton,
      ),
    );
  }

  static Future<void> showSuccessDialog(
    final SuccessDialogParams params,
  ) async {
    return showDialog(
      useRootNavigator: false,
      context: params.context,
      barrierDismissible: false, // user must tap button!
      builder: (final context) {
        return StatefulBuilder(
          builder: (final context, final setState) {
            return EzAlert.buildSuccessDialog(
              EzAlertSuccessDialogParams(
                message: params.message,
                context: context,
                isSuccess: params.isSuccess,
                qrcode: params.qrcode,
                button: params.button,
                image: params.image,
              ),
            );
          },
        );
      },
    );
  }

  static Future<void> networkError(
    final BuildContext context, {
    required final String connectionError,
    required final String acceptButton,
  }) async {
    showAlert(
      AlertParams(context, connectionError, acceptButton: acceptButton),
    );
  }

  static Future<void> showAlert(final AlertParams params) async {
    final config = await EZCache.shared.configurations;
    final allowFeedback = config?.reportBug ?? false;
    final accessToken = await getIt<GetAccessTokenUseCase>().call();

    // set up the button
    if (params.context.mounted) {
      EzAlert.showAlert(
        params.context,
        EzAlertParams(
          title: params.title,
          confirmText: params.acceptButton ?? params.context.l10n.accept,
          msg: params.message ?? params.context.l10n.unknown,
          onConfirm: () => params.onPressOK?.call(),
          isPop: params.isPop,
          onFeedback:
              allowFeedback && accessToken.isNotEmpty && params.enableFeedback
              ? showFeedbackView
              : null,
        ),
      );
    }
  }

  static void showFeedbackView() {
    ScreenRecordHelper.instance.isDebugVN.value = false;
    ReportFloatingAction.showVidRecControllerVN.value = true;
  }

  static void showPictureFeedback() {
    final context = getIt<AppRouter>().navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    return EzFeedbackService.showFeedback(
      context,
      onDone: (final description, final imageBytes, final p2) async {
        final context = getIt<AppRouter>().navigatorKey.currentContext;
        if (context == null) {
          return;
        }

        if (description.isEmpty) {
          Alert.showAlertCollaborator(
            context,
            title: context.l10n.sendFailure,
            image: EZResources.image(
              ImageParams(
                name: AppImages.warning,
                size: ImageSize.square(MediaQuery.sizeOf(context).width / 2.8),
              ),
            ),
            message: context.l10n.pleaseInputDescription,
            onAccept: Alert.showFeedbackView,
          );

          return;
        }

        context.read<GeneralBloc>().add(
          GeneralSendFeedback(
            isVideo: false,
            description: description,
            bytes: imageBytes,
          ),
        );
      },
    );
  }

  static Widget buildFeedbackOption(
    final BuildContext context, {
    required final String label,
    required final IconData icon,
    required final void Function() onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: const LinearGradient(
            colors: [Color(0xff1101A2), Color(0xff1D01C8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Center(child: Icon(icon, size: 32)),
            const SizedBox(width: 12),
            Text(
              label,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: Theme.of(context).colorScheme.surface,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward,
              color: Theme.of(context).colorScheme.surface,
            ),
          ],
        ),
      ),
    );
  }

  static Future<void> popup(
    final BuildContext context,
    final Widget widget, {
    final Function()? whenComplete,
  }) async {
    EzAlert.popup(context, widget, whenComplete);
  }

  static Future<void> showAlertConfirm(final AlertConfirmParams params) async {
    final config = await EZCache.shared.configurations;
    final allowFeedback = config?.reportBug ?? false;
    final accessToken = await getIt<GetAccessTokenUseCase>().call();

    if (params.context.mounted) {
      return EzAlert.showAlertConfirm(
        params.context,
        EzAlertConfirmParams(
          colorConfirmText: params.colorConfirmText,
          title: params.title,
          confirmText: params.confirmText,
          cancelText: params.cancelButton,
          onPressed: params.onPressed ?? () {},
          message: params.message,
          onPressedCancel: params.onPressedCancel,
          onFeedback: allowFeedback && accessToken.isNotEmpty
              ? showFeedbackView
              : null,
          image: params.image,
        ),
      );
    }
  }

  static Future<void> showAlertConfirmV2(
    final AlertConfirmParams params,
  ) async {
    final config = await EZCache.shared.configurations;
    final allowFeedback = config?.reportBug ?? false;
    final accessToken = await getIt<GetAccessTokenUseCase>().call();

    if (params.context.mounted) {
      return EzAlert.showAlertConfirmV2(
        params.context,
        EzAlertConfirmParams(
          colorConfirmText: params.colorConfirmText,
          title: params.title,
          confirmText: params.confirmText,
          cancelText: params.cancelButton,
          onPressed: params.onPressed ?? () {},
          message: params.message,
          onPressedCancel: params.onPressedCancel,
          onFeedback: allowFeedback && accessToken.isNotEmpty
              ? showFeedbackView
              : null,
          image: params.image,
          gradient: params.gradient,
        ),
      );
    }
  }

  static Future<void> showAlertSetting(final AlertSettingParams params) async {
    return EzAlert.showAlertSetting(
      EzAlertSettingParams(
        params.context,
        params.title,
        params.message,
        closeButton: params.closeButton ?? params.context.l10n.close,
        openSettingButton:
            params.openSettingButton ?? params.context.l10n.openSettings,
        settingType: params.settingType,
      ),
    );
  }
}
