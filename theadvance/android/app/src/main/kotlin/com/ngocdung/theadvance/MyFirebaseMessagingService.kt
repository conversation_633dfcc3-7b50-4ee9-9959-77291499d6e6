package com.ngocdung.theadvance

import android.content.SharedPreferences
import com.example.ez_calling.stringee_common.StringeeCallService
import com.example.ez_calling.stringee_common.StringeeConnectionManager
import com.example.ez_calling.stringee_common.StringeeUtils
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage


class MyFirebaseMessagingService : FirebaseMessagingService() {
    private lateinit var accessToken: String
    private lateinit var sharedPref: SharedPreferences

    companion object {
        private const val TAG = "MyFirebaseMsgService"
    }

    // [START receive_message]
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        // Not getting messages here? See why this may be: https://goo.gl/39bRNJ
        // Check if message contains a data payload.
        if (remoteMessage.data.isNotEmpty()) {
            // Handle message within 10 seconds
            messageHandler(remoteMessage.data)
        }


        // Check if message contains a notification payload.
        remoteMessage.notification?.let {
//            Log.d(TAG, "Message Notification Body: ${it.body}")
        }
    }

    // [START on_new_token]
    /**
     * Called if the FCM registration token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the
     * FCM registration token is initially generated so this is where you would retrieve the token.
     */
    override fun onNewToken(token: String) {
        // If you want to send messages to this application instance or
        // manage this apps subscriptions on the server side, send the
        // FCM registration token to your app server.
        sendRegistrationToServer(token)
    }
    private fun sendRegistrationToServer(token: String?) {
        // TODO: Implement this method to send token to your app server.
    }
    // [END on_new_token]

    private fun messageHandler(msgData: Map<String, String>?) {
        val payload = msgData?.get("payload")
        // payload is call data from firebase
        if (payload != null) {
            android.util.Log.d(TAG, "Received call payload: $payload")

            // Initialize connection manager for proper background handling
            StringeeConnectionManager.initialize(applicationContext)

            // Parse the payload to extract call information
            try {
                val callData = org.json.JSONObject(payload)
                val callId = callData.optString("callId", "")
                val from = callData.optString("from", "")
                val to = callData.optString("to", "")
                val isVideoCall = callData.optBoolean("isVideoCall", false)

                android.util.Log.d(TAG, "Call details - ID: $callId, From: $from, To: $to, Video: $isVideoCall")

                // Store call info for recovery
                val sharedPref = applicationContext.getSharedPreferences("StringeeCallData", android.content.Context.MODE_PRIVATE)
                with(sharedPref.edit()) {
                    putString("pendingCallId", callId)
                    putString("pendingCallFrom", from)
                    putString("pendingCallTo", to)
                    putBoolean("pendingCallIsVideo", isVideoCall)
                    putLong("pendingCallTime", System.currentTimeMillis())
                    apply()
                }

                // Start the call service to maintain connection
                StringeeCallService.startService(applicationContext, callId)

                // Connect to Stringee with enhanced background handling
                StringeeUtils.connectStringee(applicationContext, {
                    android.util.Log.d(TAG, "Stringee connected for incoming call")
                }, true)

            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error parsing call payload: ${e.message}")
                // Fallback to original behavior
                StringeeUtils.connectStringee(applicationContext, onReceiveBg = true)
            }
        }
    }
}