plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace="com.ngocdung.theadvance"
    // ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
    flavorDimensions "enviroment"

    productFlavors {
        dev {
            dimension "enviroment"
            applicationId "com.ngocdung.theadvance"
            resValue "string", "app_name", "The Advance - dev"
        }
        prod {
            dimension "enviroment"
            applicationId "com.ngocdung.theadvance"
            resValue "string", "app_name", "The Advance"
        }
        acc {
            dimension "enviroment"
            applicationId "com.thammyvienngocdung.acc"
            resValue "string", "app_name", "Ngọc Dung Internal"
        }
    }

    // ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----

   compileSdkVersion 35
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }
    
    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.ngocdung.theadvance"
        minSdkVersion 24
        targetSdkVersion 35
        multiDexEnabled true
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        proguardFiles
    }

    // buildTypes {
    //     release {
    //         // TODO: Add your own signing config for the release build.
    //         // Signing with the debug keys for now, so `flutter run --release` works.
    //         signingConfig signingConfigs.debug
    //     }
    // }
    signingConfigs {
       release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile file(keystoreProperties['storeFile'])
           storePassword keystoreProperties['storePassword']
       }
   }
   buildTypes {
       release {

           signingConfig signingConfigs.release

           minifyEnabled true
//           useProguard true

           proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
   }

   compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    // kotlin {
    //     jvmToolchain(17)
    // }

}

flutter {
    source '../..'
}

dependencies {
    implementation "com.google.firebase:firebase-messaging:23.0.8"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'com.google.android.gms:play-services-mlkit-barcode-scanning:18.1.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.github.HBiSoft:HBRecorder:2.0.5'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test:runner:1.4.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'com.android.volley:volley:1.2.1'
}
