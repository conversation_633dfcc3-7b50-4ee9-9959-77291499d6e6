# Stringee Background Connection Fix

## Problem
The Stringee library was losing connection when the app went to background and attempting to make multiple network requests, causing the Android system to restrict background network access and leading to out-of-memory crashes when the app was reopened.

## Root Cause
1. **No Lifecycle Management**: The Stringee client didn't properly handle app background/foreground transitions
2. **Continuous Reconnection Attempts**: When connection was lost in background, the client kept trying to reconnect indefinitely
3. **Memory Leaks**: Multiple failed connection attempts accumulated in memory
4. **Background Network Restrictions**: Android restricts background network access, causing connection failures

## Solution Overview
Implemented a comprehensive lifecycle-aware connection management system:

### 1. StringeeConnectionManager
- **Lifecycle Awareness**: Uses `ProcessLifecycleOwner` to detect app background/foreground states
- **Smart Reconnection**: Implements exponential backoff with maximum retry limits
- **Background Handling**: Disconnects Stringee client when app goes to background (unless in call)
- **Foreground Recovery**: Automatically reconnects when app returns to foreground

### 2. Key Features
- **Automatic Disconnection**: Disconnects Stringee when app goes to background to prevent memory leaks
- **Call Protection**: Maintains connection during active calls even in background
- **Retry Logic**: Implements smart retry with exponential backoff (5s, 10s, 15s max)
- **Token Refresh**: Automatically fetches new tokens when connection fails
- **Memory Management**: Properly cancels timers and cleans up resources

### 3. Files Modified

#### Core Connection Management
- `StringeeConnectionManager.kt` - New lifecycle-aware connection manager
- `Common.kt` - Added background state tracking variables
- `StringeeUtils.kt` - Updated to use new connection manager

#### Integration Points
- `EzCallingPlugin.kt` - Initialize connection manager on login and calls
- `MyFirebaseMessagingService.kt` - Use connection manager for push notifications

## Technical Implementation

### Background State Detection
```kotlin
override fun onStart(owner: LifecycleOwner) {
    // App moved to foreground - reconnect if needed
    Common.isAppInBackground = false
    if (pendingConnection || !Common.client?.isConnected) {
        connectStringeeWithRetry(context)
    }
}

override fun onStop(owner: LifecycleOwner) {
    // App moved to background - disconnect unless in call
    Common.isAppInBackground = true
    if (!Common.isInCall) {
        disconnectStringee()
    }
}
```

### Smart Reconnection Logic
```kotlin
private fun scheduleReconnect(context: Context, onConnected: () -> Unit, onReceiveBg: Boolean) {
    if (Common.reconnectAttempts >= Common.maxReconnectAttempts) {
        pendingConnection = true // Will reconnect on foreground
        return
    }
    
    val delay = Common.reconnectDelay * Common.reconnectAttempts // Exponential backoff
    Common.reconnectTimer = Timer()
    Common.reconnectTimer?.schedule(delay) {
        if (!Common.isAppInBackground) {
            connectStringeeInternal(context, onConnected, onReceiveBg)
        }
    }
}
```

### Memory Management
```kotlin
private fun disconnectStringee() {
    cancelReconnectTimer() // Cancel any pending reconnection attempts
    Common.client?.disconnect()
    Common.client = null
}
```

## Benefits
1. **No More Memory Leaks**: Proper cleanup prevents accumulation of failed connections
2. **Battery Optimization**: Reduces background network activity
3. **Reliable Reconnection**: Smart retry logic ensures connection when needed
4. **Call Continuity**: Maintains connection during active calls
5. **Android Compliance**: Respects Android's background execution limits

## Usage
The connection manager is automatically initialized when:
- User logs in (`EzCallingPlugin.loggedin`)
- Making outgoing calls (`EzCallingPlugin.makeCall`)
- Receiving push notifications (`MyFirebaseMessagingService`)

No additional code changes required in the Flutter layer - the fix is transparent to the app logic.

## Testing Recommendations
1. Test app backgrounding during idle state (should disconnect)
2. Test app backgrounding during active call (should maintain connection)
3. Test foreground recovery after extended background time
4. Test incoming calls via push notifications
5. Monitor memory usage during background/foreground cycles
