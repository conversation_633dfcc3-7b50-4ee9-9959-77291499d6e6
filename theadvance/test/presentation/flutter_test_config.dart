// Dart imports:
import 'dart:async';

// Package imports:
import 'package:flutter_test/flutter_test.dart';

// Project imports:
import 'package:theadvance/src/injector/injector.dart';
import 'package:theadvance/src/presentation/assign_task/bloc/assign_task_bloc.dart';
import 'package:theadvance/src/presentation/branch_chat_list/branch_chat_list.dart';
import 'package:theadvance/src/presentation/chat_select_branch/bloc/chat_select_branch_bloc.dart';
import 'package:theadvance/src/presentation/checkin_photo/bloc/checkin_photo_bloc.dart';
import 'package:theadvance/src/presentation/consultation_customer/bloc/bloc.dart';
import 'package:theadvance/src/presentation/consultation_manager/bloc/consultation_manager_bloc.dart';
import 'package:theadvance/src/presentation/create_chat_folder/create_chat_folder.dart';
import 'package:theadvance/src/presentation/create_customer/bloc/create_customer_bloc.dart';
import 'package:theadvance/src/presentation/customer_booking_info/bloc/customer_booking_info_bloc.dart';
import 'package:theadvance/src/presentation/customer_list/bloc/customer_list_bloc.dart';
import 'package:theadvance/src/presentation/customer_record/bloc/customer_record_bloc.dart';
import 'package:theadvance/src/presentation/customer_schedule/bloc/customer_schedule_bloc.dart';
import 'package:theadvance/src/presentation/detail_crm_customer/bloc/detail_crm_customer_bloc.dart';
import 'package:theadvance/src/presentation/detail_staff_evaluation_period/bloc/detail_staff_evaluation_period_bloc.dart';
import 'package:theadvance/src/presentation/dev/dev.dart';
import 'package:theadvance/src/presentation/hr_organization/bloc/hr_organization_bloc.dart';
import 'package:theadvance/src/presentation/medical_product_creation/bloc/medical_product_creation_bloc.dart';
import 'package:theadvance/src/presentation/medical_service_creation/bloc/medical_service_creation_bloc.dart';
import 'package:theadvance/src/presentation/medical_service_list/bloc/medical_service_list_bloc.dart';
import 'package:theadvance/src/presentation/medical_template_list/bloc/medical_template_list_bloc.dart';
import 'package:theadvance/src/presentation/px_list/bloc/px_list_bloc.dart';
import 'package:theadvance/src/presentation/px_recheck/bloc/px_recheck_bloc.dart';
import 'package:theadvance/src/presentation/px_task_list/bloc/px_task_list_bloc.dart';
import 'package:theadvance/src/presentation/px_unasigned/bloc/px_unasigned_bloc.dart';
import 'package:theadvance/src/presentation/px_unasigned_update/bloc/px_unasigned_update_bloc.dart';
import 'package:theadvance/src/presentation/select_px_room/bloc/select_px_room_bloc.dart';
import 'package:theadvance/src/presentation/staff_evaluation_periods/bloc/staff_evaluation_periods_bloc.dart';
import 'package:theadvance/src/presentation/taking_care_customer/bloc/taking_care_customer_bloc.dart';
import '../mock_repositories/mock_assign_task_repository.dart';
import '../mock_repositories/mock_branch_chat_list_repository.dart';
import '../mock_repositories/mock_chat_select_branch_repository.dart';
import '../mock_repositories/mock_checkin_photo_repository.dart';
import '../mock_repositories/mock_collaborator_user_repository.dart';
import '../mock_repositories/mock_consultation_customer_repository.dart';
import '../mock_repositories/mock_consultation_manager_repository.dart';
import '../mock_repositories/mock_create_chat_folder_repository.dart';
import '../mock_repositories/mock_create_customer_repository.dart';
import '../mock_repositories/mock_customer_booking_info_repository.dart';
import '../mock_repositories/mock_customer_list_repository.dart';
import '../mock_repositories/mock_customer_record_repository.dart';
import '../mock_repositories/mock_customer_repository.dart';
import '../mock_repositories/mock_customer_schedule_repository.dart';
import '../mock_repositories/mock_detail_crm_customer_repository.dart';
import '../mock_repositories/mock_detail_staff_evaluation_period_repository.dart';
import '../mock_repositories/mock_dev_repository.dart';
import '../mock_repositories/mock_hr_organization_repository.dart';
import '../mock_repositories/mock_medical_product_create_repository.dart';
import '../mock_repositories/mock_medical_service_create_repository.dart';
import '../mock_repositories/mock_medical_service_list_repository.dart';
import '../mock_repositories/mock_medical_template_list_repository.dart';
import '../mock_repositories/mock_px_list_repository.dart';
import '../mock_repositories/mock_px_recheck_repository.dart';
import '../mock_repositories/mock_px_task_list_repository.dart';
import '../mock_repositories/mock_px_unasigned_repository.dart';
import '../mock_repositories/mock_px_unasigned_update_repository.dart';
import '../mock_repositories/mock_select_px_room_repository.dart';
import '../mock_repositories/mock_staff_evaluation_periods_repository.dart';
import '../mock_repositories/mock_taking_care_customer_repository.dart';

Future<void> testExecutable(final FutureOr<void> Function() testMain) async {
  setUpAll(() async {
    configureDependencies();
    final getMedicalServiceListUseCase = MockGetMedicalServiceListUseCase();
    final saveMedicalServiceListUseCase = MockSaveMedicalServiceListUseCase();
    final getSavedMedicalServiceListUseCase =
        MockGetSavedMedicalServiceListUseCase();
    final removeMedicalServiceListUseCase =
        MockRemoveMedicalServiceListUseCase();
    final getTemplateDetailUseCase =
        MockMedicalTemplateDetailGetMedicalTemplateListUseCase();

    final medicalServiceListBloc = MedicalServiceListBloc(
      getMedicalServiceListUseCase,
      saveMedicalServiceListUseCase,
      getSavedMedicalServiceListUseCase,
      removeMedicalServiceListUseCase,
    );

    final getMedicalTemplateListUseCase = MockGetMedicalTemplateListUseCase();
    final saveMedicalTemplateListUseCase = MockSaveMedicalTemplateListUseCase();
    final getSavedMedicalTemplateListUseCase =
        MockGetSavedMedicalTemplateListUseCase();
    final removeMedicalTemplateListUseCase =
        MockRemoveMedicalTemplateListUseCase();
    final medicalTemplateListBloc = MedicalTemplateListBloc(
      getMedicalTemplateListUseCase,
      saveMedicalTemplateListUseCase,
      getSavedMedicalTemplateListUseCase,
      removeMedicalTemplateListUseCase,
      getTemplateDetailUseCase,
    );

    final getMedicalProductCreationUseCase =
        MockGetMedicalProductCreationUseCase();
    final saveMedicalProductCreationUseCase =
        MockSaveMedicalProductCreationUseCase();
    final getSavedMedicalProductCreationUseCase =
        MockGetSavedMedicalProductCreationUseCase();
    final removeMedicalProductCreationUseCase =
        MockRemoveMedicalProductCreationUseCase();
    final productsUseCase = MockProductsMedicalProductCreationUseCase();
    final actionsUseCase = MockGetServiceAndProductActionsUseCase();

    final medicalProductCreationBloc = MedicalProductCreationBloc(
      getMedicalProductCreationUseCase,
      saveMedicalProductCreationUseCase,
      getSavedMedicalProductCreationUseCase,
      removeMedicalProductCreationUseCase,
      productsUseCase,
      actionsUseCase,
    );

    final getMedicalServiceCreationUseCase =
        MockGetMedicalServiceCreationUseCase();
    final saveMedicalServiceCreationUseCase =
        MockSaveMedicalServiceCreationUseCase();
    final getSavedMedicalServiceCreationUseCase =
        MockGetSavedMedicalServiceCreationUseCase();
    final removeMedicalServiceCreationUseCase =
        MockRemoveMedicalServiceCreationUseCase();
    final servicesUseCase = MockServicesMedicalServiceCreationUseCase();
    final methodsUseCase = MockMethodsMedicalServiceCreationUseCase();
    final getActions = MockGetServiceAndProductActionsUseCase();

    final medicalServiceCreationBloc = MedicalServiceCreationBloc(
      getMedicalServiceCreationUseCase,
      saveMedicalServiceCreationUseCase,
      getSavedMedicalServiceCreationUseCase,
      removeMedicalServiceCreationUseCase,
      servicesUseCase,
      methodsUseCase,
      getActions,
    );

    await getIt.unregister<MedicalServiceListBloc>();
    getIt.registerFactory<MedicalServiceListBloc>(() => medicalServiceListBloc);

    await getIt.unregister<MedicalTemplateListBloc>();
    getIt.registerFactory<MedicalTemplateListBloc>(
      () => medicalTemplateListBloc,
    );

    await getIt.unregister<MedicalProductCreationBloc>();
    getIt.registerFactory<MedicalProductCreationBloc>(
      () => medicalProductCreationBloc,
    );

    await getIt.unregister<MedicalServiceCreationBloc>();
    getIt.registerFactory<MedicalServiceCreationBloc>(
      () => medicalServiceCreationBloc,
    );
  });

  final getCustomerScheduleUseCase = MockGetCustomerScheduleUseCase();
  final saveCustomerScheduleUseCase = MockSaveCustomerScheduleUseCase();
  final getSavedCustomerScheduleUseCase = MockGetSavedCustomerScheduleUseCase();
  final removeCustomerScheduleUseCase = MockRemoveCustomerScheduleUseCase();
  final customerScheduleBloc = CustomerScheduleBloc(
    getCustomerScheduleUseCase,
    saveCustomerScheduleUseCase,
    getSavedCustomerScheduleUseCase,
    removeCustomerScheduleUseCase,
  );
  await getIt.unregister<CustomerScheduleBloc>();
  getIt.registerFactory<CustomerScheduleBloc>(() => customerScheduleBloc);

  final getCustomerBookingInfoUseCase = MockGetCustomerBookingInfoUseCase();
  final saveCustomerBookingInfoUseCase = MockSaveCustomerBookingInfoUseCase();
  final getSavedCustomerBookingInfoUseCase =
      MockGetSavedCustomerBookingInfoUseCase();
  final removeCustomerBookingInfoUseCase =
      MockRemoveCustomerBookingInfoUseCase();
  final bookedServicesFetchCustomerBookingInfoUseCase =
      MockBookedServicesFetchCustomerBookingInfoUseCase();
  final usedServiceFetchCustomerBookingInfoUseCase =
      MockUsedServiceFetchCustomerBookingInfoUseCase();
  final suggestServicesFetchCustomerBookingInfoUseCase =
      MockSuggestServicesFetchCustomerBookingInfoUseCase();
  final serviceDetailsLoadCustomerBookingInfoUseCase =
      MockServiceDetailsLoadCustomerBookingInfoUseCase();
  final roomList = MockGetRoomListCustomerUseCase();

  final customerBookingInfoBloc = CustomerBookingInfoBloc(
    getCustomerBookingInfoUseCase,
    saveCustomerBookingInfoUseCase,
    getSavedCustomerBookingInfoUseCase,
    removeCustomerBookingInfoUseCase,
    bookedServicesFetchCustomerBookingInfoUseCase,
    suggestServicesFetchCustomerBookingInfoUseCase,
    serviceDetailsLoadCustomerBookingInfoUseCase,
    roomList,
    usedServiceFetchCustomerBookingInfoUseCase,
  );
  await getIt.unregister<CustomerBookingInfoBloc>();
  getIt.registerFactory<CustomerBookingInfoBloc>(() => customerBookingInfoBloc);

  final getAssignTaskUseCase = MockGetAssignTaskUseCase();
  final saveAssignTaskUseCase = MockSaveAssignTaskUseCase();
  final getSavedAssignTaskUseCase = MockGetSavedAssignTaskUseCase();
  final removeAssignTaskUseCase = MockRemoveAssignTaskUseCase();
  final getStaffAssignTaskUseCase = MockGetStaffAssignTaskUseCase();
  final createAssignTaskUseCase = MockCreateAssignTaskUseCase();
  final deleteAssignTaskUseCase = MockDeleteAssignTaskUseCase();
  final updateAssignTaskUseCase = MockUpdateAssignTaskUseCase();

  final assignTaskBloc = AssignTaskBloc(
    getAssignTaskUseCase,
    saveAssignTaskUseCase,
    getSavedAssignTaskUseCase,
    removeAssignTaskUseCase,
    getStaffAssignTaskUseCase,
    createAssignTaskUseCase,
    deleteAssignTaskUseCase,
    updateAssignTaskUseCase,
  );
  await getIt.unregister<AssignTaskBloc>();
  getIt.registerFactory<AssignTaskBloc>(() => assignTaskBloc);

  final saveChatSelectBranchUseCase = MockSaveChatSelectBranchUseCase();
  final removeChatSelectBranchUseCase = MockRemoveChatSelectBranchUseCase();
  final getChatSelectBranchUseCase = MockGetChatSelectBranchUseCase();
  final chatSelectBranchBloc = ChatSelectBranchBloc(
    getChatSelectBranchUseCase,
    saveChatSelectBranchUseCase,
    removeChatSelectBranchUseCase,
  );
  await getIt.unregister<ChatSelectBranchBloc>();
  getIt.registerFactory<ChatSelectBranchBloc>(() => chatSelectBranchBloc);

  final getBranchChatListUseCase = MockGetBranchChatListUseCase();
  final saveBranchChatListUseCase = MockSaveBranchChatListUseCase();
  final getSavedBranchChatListUseCase = MockGetSavedBranchChatListUseCase();
  final removeBranchChatListUseCase = MockRemoveBranchChatListUseCase();
  final branchChatListBloc = BranchChatListBloc(
    getBranchChatListUseCase,
    saveBranchChatListUseCase,
    getSavedBranchChatListUseCase,
    removeBranchChatListUseCase,
  );
  await getIt.unregister<BranchChatListBloc>();
  getIt.registerFactory<BranchChatListBloc>(() => branchChatListBloc);

  final getPxListUseCase = MockGetPxListUseCase();
  final savePxListUseCase = MockSavePxListUseCase();
  final getSavedPxListUseCase = MockGetSavedPxListUseCase();
  final removePxListUseCase = MockRemovePxListUseCase();
  final pxListBloc = PxListBloc(
    savePxListUseCase,
    getSavedPxListUseCase,
    removePxListUseCase,
  );
  await getIt.unregister<PxListBloc>();
  getIt.registerFactory<PxListBloc>(() => pxListBloc);

  final getPxUnasignedUseCase = MockGetPxCustomerUseCase();
  final savePxUnasignedUseCase = MockSavePxUnasignedUseCase();
  final getSavedPxUnasignedUseCase = MockGetSavedPxUnasignedUseCase();
  final removePxUnasignedUseCase = MockRemovePxUnasignedUseCase();
  final pxUnasignedBloc = PxUnasignedBloc(
    getPxUnasignedUseCase,
    savePxUnasignedUseCase,
    getSavedPxUnasignedUseCase,
    removePxUnasignedUseCase,
  );
  await getIt.unregister<PxUnasignedBloc>();
  getIt.registerFactory<PxUnasignedBloc>(() => pxUnasignedBloc);
  final getPxTaskListUseCase = MockGetPxCustomerListUseCase();
  final savePxTaskListUseCase = MockSavePxTaskListUseCase();
  final getSavedPxTaskListUseCase = MockGetSavedPxTaskListUseCase();
  final removePxTaskListUseCase = MockRemovePxTaskListUseCase();
  final getSectionTakingCareCustomerUseCase =
      MockGetSectionTakingCareCustomerUseCase();
  final assignsFetchPxRecheckUseCase = MockAssignsFetchPxRecheckUseCase();
  final pxTaskListBloc = PxTaskListBloc(
    getPxTaskListUseCase,
    savePxTaskListUseCase,
    getSavedPxTaskListUseCase,
    removePxTaskListUseCase,
    getSectionTakingCareCustomerUseCase,
    assignsFetchPxRecheckUseCase,
  );
  await getIt.unregister<PxTaskListBloc>();
  getIt.registerFactory<PxTaskListBloc>(() => pxTaskListBloc);

  final getPxUnasignedUpdateUseCase = MockGetPxUnasignedUpdateUseCase();
  final savePxUnasignedUpdateUseCase = MockSavePxUnasignedUpdateUseCase();
  final getSavedPxUnasignedUpdateUseCase =
      MockGetSavedPxUnasignedUpdateUseCase();
  final removePxUnasignedUpdateUseCase = MockRemovePxUnasignedUpdateUseCase();
  final worksFetchPxUnasignedUpdateUseCase =
      MockWorksFetchPxUnasignedUpdateUseCase();
  final employeesFetchPxUnasignedUpdateUseCase =
      MockEmployeesFetchPxUnasignedUpdateUseCase();
  final assignPxUnasignedUpdateUseCase = MockAssignPxUnasignedUpdateUseCase();

  final pxUnasignedUpdateBloc = PxUnasignedUpdateBloc(
    getPxUnasignedUpdateUseCase,
    savePxUnasignedUpdateUseCase,
    getSavedPxUnasignedUpdateUseCase,
    removePxUnasignedUpdateUseCase,
    worksFetchPxUnasignedUpdateUseCase,
    employeesFetchPxUnasignedUpdateUseCase,
    assignPxUnasignedUpdateUseCase,
    getSectionTakingCareCustomerUseCase,
  );
  await getIt.unregister<PxUnasignedUpdateBloc>();
  getIt.registerFactory<PxUnasignedUpdateBloc>(() => pxUnasignedUpdateBloc);

  final getTakingCareCustomerUseCase = MockGetTakingCareCustomerUseCase();
  final saveTakingCareCustomerUseCase = MockSaveTakingCareCustomerUseCase();
  final getSavedTakingCareCustomerUseCase =
      MockGetSavedTakingCareCustomerUseCase();
  final removeTakingCareCustomerUseCase = MockRemoveTakingCareCustomerUseCase();
  final finishTaskTakingCareCustomerUseCase =
      MockFinishTaskTakingCareCustomerUseCase();
  final uploadImagesTakingCareCustomerUseCase =
      MockUploadImagesTakingCareCustomerUseCase();
  final removeImageTakingCareCustomerUseCase =
      MockRemoveImageTakingCareCustomerUseCase();
  final uploadRecordTakingCareCustomerUseCase =
      MockUploadRecordTakingCareCustomerUseCase();
  final createSupportTakingCareCustomerUseCase =
      MockCreateSupportTakingCareCustomerUseCase();
  final checkEmployeeInRoomTakingCareCustomerUseCase =
      MockCheckEmployeeInRoomTakingCareCustomerUseCase();
  final botTypeLoadTakingCareCustomerUseCase =
      MockBotTypeLoadTakingCareCustomerUseCase();
  final productLoadConsultationCustomerUseCase =
      MockProductLoadConsultationCustomerUseCase();
  final getTreatmentDetailUseCase = MockGetTreatmentDetailUseCase();
  final notiBotTypeLoadTakingCareCustomerUseCase =
      MockNotiBotTypeLoadTakingCareCustomerUseCase();
  final getTreatmentPhotoTakingCareCustomerUseCase =
      MockGetTreatmentPhotoTakingCareCustomerUseCase();
  final updateServiceDetailUseCaseTakingCareCustomerUseCase =
      MockUpdateServiceDetailUseCaseTakingCareCustomerUseCase();

  final takingCareCustomerBloc = TakingCareCustomerBloc(
    getTakingCareCustomerUseCase,
    saveTakingCareCustomerUseCase,
    getSavedTakingCareCustomerUseCase,
    removeTakingCareCustomerUseCase,
    finishTaskTakingCareCustomerUseCase,
    uploadImagesTakingCareCustomerUseCase,
    removeImageTakingCareCustomerUseCase,
    uploadRecordTakingCareCustomerUseCase,
    createSupportTakingCareCustomerUseCase,
    checkEmployeeInRoomTakingCareCustomerUseCase,
    botTypeLoadTakingCareCustomerUseCase,
    productLoadConsultationCustomerUseCase,
    getTreatmentDetailUseCase,
    notiBotTypeLoadTakingCareCustomerUseCase,
    getTreatmentPhotoTakingCareCustomerUseCase,
    updateServiceDetailUseCaseTakingCareCustomerUseCase,
  );
  await getIt.unregister<TakingCareCustomerBloc>();
  getIt.registerFactory<TakingCareCustomerBloc>(() => takingCareCustomerBloc);

  final getPxRecheckUseCase = MockGetPxCustomerUseCase();
  final savePxRecheckUseCase = MockSavePxUnasignedUseCase();
  final getSavedPxRecheckUseCase = MockGetSavedPxUnasignedUseCase();
  final removePxRecheckUseCase = MockRemovePxUnasignedUseCase();
  final noteFinishPxRecheckUseCase = MockNoteFinishPxRecheckUseCase();
  final workStatusUpdatePxRecheckUseCase =
      MockWorkStatusUpdatePxRecheckUseCase();
  final assignPxRecheckUpdateUseCase = MockAssignPxRecheckUpdateUseCase();
  final pxRecheckBloc = PxRecheckBloc(
    getPxRecheckUseCase,
    savePxRecheckUseCase,
    getSavedPxRecheckUseCase,
    removePxRecheckUseCase,
    assignsFetchPxRecheckUseCase,
    noteFinishPxRecheckUseCase,
    employeesFetchPxUnasignedUpdateUseCase,
    assignPxRecheckUpdateUseCase,
    workStatusUpdatePxRecheckUseCase,
  );
  await getIt.unregister<PxRecheckBloc>();
  getIt.registerFactory<PxRecheckBloc>(() => pxRecheckBloc);

  final getCreateCustomerUseCase = MockGetCreateCustomerUseCase();
  final saveCreateCustomerUseCase = MockSaveCreateCustomerUseCase();
  final getSavedCreateCustomerUseCase = MockGetSavedCreateCustomerUseCase();
  final removeCreateCustomerUseCase = MockRemoveCreateCustomerUseCase();
  final getProvinceCreateCustomerUseCase =
      MockGetProvinceCreateCustomerUseCase();
  final getDistrictCreateCustomerUseCase =
      MockGetDistrictCreateCustomerUseCase();
  final getWardCreateCustomerUseCase = MockGetWardCreateCustomerUseCase();
  final getJobCreateCustomerUseCase = MockGetJobCreateCustomerUseCase();
  final updateCreateCustomerUseCase = MockUpdateCreateCustomerUseCase();
  final surveyLoadCreateCustomerUseCase = MockSurveyLoadCreateCustomerUseCase();
  final customerSearchCreateCustomerUseCase =
      MockCustomerSearchCreateCustomerUseCase();

  final createCustomerBloc = CreateCustomerBloc(
    getCreateCustomerUseCase,
    saveCreateCustomerUseCase,
    getSavedCreateCustomerUseCase,
    removeCreateCustomerUseCase,
    getProvinceCreateCustomerUseCase,
    getDistrictCreateCustomerUseCase,
    getWardCreateCustomerUseCase,
    getJobCreateCustomerUseCase,
    getChatSelectBranchUseCase,
    updateCreateCustomerUseCase,
    surveyLoadCreateCustomerUseCase,
    customerSearchCreateCustomerUseCase,
  );
  await getIt.unregister<CreateCustomerBloc>();
  getIt.registerFactory<CreateCustomerBloc>(() => createCustomerBloc);

  final saveSelectPxRoomUseCase = MockSaveSelectPxRoomUseCase();
  final removeSelectPxRoomUseCase = MockRemoveSelectPxRoomUseCase();
  final getRoomListCustomerUseCase = MockGetRoomListCustomerUseCase();
  final mockGetCustomerRoomCodeUseCase = MockSaveCustomerRoomCodeUseCase();
  final roomChangeSelectPxRoomUseCase = MockRoomChangeSelectPxRoomUseCase();
  final selectPxRoomBloc = SelectPxRoomBloc(
    getRoomListCustomerUseCase,
    saveSelectPxRoomUseCase,
    removeSelectPxRoomUseCase,
    mockGetCustomerRoomCodeUseCase,
    roomChangeSelectPxRoomUseCase,
  );
  await getIt.unregister<SelectPxRoomBloc>();
  getIt.registerFactory<SelectPxRoomBloc>(() => selectPxRoomBloc);

  final getCustomerListUseCase = MockGetCustomerListUseCase();
  final getCustomerRelationShipListUseCase =
      MockGetCustomerRelationShipListUseCase();
  final saveCustomerListUseCase = MockSaveCustomerListUseCase();
  final getSavedCustomerListUseCase = MockGetSavedCustomerListUseCase();
  final removeCustomerListUseCase = MockRemoveCustomerListUseCase();
  final customerListBloc = CustomerListBloc(
    getCustomerListUseCase,
    saveCustomerListUseCase,
    getSavedCustomerListUseCase,
    removeCustomerListUseCase,
    getCustomerRelationShipListUseCase,
  );
  await getIt.unregister<CustomerListBloc>();
  getIt.registerFactory<CustomerListBloc>(() => customerListBloc);

  final getConsultationManagerUseCase = MockGetConsultationManagerUseCase();
  final saveConsultationManagerUseCase = MockSaveConsultationManagerUseCase();
  final getSavedConsultationManagerUseCase =
      MockGetSavedConsultationManagerUseCase();
  final removeConsultationManagerUseCase =
      MockRemoveConsultationManagerUseCase();
  final listFetchConsultationManagerUseCase =
      MockGetCustomerConsultationManagerUseCase();
  final bedFetchConsultationManagerUseCase =
      MockBedFetchConsultationManagerUseCase();
  final bedAssignConsultationManagerUseCase =
      MockBedAssignConsultationManagerUseCase();
  final mockEmployeesFetchPxUnasignedUpdateUseCase =
      MockEmployeesFetchPxUnasignedUpdateUseCase();
  final getUserUseCase = MockCacheUserUseCase();
  final listFetchByStaffConsultationManagerUseCase =
      MockListFetchByStaffConsultationManagerUseCase();
  final assignUpdateUseCase = MockAssignUpdateUseCase();
  final deleteServiceAssignUseCase = MockDeleteServiceAssignUseCase();
  final deleteServiceCustomerUseCase = MockDeleteServiceCustomerUseCase();
  final consultationManagerBloc = ConsultationManagerBloc(
    getConsultationManagerUseCase,
    getUserUseCase,
    getPxListUseCase,
    saveConsultationManagerUseCase,
    getSavedConsultationManagerUseCase,
    removeConsultationManagerUseCase,
    listFetchConsultationManagerUseCase,
    getRoomListCustomerUseCase,
    roomChangeSelectPxRoomUseCase,
    bedFetchConsultationManagerUseCase,
    bedAssignConsultationManagerUseCase,
    mockEmployeesFetchPxUnasignedUpdateUseCase,
    assignUpdateUseCase,
    listFetchByStaffConsultationManagerUseCase,
    deleteServiceAssignUseCase,
    deleteServiceCustomerUseCase,
  );
  await getIt.unregister<ConsultationManagerBloc>();
  getIt.registerFactory<ConsultationManagerBloc>(() => consultationManagerBloc);

  final getConsultationCustomerUseCase = MockGetConsultationCustomerUseCase();
  final saveConsultationCustomerUseCase = MockSaveConsultationCustomerUseCase();
  final getSavedConsultationCustomerUseCase =
      MockGetSavedConsultationCustomerUseCase();
  final removeConsultationCustomerUseCase =
      MockRemoveConsultationCustomerUseCase();
  final getServiceConsultationCustomerUseCase =
      MockGetServiceConsultationCustomerUseCase();
  final getActionConsultationCustomerUseCase =
      MockGetActionConsultationCustomerUseCase();
  final completeConsultationCustomerUseCase =
      MockCompleteConsultationCustomerUseCase();
  final getTreatMentOMUsecase =
      MockCreateTreatmentOMDetailsTakingCareCustomerUseCase();
  final getRoomList = MockGetRoomListUseCase();
  final getCustommerInfo = MockGetFitCustomerInfoUseCase();
  final updateCustommerInfo = MockUpdateFitCustomerInfoUseCase();
  final getResultOfFit = MockGetResultOfFitUseCase();
  final serviceInsideTicket = MockGetServiceInsideTicketUseCase();
  final getskinCustommerInfo = MockGetSkinCustomerInfoUseCase();
  final updateskinCustommerInfo = MockUpdateSkinCustomerInfoUseCase();
  final editServiceConsultationCustomerUseCase =
      MockEditServiceConsultationCustomerUseCase();
  final removeServiceConsultationCustomerUseCase =
      MockRemoveServiceConsultationCustomerUseCase();
  final getConsultationTTBDUseCase = MockGetConsultationTTBDUseCase();
  final getConsultationNDTVUseCase = MockGetConsultationNDTVUseCase();

  final consultationCustomerBloc = ConsultationCustomerBloc(
    getConsultationCustomerUseCase,
    saveConsultationCustomerUseCase,
    getSavedConsultationCustomerUseCase,
    removeConsultationCustomerUseCase,
    getServiceConsultationCustomerUseCase,
    getActionConsultationCustomerUseCase,
    completeConsultationCustomerUseCase,
    productLoadConsultationCustomerUseCase,
    getTreatMentOMUsecase,
    getRoomList,
    getCustommerInfo,
    updateCustommerInfo,
    getResultOfFit,
    serviceInsideTicket,
    updateskinCustommerInfo,
    getskinCustommerInfo,
    removeServiceConsultationCustomerUseCase,
    editServiceConsultationCustomerUseCase,
    getConsultationTTBDUseCase,
    getConsultationNDTVUseCase,
  );
  await getIt.unregister<ConsultationCustomerBloc>();
  getIt.registerFactory<ConsultationCustomerBloc>(
    () => consultationCustomerBloc,
  );

  final getStaffEvaluationPeriodsUseCase =
      MockGetStaffEvaluationPeriodsUseCase();
  final saveStaffEvaluationPeriodsUseCase =
      MockSaveStaffEvaluationPeriodsUseCase();
  final getSavedStaffEvaluationPeriodsUseCase =
      MockGetSavedStaffEvaluationPeriodsUseCase();
  final removeStaffEvaluationPeriodsUseCase =
      MockRemoveStaffEvaluationPeriodsUseCase();
  final staffEvaluationPeriodsBloc = StaffEvaluationPeriodsBloc(
    getStaffEvaluationPeriodsUseCase,
    saveStaffEvaluationPeriodsUseCase,
    getSavedStaffEvaluationPeriodsUseCase,
    removeStaffEvaluationPeriodsUseCase,
  );
  await getIt.unregister<StaffEvaluationPeriodsBloc>();
  getIt.registerFactory<StaffEvaluationPeriodsBloc>(
    () => staffEvaluationPeriodsBloc,
  );

  final getDetailStaffEvaluationPeriodUseCase =
      MockGetDetailStaffEvaluationPeriodUseCase();
  final saveDetailStaffEvaluationPeriodUseCase =
      MockSaveDetailStaffEvaluationPeriodUseCase();
  final getSavedDetailStaffEvaluationPeriodUseCase =
      MockGetSavedDetailStaffEvaluationPeriodUseCase();
  final removeDetailStaffEvaluationPeriodUseCase =
      MockRemoveDetailStaffEvaluationPeriodUseCase();
  final employeeFetchDetailStaffEvaluationPeriodUseCase =
      MockEmployeeFetchDetailStaffEvaluationPeriodUseCase();
  final detailStaffEvaluationPeriodBloc = DetailStaffEvaluationPeriodBloc(
    getDetailStaffEvaluationPeriodUseCase,
    saveDetailStaffEvaluationPeriodUseCase,
    getSavedDetailStaffEvaluationPeriodUseCase,
    removeDetailStaffEvaluationPeriodUseCase,
    employeeFetchDetailStaffEvaluationPeriodUseCase,
  );
  await getIt.unregister<DetailStaffEvaluationPeriodBloc>();
  getIt.registerFactory<DetailStaffEvaluationPeriodBloc>(
    () => detailStaffEvaluationPeriodBloc,
  );

  final getDetailCrmCustomerUseCase = MockGetDetailCrmCustomerUseCase();
  final saveDetailCrmCustomerUseCase = MockSaveDetailCrmCustomerUseCase();
  final getSavedDetailCrmCustomerUseCase =
      MockGetSavedDetailCrmCustomerUseCase();
  final removeDetailCrmCustomerUseCase = MockRemoveDetailCrmCustomerUseCase();
  final adviceFetchDetailCrmCustomerUseCase =
      MockAdviceFetchDetailCrmCustomerUseCase();
  final serviceFetchDetailCrmCustomerUseCase =
      MockServiceFetchDetailCrmCustomerUseCase();
  final callLogFetchDetailCrmCustomerUseCase =
      MockCallLogFetchDetailCrmCustomerUseCase();
  final messageLogFetchDetailCrmCustomerUseCase =
      MockMessageLogFetchDetailCrmCustomerUseCase();
  final bookingLogFetchDetailCrmCustomerUseCase =
      MockBookingLogFetchDetailCrmCustomerUseCase();
  final adviceTypeFetchDetailCrmCustomerUseCase =
      MockAdviceTypeFetchDetailCrmCustomerUseCase();
  final adviceUpdateDetailCrmCustomerUseCase =
      MockAdviceUpdateDetailCrmCustomerUseCase();
  final branchLoadDetailCrmCustomerUseCase =
      MockBranchLoadDetailCrmCustomerUseCase();
  final roomLoadDetailCrmCustomerUseCase =
      MockRoomLoadDetailCrmCustomerUseCase();
  final timeLoadDetailCrmCustomerUseCase =
      MockTimeLoadDetailCrmCustomerUseCase();
  final serviceLoadDetailCrmCustomerUseCase =
      MockServiceLoadDetailCrmCustomerUseCase();
  final promotionLoadDetailCrmCustomerUseCase =
      MockPromotionLoadDetailCrmCustomerUseCase();

  final bookingDetailLoadDetailCrmCustomerUseCase =
      MockBookingDetailLoadDetailCrmCustomerUseCase();
  final numberBookingLoadDetailCrmCustomerUseCase =
      MockNumberBookingLoadDetailCrmCustomerUseCase();
  final bookDetailCrmCustomerUseCase = MockBookDetailCrmCustomerUseCase();
  final bookingLoadDetailCrmCustomerUseCase =
      MockBookingLoadDetailCrmCustomerUseCase();

  final detailCrmCustomerBloc = DetailCrmCustomerBloc(
    getUserUseCase,
    getDetailCrmCustomerUseCase,
    saveDetailCrmCustomerUseCase,
    getSavedDetailCrmCustomerUseCase,
    removeDetailCrmCustomerUseCase,
    adviceFetchDetailCrmCustomerUseCase,
    serviceFetchDetailCrmCustomerUseCase,
    callLogFetchDetailCrmCustomerUseCase,
    messageLogFetchDetailCrmCustomerUseCase,
    bookingLogFetchDetailCrmCustomerUseCase,
    adviceTypeFetchDetailCrmCustomerUseCase,
    adviceUpdateDetailCrmCustomerUseCase,
    branchLoadDetailCrmCustomerUseCase,
    promotionLoadDetailCrmCustomerUseCase,
    roomLoadDetailCrmCustomerUseCase,
    timeLoadDetailCrmCustomerUseCase,
    serviceLoadDetailCrmCustomerUseCase,
    numberBookingLoadDetailCrmCustomerUseCase,
    bookingDetailLoadDetailCrmCustomerUseCase,
    bookDetailCrmCustomerUseCase,
    bookingLoadDetailCrmCustomerUseCase,
  );
  await getIt.unregister<DetailCrmCustomerBloc>();
  getIt.registerFactory<DetailCrmCustomerBloc>(() => detailCrmCustomerBloc);

  final getCreateChatFolderUseCase = MockGetCreateChatFolderUseCase();
  final saveCreateChatFolderUseCase = MockSaveCreateChatFolderUseCase();
  final getSavedCreateChatFolderUseCase = MockGetSavedCreateChatFolderUseCase();
  final removeCreateChatFolderUseCase = MockRemoveCreateChatFolderUseCase();
  final loadCreateChatFolderUseCase = MockLoadCreateChatFolderUseCase();
  final removeFolderCreateChatFolderUseCase =
      MockRemoveFolderCreateChatFolderUseCase();
  final conversationLoadCreateChatFolderUseCase =
      MockConversationLoadCreateChatFolderUseCase();
  final updateCreateChatFolderUseCase = MockUpdateCreateChatFolderUseCase();

  final createChatFolderBloc = CreateChatFolderBloc(
    getCreateChatFolderUseCase,
    saveCreateChatFolderUseCase,
    getSavedCreateChatFolderUseCase,
    removeCreateChatFolderUseCase,
    loadCreateChatFolderUseCase,
    removeFolderCreateChatFolderUseCase,
    conversationLoadCreateChatFolderUseCase,
    updateCreateChatFolderUseCase,
  );
  await getIt.unregister<CreateChatFolderBloc>();
  getIt.registerFactory<CreateChatFolderBloc>(() => createChatFolderBloc);

  await testMain();
}

final stringeeTokenFetchUserUseCase = MockStringeeTokenFetchUserUseCase();

Future<void> testHROrganization(
  final FutureOr<void> Function() testMain,
) async {
  setUpAll(() async {
    configureDependencies();
    final getHrOrganizationUseCase = MockGetHrOrganizationUseCase();
    final saveHrOrganizationUseCase = MockSaveHrOrganizationUseCase();
    final getSavedHrOrganizationUseCase = MockGetSavedHrOrganizationUseCase();
    final removeHrOrganizationUseCase = MockRemoveHrOrganizationUseCase();
    final hrOrganizationBloc = HrOrganizationBloc(
      getHrOrganizationUseCase,
      saveHrOrganizationUseCase,
      getSavedHrOrganizationUseCase,
      removeHrOrganizationUseCase,
    );
    await getIt.unregister<HrOrganizationBloc>();
    getIt.registerFactory<HrOrganizationBloc>(() => hrOrganizationBloc);
  });

  final getCustomerRecordUseCase = MockGetCustomerRecordUseCase();
  final saveCustomerRecordUseCase = MockSaveCustomerRecordUseCase();
  final getSavedCustomerRecordUseCase = MockGetSavedCustomerRecordUseCase();
  final removeCustomerRecordUseCase = MockRemoveCustomerRecordUseCase();
  final customerRecordBloc = CustomerRecordBloc(
    getCustomerRecordUseCase,
    saveCustomerRecordUseCase,
    getSavedCustomerRecordUseCase,
    removeCustomerRecordUseCase,
  );
  await getIt.unregister<CustomerRecordBloc>();
  getIt.registerFactory<CustomerRecordBloc>(() => customerRecordBloc);

  final getCheckinPhotoUseCase = MockGetCheckinPhotoUseCase();
  final saveCheckinPhotoUseCase = MockSaveCheckinPhotoUseCase();
  final getSavedCheckinPhotoUseCase = MockGetSavedCheckinPhotoUseCase();
  final removeCheckinPhotoUseCase = MockRemoveCheckinPhotoUseCase();
  final checkinPhotoBloc = CheckinPhotoBloc(
    getCheckinPhotoUseCase,
    saveCheckinPhotoUseCase,
    getSavedCheckinPhotoUseCase,
    removeCheckinPhotoUseCase,
  );
  await getIt.unregister<CheckinPhotoBloc>();
  getIt.registerFactory<CheckinPhotoBloc>(() => checkinPhotoBloc);
  final getDevUseCase = MockGetDevUseCase();
  final saveDevUseCase = MockSaveDevUseCase();
  final getSavedDevUseCase = MockGetSavedDevUseCase();
  final removeDevUseCase = MockRemoveDevUseCase();
  final miniAppDevUseCase = MockMiniAppDevUseCase();
  final devBloc = DevBloc(
    getDevUseCase,
    saveDevUseCase,
    getSavedDevUseCase,
    removeDevUseCase,
    miniAppDevUseCase,
  );
  await getIt.unregister<DevBloc>();
  getIt.registerFactory<DevBloc>(() => devBloc);
  await testMain();
}
