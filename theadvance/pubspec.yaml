name: theadvance
description: <PERSON><PERSON> Dung Mobile Application.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.7.3+520
publish_to: none
environment:
  sdk: ">=3.8.0 <4.0.0"

dependencies:
  animated_digit: ^3.2.3
  animated_loading_border: ^0.0.2
  animations: ^2.0.2
  app_badge_plus: ^1.2.3
  app_config:
    path: packages/app_config
  app_links: ^6.4.0
  app_tracking_transparency: ^2.0.2+4
  app_ui:
    path: packages/app_ui
  audio_session: ^0.1.23
  audio_video_progress_bar: ^2.0.3
  audio_waveforms: ^1.2.0
  audioplayers: ^6.4.0
  auto_mappr: ^2.5.0
  auto_mappr_annotation: ^2.2.0
  auto_route: ^9.2.2
  auto_size_text: ^3.0.0
  badges: ^3.1.2
  barcode: ^2.2.8
  cached_network_image: ^3.4.1
  camera: ^0.11.2
  chewie: ^1.7.5
  collection: ^1.17.2
  connectivity_plus: ^6.1.5
  crypto: ^3.0.1
  curl_logger_dio_interceptor: ^1.0.0
  dart_mappable: ^4.2.2
  detectable_text_field: ^3.0.2
  device_info_plus: ^10.0.1
  device_preview: ^1.2.0
  diacritic: ^0.1.3
  dismissible_page: ^1.0.2
  dotted_border: ^2.0.0+3
  draggable_widget: ^2.0.0
  dropdown_button2: ^2.3.9
  emoji_picker_flutter: ^4.3.0
  expandable: ^5.0.1
  extended_image: ^9.0.7
  ez_alert:
    path: packages/ez_alert
  ez_calling:
    path: packages/ez_calling
  ez_core:
    path: packages/ez_core
  ez_feedback:
    path: packages/ez_feedback
  ez_intl:
    path: packages/ez_intl
  ez_login_authentication:
    path: packages/ez_login_authentication
  ez_permission:
    path: packages/ez_permission
  ez_qr_code:
    path: packages/ez_qr_code
  ez_resources:
    path: packages/ez_resources
  ffmpeg_kit_flutter:
    git:
      url: https://github.com/minhtritc97/ffmpeg-kit.git
      path: flutter/flutter
      ref: flutter_fix_retired_v6.0.3
  file_picker: ^8.1.6
  file_saver: ^0.2.13
  firebase_core: ^2.23.0
  firebase_crashlytics: ^3.4.5
  firebase_messaging: ^14.7.5
  flick_video_player: ^0.9.0
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.2
  flutter_background: ^1.3.0+1
  flutter_bluetooth_printer: ^2.16.5
  flutter_callkit_incoming: ^2.5.5
  flutter_device_imei: ^0.0.2
  flutter_downloader: ^1.11.5
  flutter_file_downloader: ^2.0.0
  flutter_html: ^3.0.0
  flutter_html_iframe: ^3.0.0
  flutter_image_clipboard:
    git: https://github.com/minhtritc97/flutter_image_clipboard.git
  flutter_image_compress: ^2.3.0
  flutter_image_stack: ^0.0.7
  flutter_link_previewer: ^3.2.2
  flutter_local_notifications: ^17.1.2
  flutter_localizations:
    sdk: flutter
  flutter_multi_formatter: ^2.12.0
  flutter_quill_delta_from_html: ^1.5.0
  flutter_rating_bar: ^4.0.1
  flutter_screen_recording:
    git:
      url: https://github.com/minhtritc97/flutter_screen_recording.git
      path: flutter_screen_recording
  flutter_slidable: ^3.1.0
  flutter_spinkit: ^5.1.0
  flutter_staggered_animations: ^1.1.1
  flutter_timer_countdown: ^1.0.7
  flutter_timezone: ^4.1.0
  flutter_typeahead:
    path: packages/flutter_typeahead
  custom_image_crop:
    path: packages/custom_image_crop
  flutter_widget_from_html: ^0.17.0
  freezed_annotation: ^2.2.0
  full_screen_image: ^2.0.0
  get_it: ^7.2.0
  glass: ^2.0.0+2
  google_fonts: ^6.2.1
  hive: ^2.0.5
  hive_flutter: ^1.1.0
  home_widget: ^0.8.0
  html: ^0.15.4
  html_editor_enhanced: ^2.6.0
  image: ^4.5.2
  image_cropper: ^9.0.0
  image_picker: ^1.1.2
  image_size_getter: ^2.1.3
  infinite_scroll_pagination: ^4.0.0
  injectable: 2.4.0
  json_annotation: ^4.7.0
  linkfy_text:
    git: https://github.com/minhtritc97/linkfy_text.git
  livelyness_detection:
    git:
      url: https://github.com/minhtritc97/livelyness_detection.git
      ref: main
  loading_animation_widget: ^1.3.0
  measure_size: ^4.0.0
  media_cache_manager: ^0.3.0
  mime: ^2.0.0
  mime_type: ^1.0.0
  native_shared_preferences:
    git: https://github.com/minhtritc97/native_shared_preferences.git
  network_info_plus: ^5.0.2
  ogg_caf_converter: ^0.1.4
  open_filex: ^4.7.0
  package_info_plus: ^8.0.2
  pasteboard: ^0.3.0
  path: ^1.9.0
  path_provider: ^2.0.5
  path_to_regexp: ^0.4.0
  pdfrx: ^1.2.7
  persistent_bottom_nav_bar_v2: ^6.0.1
  photo_manager: ^3.6.3
  photo_manager_image_provider: ^2.1.1
  pin_code_fields: ^8.0.1
  pin_input_text_field: ^4.5.0
  pro_image_editor: ^11.1.2
  quick_actions: ^1.0.8
  quill_html_editor:
    git: https://github.com/minhtritc97/quill_html_editor.git
  readmore: ^3.0.0
  record: ^5.2.1
  reorderables: ^0.6.0
  restart_app: ^1.3.2
  rxdart: ^0.28.0
  saver_gallery: ^4.0.1
  screenshot: ^3.0.0
  scroll_to_index: ^3.0.1
  shimmer: ^3.0.0
  shorebird_code_push: ^1.1.4
  sign_in_with_apple: ^6.0.0
  signature: ^6.0.0
  simple_html_css: ^5.0.0
  socket_io_client: ^2.0.3+1
  stringee_flutter_plugin:
    git: https://github.com/minhtritc97/stringee_flutter_plugin.git
  substring_highlight: ^1.0.33
  table_calendar: ^3.0.9
  tiengviet: ^1.0.0
  timeago: ^3.1.0
  timelines_plus: ^1.0.3
  universal_html: ^2.0.8
  video_compress: ^3.1.3
  video_player: ^2.10.0
  video_thumbnail: ^0.5.6
  visibility_detector: ^0.4.0+2
  vpn_connection_detector: ^1.0.7
  vsc_quill_delta_to_html: ^1.0.5
  webview_flutter: ^4.10.0
  webview_flutter_android: ^4.2.0
  webview_flutter_web: ^0.2.3+4
  webview_flutter_wkwebview: ^3.16.3
  youtube_player_iframe: ^5.0.0

dev_dependencies:
  auto_route_generator: ^9.0.0
  build_runner: ^2.4.13
  dart_mappable_builder: ^4.2.3
  dependency_validator: ^3.2.3
  floor_generator: ^1.2.0
  flutter_launcher_icons: ^0.14.4
  flutter_native_splash: ^2.4.6
  flutter_test:
    sdk: flutter
  freezed: ^2.3.5
  hive_generator: ^2.0.0
  import_sorter: ^4.6.0
  injectable_generator: ^2.6.1
  json_serializable:
    git: https://github.com/minhtritc97/flexible_json_serializable.git
  mocktail: ^1.0.0
  retrofit_generator: ^8.1.0
  cached_video_player_plus: ^4.0.3
# config flavor in ios, android
flavorizr:
  app:
    android:
      flavorDimensions: "enviroment"
    ios: null

  flavors:
    dev:
      app:
        name: "The Advance - dev"

      android:
        applicationId: "com.ngocdung.theadvance"
        firebase:
          config: "android/app/google-services.json"

      ios:
        bundleId: "com.ngocdung.theadvance"
        firebase:
          config: "ios/Runner/GoogleService-Info.plist"

    prod:
      app:
        name: "The Advance"

      android:
        applicationId: "com.ngocdung.theadvance"
        firebase:
          config: "android/app/google-services.json"
      ios:
        bundleId: "com.ngocdung.theadvance"
        firebase:
          config: "ios/Runner/GoogleService-Info.plist"

    acc:
      app:
        name: "Ngọc Dung Internal"

      android:
        applicationId: "com.thammyvienngocdung.acc"
        firebase:
          config: "android/app/google-services-acc.json"
      ios:
        bundleId: "com.thammyvienngocdung.acc"
        firebase:
          config: "ios/Runner/GoogleService-Info-Acc.plist"

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  assets:
    - assets/fonts/
    - assets/mockup/
    - assets/audio/
    - assets/emoji/
    - assets/stickers/
    - shorebird.yaml

  uses-material-design: true
