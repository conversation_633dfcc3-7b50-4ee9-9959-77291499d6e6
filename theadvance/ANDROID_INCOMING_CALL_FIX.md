# Android Incoming Call Fix - App Killed State

## Problem
When the Android app is completely killed (not just backgrounded), incoming calls are not received because:
1. **No Active Stringee Connection**: The Stringee client connection is lost when app is killed
2. **Firebase Push Only**: Only Firebase push notifications are received, but they don't automatically establish Stringee connection
3. **Connection Recovery Failure**: The app fails to quickly reconnect to <PERSON><PERSON> to receive the actual call

## Root Cause Analysis
1. **App Lifecycle**: When Android kills the app, all connections and services are terminated
2. **Stringee Dependency**: Incoming calls require an active Stringee WebSocket connection
3. **Timing Issues**: Firebase push → App wake → Stringee connect → Call receive has timing gaps
4. **Background Restrictions**: Android limits background processing for killed apps

## Solution Overview
Implemented a multi-layered approach to handle incoming calls when app is killed:

### 1. Enhanced Firebase Message Handler
- **Call Data Parsing**: Extract call details from Firebase payload
- **Persistent Storage**: Store call info for recovery if connection fails
- **Service Activation**: Start foreground service to maintain connection
- **Improved Logging**: Better debugging and error tracking

### 2. StringeeCallService (New)
- **Foreground Service**: Maintains connection when app is killed
- **Auto-Timeout**: Automatically stops after 60 seconds if no call received
- **Notification**: Shows "Preparing for incoming call" notification
- **Connection Management**: Handles Stringee connection lifecycle

### 3. Enhanced Connection Manager
- **Call Recovery**: Check for pending calls on app restart
- **Service Integration**: Coordinate with StringeeCallService
- **Better Error Handling**: Improved reconnection logic
- **State Management**: Track call states across app lifecycle

## Technical Implementation

### Firebase Message Handler Enhancement
```kotlin
private fun messageHandler(msgData: Map<String, String>?) {
    val payload = msgData?.get("payload")
    if (payload != null) {
        // Parse call data
        val callData = JSONObject(payload)
        val callId = callData.optString("callId", "")
        
        // Store for recovery
        storeCallData(callId, from, to, isVideoCall)
        
        // Start service to maintain connection
        StringeeCallService.startService(applicationContext, callId)
        
        // Connect to Stringee
        StringeeUtils.connectStringee(applicationContext, onReceiveBg = true)
    }
}
```

### StringeeCallService
```kotlin
class StringeeCallService : Service() {
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Start foreground service
        startForeground(NOTIFICATION_ID, createNotification())
        
        // Initialize and connect to Stringee
        StringeeConnectionManager.initialize(applicationContext)
        StringeeUtils.connectStringee(applicationContext, onReceiveBg = true)
        
        // Auto-stop after timeout
        setupAutoStopTimer()
        
        return START_NOT_STICKY
    }
}
```

### Call Recovery System
```kotlin
fun checkForPendingCall(context: Context) {
    val pendingCallId = getPendingCallId()
    val pendingCallTime = getPendingCallTime()
    
    // If call is recent (< 30 seconds), try to recover
    if (pendingCallId.isNotEmpty() && isRecent(pendingCallTime)) {
        connectStringeeWithRetry(context, onReceiveBg = true)
    }
}
```

## Key Features

### 1. Foreground Service Protection
- **System Priority**: Foreground service prevents Android from killing the process
- **User Notification**: Shows notification explaining connection preparation
- **Auto-Cleanup**: Service stops automatically when no longer needed

### 2. Call Data Persistence
- **SharedPreferences Storage**: Stores call details for recovery
- **Timestamp Tracking**: Prevents stale call recovery attempts
- **Automatic Cleanup**: Clears data when call is received or expires

### 3. Multi-Layer Fallback
1. **Primary**: Direct Stringee connection receives call
2. **Secondary**: Service maintains connection if app is killed
3. **Tertiary**: Call recovery on app restart
4. **Fallback**: CallKit notification if all else fails

### 4. Enhanced Error Handling
- **Connection Timeouts**: Proper timeout handling for failed connections
- **Service Lifecycle**: Proper service start/stop management
- **State Synchronization**: Keep call state consistent across components

## Files Modified

### Core Components
- `MyFirebaseMessagingService.kt` - Enhanced push notification handling
- `StringeeConnectionManager.kt` - Improved connection lifecycle management
- `StringeeCallService.kt` - New foreground service for call handling
- `StringeeUtils.kt` - Enhanced background call handling
- `EzCallingPlugin.kt` - Added pending call recovery on app start

### Configuration
- `AndroidManifest.xml` - Added StringeeCallService declaration
- Added foreground service permissions and configuration

## Testing Scenarios

### 1. App Killed State
1. Force kill the app completely
2. Send incoming call via Firebase
3. Verify service starts and maintains connection
4. Verify call is received and UI appears

### 2. Background State
1. Put app in background
2. Send incoming call
3. Verify normal background handling works
4. Verify service doesn't interfere

### 3. Recovery Testing
1. Kill app during incoming call process
2. Restart app within 30 seconds
3. Verify pending call recovery works

### 4. Timeout Testing
1. Send Firebase push without actual call
2. Verify service stops after 60 seconds
3. Verify no resource leaks

## Benefits
1. **Reliable Call Reception**: Incoming calls work even when app is killed
2. **Battery Efficient**: Service only runs when needed, auto-stops
3. **User Experience**: Seamless call experience regardless of app state
4. **Android Compliance**: Uses proper foreground service patterns
5. **Fallback Protection**: Multiple layers ensure call delivery

## Monitoring & Debugging
- Enhanced logging throughout the call flow
- Service lifecycle tracking
- Connection state monitoring
- Call recovery attempt logging

This solution ensures that incoming calls are reliably received on Android even when the app has been completely killed by the system.
