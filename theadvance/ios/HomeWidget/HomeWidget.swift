import WidgetKit
import <PERSON><PERSON>

private let widgetGroupId = "group.com.ngocdung.theadvance"

struct WorkTimeEntry: TimelineEntry {
    let date: Date
    let workDate: String
    let workHours: String
    let checkIn: String
    let checkOut: String
    let total: String
    let progress: Int // 0 to 100
    
}

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> WorkTimeEntry {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd/MM/yyyy"
        let dateString = dateFormatter.string(from: Date())
        return WorkTimeEntry(
            date: Date(),
            workDate: dateString,
            workHours: "0 giờ 0 phút",
            checkIn: "--:--",
            checkOut: "--:--",
            total: "0.0",
            progress: 0,
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (WorkTimeEntry) -> ()) {
        let data = UserDefaults(suiteName: widgetGroupId)

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EEEE, d MMMM"
        dateFormatter.locale = Locale(identifier: "vi_VN")

        // Convert the Date to a String using the formatter
        let dateString = dateFormatter.string(from: Date())

        // Check if it's a new day and reset data if needed
        let lastUpdateDate = data?.string(forKey: "lastUpdateDate") ?? ""
        let shouldResetData = lastUpdateDate != dateString

        let workDate = shouldResetData ? dateString : (data?.string(forKey: "workDate") ?? dateString)
        let workHours = shouldResetData ? "0 giờ 0 phút" : (data?.string(forKey: "workHours") ?? "0 giờ 0 phút")
        let checkIn = shouldResetData ? "--:--" : (data?.string(forKey: "checkIn") ?? "--:--")
        let checkOut = shouldResetData ? "--:--" : (data?.string(forKey: "checkOut") ?? "--:--")
        let total = shouldResetData ? "0.0" : (data?.string(forKey: "total") ?? "0.0")
        let progress = shouldResetData ? 0 : (data?.integer(forKey: "progress") ?? 0)

        // Update the last update date if it's a new day
        if shouldResetData {
            data?.set(dateString, forKey: "lastUpdateDate")
            data?.set(workDate, forKey: "workDate")
            data?.set(workHours, forKey: "workHours")
            data?.set(checkIn, forKey: "checkIn")
            data?.set(checkOut, forKey: "checkOut")
            data?.set(total, forKey: "total")
            data?.set(progress, forKey: "progress")
        }

        let entry = WorkTimeEntry(
            date: Date(),
            workDate: workDate,
            workHours: workHours,
            checkIn: checkIn,
            checkOut: checkOut,
            total: total,
            progress: progress
        )

        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<WorkTimeEntry>) -> ()) {
        getSnapshot(in: context) { (entry) in
            let currentDate = Date()
            let midnight = Calendar.current.startOfDay(for: currentDate.addingTimeInterval(24*60*60))

            // Create multiple timeline entries to ensure widget updates throughout the day
            var entries: [WorkTimeEntry] = [entry]

            // Add entries for every hour to ensure the widget stays updated
            for hourOffset in 1...23 {
                let futureDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate) ?? currentDate
                if futureDate < midnight {
                    let futureEntry = WorkTimeEntry(
                        date: futureDate,
                        workDate: entry.workDate,
                        workHours: entry.workHours,
                        checkIn: entry.checkIn,
                        checkOut: entry.checkOut,
                        total: entry.total,
                        progress: entry.progress
                    )
                    entries.append(futureEntry)
                }
            }

            // The timeline will refresh at midnight to reset for the new day
            let timeline = Timeline(entries: entries, policy: .after(midnight))
            completion(timeline)
        }
    }
}

struct HomeWidgetEntryView: View {
    @Environment(\.widgetFamily) var family
    @Environment(\.colorScheme) var colorScheme
    var entry: WorkTimeEntry

    var body: some View {
        switch family {
        case .systemSmall:
            HomeWidgetSmallView(entry: entry)
                .environment(\.colorScheme, colorScheme)
        default:
            HomeWidgetMediumView(entry: entry)
                .environment(\.colorScheme, colorScheme)
        }
    }
}

// Medium layout with dark mode support
struct HomeWidgetMediumView: View {
    @Environment(\.colorScheme) var colorScheme
    var entry: WorkTimeEntry

    // Adaptive colors based on color scheme
    private var backgroundGradientColors: [Color] {
        if colorScheme == .dark {
            return [
                Color(red: 0.1, green: 0.3, blue: 0.4),
                Color(red: 0.15, green: 0.4, blue: 0.5),
                Color(red: 0.2, green: 0.5, blue: 0.6)
            ]
        } else {
            return [
                Color(red: 0.25, green: 0.8, blue: 0.7),
                Color(red: 0.45, green: 0.9, blue: 0.8),
                Color(red: 0.7, green: 0.95, blue: 0.9)
            ]
        }
    }

    private var containerGradientColors: [Color] {
        if colorScheme == .dark {
            return [
                Color.gray.opacity(0.3),
                Color.gray.opacity(0.1)
            ]
        } else {
            return [
                Color.white.opacity(0.3),
                Color.white.opacity(0.1)
            ]
        }
    }

    private var primaryTextColor: Color {
        colorScheme == .dark ? .white : .black
    }

    private var secondaryTextColor: Color {
        if colorScheme == .dark {
            return Color.white.opacity(0.8)
        } else {
            return .black
        }
    }

    private var accentColor: Color {
        if colorScheme == .dark {
            return Color(red: 0.4, green: 0.7, blue: 0.4)
        } else {
            return Color(red: 0.2, green: 0.5, blue: 0.2)
        }
    }

    private var strokeColor: Color {
        if colorScheme == .dark {
            return Color.white.opacity(0.2)
        } else {
            return Color.gray.opacity(0.1)
        }
    }

    private var shadowColor: Color {
        if colorScheme == .dark {
            return Color.black.opacity(0.6)
        } else {
            return Color.black.opacity(0.3)
        }
    }

    private var dividerColor: Color {
        if colorScheme == .dark {
            return Color.white.opacity(0.3)
        } else {
            return Color.gray.opacity(0.15)
        }
    }

    var body: some View {
        // Background gradient fills entire widget
        LinearGradient(
            gradient: Gradient(colors: backgroundGradientColors),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ).opacity(0.2)
            .overlay(
                // Content container with padding inside the gradient
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: containerGradientColors),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(strokeColor, lineWidth: 1)
                    )
                    // Add shadow for main RoundedRectangle
                    .shadow(
                        color: shadowColor,
                        radius: 2,
                        x: 0,
                        y: 2
                    )
                    .padding(.all,4)
                    .overlay(
                        // Content
                        VStack(spacing: 0) {
                            // Calendar icon and date
                            VStack(spacing: 4) {
                                Image(systemName: "calendar")
                                    .font(.system(size: 14))
                                    .foregroundColor(accentColor)

                                Text(entry.workDate)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(primaryTextColor)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.8)
                            }
                            .padding(.top, 12)
                            
                            Spacer()
                            
                            // Check-in and Check-out times
                            VStack(spacing: 8) {
                                HStack {
                                    Text("Giờ vào")
                                        .font(.system(size: 13))
                                        .foregroundColor(secondaryTextColor)

                                    Spacer()

                                    Text(entry.checkIn)
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(accentColor)
                                }

                                Divider()
                                    .background(dividerColor)

                                HStack {
                                    Text("Giờ ra")
                                        .font(.system(size: 13))
                                        .foregroundColor(secondaryTextColor)

                                    Spacer()

                                    Text(entry.checkOut)
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(accentColor)
                                }
                            }
                            .padding(.horizontal, 4)
                            
                            Spacer()
                            
                            // Total hours
                            VStack(alignment: .leading, spacing: 2) {
                                HStack {
                                    Text("Tổng công")
                                        .font(.system(size: 13))
                                        .foregroundColor(secondaryTextColor)
                                    Spacer()
                                }

                                HStack(alignment: .bottom, spacing: 4) {
                                    Text("\(entry.total)")
                                        .font(.system(size: 22, weight: .medium))
                                        .foregroundColor(primaryTextColor)

                                    Text("(\(entry.workHours))")
                                        .font(.system(size: 12))
                                        .foregroundColor(secondaryTextColor.opacity(0.7))
                                        .padding(.bottom, 2)

                                    Spacer()
                                }
                                
                                // Progress bar
                                ZStack(alignment: .leading) {
                                    // Background bar
                                    RoundedRectangle(cornerRadius: 2)
                                        .fill(dividerColor.opacity(0.5))
                                        .frame(width: 130, height: 3)

                                    // Progress bar with color transition
                                    RoundedRectangle(cornerRadius: 2)
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color.orange.opacity(1.0 - (Double(entry.progress)/100)),
                                                    accentColor.opacity((Double(entry.progress)/100))
                                                ]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .frame(width: 130 * (Double(entry.progress)/100), height: 3)
                                }.padding(.horizontal, 2)
                            }
                            .padding(.horizontal, 4)
                            .padding(.bottom, 12)
                        }
                            .padding(8)
                    )
            )
            .widgetURL(URL(string: "theadvance://theadvance.com/checkin"))
        
    }
}

// Small layout (square version) with dark mode support
struct HomeWidgetSmallView: View {
    @Environment(\.colorScheme) var colorScheme
    var entry: WorkTimeEntry

    // Adaptive colors based on color scheme (same as medium view)
    private var backgroundGradientColors: [Color] {
        if colorScheme == .dark {
            return [
                Color(red: 0.1, green: 0.3, blue: 0.4),
                Color(red: 0.15, green: 0.4, blue: 0.5),
                Color(red: 0.2, green: 0.5, blue: 0.6)
            ]
        } else {
            return [
                Color(red: 0.25, green: 0.8, blue: 0.7),
                Color(red: 0.45, green: 0.9, blue: 0.8),
                Color(red: 0.7, green: 0.95, blue: 0.9)
            ]
        }
    }

    private var containerGradientColors: [Color] {
        if colorScheme == .dark {
            return [
                Color.gray.opacity(0.4),
                Color.gray.opacity(0.2)
            ]
        } else {
            return [
                Color.white.opacity(0.3),
                Color.white.opacity(0.1)
            ]
        }
    }

    private var primaryTextColor: Color {
        colorScheme == .dark ? .white : .black
    }

    private var secondaryTextColor: Color {
        if colorScheme == .dark {
            return Color.white.opacity(0.8)
        } else {
            return .black
        }
    }

    private var accentColor: Color {
        if colorScheme == .dark {
            return Color(red: 0.4, green: 0.7, blue: 0.4)
        } else {
            return Color(red: 0.2, green: 0.5, blue: 0.2)
        }
    }

    private var strokeColor: Color {
        if colorScheme == .dark {
            return Color.white.opacity(0.2)
        } else {
            return Color.gray.opacity(0.1)
        }
    }

    private var shadowColor: Color {
        if colorScheme == .dark {
            return Color.black.opacity(0.6)
        } else {
            return Color.black.opacity(0.3)
        }
    }

    private var dividerColor: Color {
        if colorScheme == .dark {
            return Color.white.opacity(0.3)
        } else {
            return Color.gray.opacity(0.15)
        }
    }

    var body: some View {
        // Background gradient fills entire widget
        LinearGradient(
            gradient: Gradient(colors: backgroundGradientColors),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ).opacity(0.2)
            .overlay(
                // Content container with padding inside the gradient
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: containerGradientColors),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(strokeColor, lineWidth: 1)
                    )
                    // Add shadow for main RoundedRectangle
                    .shadow(
                        color: shadowColor,
                        radius: 2,
                        x: 0,
                        y: 2
                    )
                    .padding(.all,4)
                    .overlay(
                        // Content
                        VStack(spacing: 0) {
                            // Calendar icon and date
                            VStack(spacing: 4) {
                                Image(systemName: "calendar")
                                    .font(.system(size: 14))
                                    .foregroundColor(accentColor)

                                Text(entry.workDate)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(primaryTextColor)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.8)
                            }
                            .padding(.top, 12)
                            
                            Spacer()
                            
                            // Check-in and Check-out times
                            VStack(spacing: 8) {
                                HStack {
                                    Text("Giờ vào")
                                        .font(.system(size: 13))
                                        .foregroundColor(secondaryTextColor)

                                    Spacer()

                                    Text(entry.checkIn)
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(accentColor)
                                }

                                Divider()
                                    .background(dividerColor)

                                HStack {
                                    Text("Giờ ra")
                                        .font(.system(size: 13))
                                        .foregroundColor(secondaryTextColor)

                                    Spacer()

                                    Text(entry.checkOut)
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(accentColor)
                                }
                            }
                            .padding(.horizontal, 4)
                            
                            Spacer()
                            
                            // Total hours
                            VStack(alignment: .leading, spacing: 2) {
                                HStack {
                                    Text("Tổng công")
                                        .font(.system(size: 13))
                                        .foregroundColor(secondaryTextColor)
                                    Spacer()
                                }

                                HStack(alignment: .bottom, spacing: 4) {
                                    Text("\(entry.total)")
                                        .font(.system(size: 22, weight: .medium))
                                        .foregroundColor(primaryTextColor)

                                    Text("(\(entry.workHours))")
                                        .font(.system(size: 12))
                                        .foregroundColor(secondaryTextColor.opacity(0.7))
                                        .padding(.bottom, 2)

                                    Spacer()
                                }
                                
                                // Progress bar
                                ZStack(alignment: .leading) {
                                    // Background bar
                                    RoundedRectangle(cornerRadius: 2)
                                        .fill(dividerColor.opacity(0.5))
                                        .frame(width: 130, height: 3)

                                    // Progress bar with color transition
                                    RoundedRectangle(cornerRadius: 2)
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color.orange.opacity(1.0 - (Double(entry.progress)/100)),
                                                    accentColor.opacity((Double(entry.progress)/100))
                                                ]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .frame(width: 130 * (Double(entry.progress)/100), height: 3)
                                }.padding(.horizontal, 2)
                            }
                            .padding(.horizontal, 4)
                            .padding(.bottom, 12)
                        }
                            .padding(8)
                    )
            )
            .widgetURL(URL(string: "theadvance://theadvance.com/checkin"))
        
    }
}


struct HomeWidget: Widget {
    let kind: String = "HomeWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            HomeWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Work Time Widget")
        .contentMarginsDisabled()
        .description("Hiển thị thông tin giờ làm việc hôm nay.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}
