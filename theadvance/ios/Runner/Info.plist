<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(BUNDLE_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>FlutterDeepLinkingEnabled</key>
		<false/>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>theadvance.com</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>theadvance</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb1636456976470040</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FacebookAppID</key>
		<string>1636456976470040</string>
		<key>FacebookDisplayName</key>
		<string>The Advance</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationCategoryType</key>
		<string></string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>https</string>
			<string>http</string>
			<string>mailto</string>
			<string>tel</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
			<key>NSAllowsArbitraryLoadsInWebContent</key>
			<true/>
			<key>NSExceptionAllowsInsecureHTTPLoads</key>
			<true/>
			<key>NSExceptionDomains</key>
			<dict>
				<key>cdn.ngocdung.net</key>
				<dict>
					<key>NSIncludesSubdomains</key>
					<true/>
					<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
					<true/>
				</dict>
				<key>googleapis.com</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true/>
					<key>NSIncludesSubdomains</key>
					<true/>
				</dict>
				<key>media.ngocdunggroup.com.vn</key>
				<dict>
					<key>NSIncludesSubdomains</key>
					<true/>
					<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
					<true/>
				</dict>
			</dict>
		</dict>
		<key>NSAppleMusicUsageDescription</key>
		<string>Vui lòng cho phép truy xuất thư viện media để gửi đi trong lúc nhắn tin hay gửi báo cáo định kỳ</string>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>Vui lòng cho phép truy xuất Bluetooth để kết nối với máy in</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>Vui lòng cho phép truy xuất Bluetooth để kết nối với máy in</string>
		<key>NSCalendarsUsageDescription</key>
		<string>Vui lòng cho phép truy xuất niên lịch đang sử dụng để việc hiển thị múi giờ chính xác</string>
		<key>NSCameraUsageDescription</key>
		<string>Vui lòng cho phép truy xuất camera để chụp hình cập nhật thông tin cá nhân</string>
		<key>NSContactsUsageDescription</key>
		<string>Lấy thông tin số điện thoại từ danh bạ để giúp việc hiển thị và chia sẻ thông tin phù hợp hơn</string>
		<key>NSFaceIDUsageDescription</key>
		<string>Vui lòng cho phép sử dụng FaceID để sử dụng chức năng đăng nhập bằng FaceID</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Vui lòng cho phép truy cập vị trí để việc cập nhật tin tức và dịch vụ được chính xác hơn</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Vui lòng cho phép truy cập vị trí để việc cập nhật tin tức và dịch vụ được chính xác hơn</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Vui lòng cho phép truy cập microphone để ghi âm</string>
		<key>NSMotionUsageDescription</key>
		<string>Vui lòng cho phép truy xuất chuyển động để việc kiểm tra vị trí chuyển động chính xác hơn</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Vui lòng cho phép truy xuất photo để chọn lấy hình ảnh cập nhật thông tin cá nhân</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Ứng dụng cần quyền truy cập vào Thư viện ảnh để lưu hình ảnh</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>Xin quyền nhận diện giọng nói để hỗ trợ cho việc ghi chú bằng giọng nói</string>
		<key>NSUserTrackingUsageDescription</key>
		<string>Với cài đặt này, The Advance có thể cung cấp cho bạn trải nghiệm quảng cáo và sử dụng nội dung tốt hơn</string>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<string>Vui lòng cho phép ứng dụng chạy nền để thực hiện cuộc gọi</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UISupportsDocumentBrowser</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
			<string>voip</string>
		</array>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>$(LAUNCH_SCREEN_STORYBOARD)</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>BASE_URL</key>
		<string>$(BASE_URL)</string>
	</dict>
</plist>
