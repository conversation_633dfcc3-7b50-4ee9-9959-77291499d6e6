package com.example.ez_calling.stringee_common

import android.content.Context
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.stringee.StringeeClient
import com.stringee.call.StringeeCall
import com.stringee.call.StringeeCall2
import com.stringee.exception.StringeeError
import com.stringee.listener.StatusListener
import com.stringee.listener.StringeeConnectionListener
import org.json.JSONObject
import java.util.Timer
import java.util.TimerTask
import kotlin.concurrent.schedule

object StringeeConnectionManager : DefaultLifecycleObserver {
    
    private var context: Context? = null
    private var isInitialized = false
    private var pendingConnection = false
    
    fun initialize(context: Context) {
        if (!isInitialized) {
            this.context = context
            ProcessLifecycleOwner.get().lifecycle.addObserver(this)
            isInitialized = true
            Log.d(Common.TAG, "StringeeConnectionManager initialized")
        }
    }
    
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        Log.d(Common.TAG, "App moved to foreground")
        Common.isAppInBackground = false
        
        // Cancel any pending reconnect attempts
        cancelReconnectTimer()
        
        // If we have a pending connection or client is disconnected, reconnect
        if (pendingConnection || (Common.client != null && !Common.client!!.isConnected)) {
            context?.let { ctx ->
                Log.d(Common.TAG, "Reconnecting Stringee on foreground")
                connectStringeeWithRetry(ctx)
            }
        }
    }
    
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Log.d(Common.TAG, "App moved to background")
        Common.isAppInBackground = true
        
        // Only disconnect if not in a call
        if (!Common.isInCall) {
            disconnectStringee()
        } else {
            Log.d(Common.TAG, "Not disconnecting - call in progress")
        }
    }
    
    fun connectStringeeWithRetry(context: Context, onConnected: () -> Unit = {}, onReceiveBg: Boolean = false) {
        // Reset reconnect attempts when explicitly connecting
        Common.reconnectAttempts = 0
        connectStringeeInternal(context, onConnected, onReceiveBg)
    }
    
    private fun connectStringeeInternal(context: Context, onConnected: () -> Unit = {}, onReceiveBg: Boolean = false) {
        val sharedPref = context.getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE)
        val stringeeToken = sharedPref.getString("loginStringee", "") ?: ""
        
        if (stringeeToken.isEmpty()) {
            Log.d(Common.TAG, "No Stringee token available")
            return
        }
        
        // Disconnect existing client if any
        Common.client?.disconnect()
        
        // Create new client
        Common.client = StringeeClient(context)
        Common.client?.setConnectionListener(object : StringeeConnectionListener {
            override fun onConnectionConnected(p0: StringeeClient?, p1: Boolean) {
                Log.d(Common.TAG, "Stringee connected successfully")
                Common.reconnectAttempts = 0
                pendingConnection = false
                cancelReconnectTimer()
                onConnected()
            }

            override fun onConnectionDisconnected(p0: StringeeClient?, p1: Boolean) {
                Log.d(Common.TAG, "Stringee disconnected")
                
                // Only attempt reconnection if app is in foreground and not in call
                if (!Common.isAppInBackground && !Common.isInCall) {
                    scheduleReconnect(context, onConnected, onReceiveBg)
                }
            }

            override fun onIncomingCall(p0: StringeeCall?) {
                Log.d(Common.TAG, "Incoming call received: ${p0?.callId}")
                Common.currentCall = p0

                if (onReceiveBg) {
                    // Handle background incoming call
                    StringeeUtils.onCallReceivedBackgroundHandler(context, Common.currentCall)

                    // Notify service that call was received
                    try {
                        val serviceIntent = android.content.Intent(context, StringeeCallService::class.java)
                        serviceIntent.action = "CALL_RECEIVED"
                        context.startService(serviceIntent)
                    } catch (e: Exception) {
                        Log.e(Common.TAG, "Error notifying service of call: ${e.message}")
                    }
                } else {
                    // Handle foreground incoming call
                    handleForegroundIncomingCall(context, p0)
                }

                // Clear any pending call data since we received the actual call
                clearPendingCallData(context)
            }

            override fun onIncomingCall2(call2: StringeeCall2?) {
                if (call2 == null) return
                
                StringeeUtils.runOnUiThread {
                    if (Common.isInCall) {
                        call2.reject(object : StatusListener() {
                            override fun onSuccess() {}
                        })
                    } else {
                        Common.calls2Map[call2.callId] = call2
                        // Handle incoming call2 - this would need proper activity handling
                        Log.d(Common.TAG, "Incoming call2 received: ${call2.callId}")
                    }
                }
            }

            override fun onConnectionError(p0: StringeeClient?, p1: StringeeError?) {
                Log.d(Common.TAG, "Stringee connection error: ${p1?.message}")
                
                // Only attempt reconnection if app is in foreground
                if (!Common.isAppInBackground) {
                    scheduleReconnect(context, onConnected, onReceiveBg)
                }
            }

            override fun onRequestNewToken(p0: StringeeClient?) {
                Log.d(Common.TAG, "Stringee requesting new token")
                StringeeUtils.fetchStringeeToken(context) { newToken ->
                    Common.client?.connect(newToken)
                }
            }

            override fun onCustomMessage(p0: String?, p1: JSONObject?) {
                Log.d(Common.TAG, "Stringee custom message received")
            }

            override fun onTopicMessage(p0: String?, p1: JSONObject?) {
                Log.d(Common.TAG, "Stringee topic message received")
            }
        })
        
        Log.d(Common.TAG, "Connecting to Stringee...")
        Common.client?.connect(stringeeToken)
        
        // Fallback check for connection
        Timer().schedule(2000) {
            if (Common.client?.isConnected == false && !Common.isAppInBackground) {
                Log.d(Common.TAG, "Connection failed, fetching new token")
                StringeeUtils.fetchStringeeToken(context) { 
                    connectStringeeInternal(context, onConnected, onReceiveBg) 
                }
            }
        }
    }
    
    private fun scheduleReconnect(context: Context, onConnected: () -> Unit = {}, onReceiveBg: Boolean = false) {
        if (Common.reconnectAttempts >= Common.maxReconnectAttempts) {
            Log.d(Common.TAG, "Max reconnect attempts reached")
            pendingConnection = true
            return
        }
        
        cancelReconnectTimer()
        
        Common.reconnectAttempts++
        val delay = Common.reconnectDelay * Common.reconnectAttempts
        
        Log.d(Common.TAG, "Scheduling reconnect attempt ${Common.reconnectAttempts} in ${delay}ms")
        
        Common.reconnectTimer = Timer()
        Common.reconnectTimer?.schedule(delay) {
            if (!Common.isAppInBackground) {
                Log.d(Common.TAG, "Attempting reconnect ${Common.reconnectAttempts}")
                connectStringeeInternal(context, onConnected, onReceiveBg)
            }
        }
    }
    
    private fun cancelReconnectTimer() {
        Common.reconnectTimer?.cancel()
        Common.reconnectTimer = null
    }
    
    private fun disconnectStringee() {
        Log.d(Common.TAG, "Disconnecting Stringee client")
        cancelReconnectTimer()
        Common.client?.disconnect()
        Common.client = null
    }
    
    fun forceDisconnect() {
        Log.d(Common.TAG, "Force disconnecting Stringee")
        Common.reconnectAttempts = Common.maxReconnectAttempts
        disconnectStringee()
    }

    private fun handleForegroundIncomingCall(context: Context, call: StringeeCall?) {
        if (call == null) return

        Log.d(Common.TAG, "Handling foreground incoming call: ${call.callId}")

        // Start the incoming call activity
        val intent = android.content.Intent(context, com.example.ez_calling.stringee_common.IncomingCallActivity::class.java)
        intent.putExtra("call_id", call.callId)
        intent.flags = android.content.Intent.FLAG_ACTIVITY_NEW_TASK or android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP

        try {
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(Common.TAG, "Error starting incoming call activity: ${e.message}")
            // Fallback to background handler
            StringeeUtils.onCallReceivedBackgroundHandler(context, call)
        }
    }

    private fun clearPendingCallData(context: Context) {
        val sharedPref = context.getSharedPreferences("StringeeCallData", android.content.Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            remove("pendingCallId")
            remove("pendingCallFrom")
            remove("pendingCallTo")
            remove("pendingCallIsVideo")
            remove("pendingCallTime")
            apply()
        }
        Log.d(Common.TAG, "Cleared pending call data")
    }

    fun checkForPendingCall(context: Context) {
        val sharedPref = context.getSharedPreferences("StringeeCallData", android.content.Context.MODE_PRIVATE)
        val pendingCallId = sharedPref.getString("pendingCallId", "")
        val pendingCallTime = sharedPref.getLong("pendingCallTime", 0)

        if (pendingCallId?.isNotEmpty() == true) {
            val currentTime = System.currentTimeMillis()
            val timeDiff = currentTime - pendingCallTime

            // If the pending call is less than 30 seconds old, try to recover it
            if (timeDiff < 30000) {
                Log.d(Common.TAG, "Found pending call: $pendingCallId, attempting recovery")

                // Connect to Stringee to try to receive the call
                connectStringeeWithRetry(context, {
                    Log.d(Common.TAG, "Connected for pending call recovery")
                }, true)
            } else {
                Log.d(Common.TAG, "Pending call too old, clearing: $pendingCallId")
                clearPendingCallData(context)
            }
        }
    }
}
