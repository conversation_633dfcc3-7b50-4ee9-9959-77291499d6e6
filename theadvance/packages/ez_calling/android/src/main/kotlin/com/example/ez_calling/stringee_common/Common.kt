package com.example.ez_calling.stringee_common

import com.stringee.StringeeClient
import com.stringee.call.StringeeCall
import com.stringee.call.StringeeCall2
import java.util.Timer
import java.util.TimerTask

object Common {
    val TAG = "InAppStringee"
    var client: StringeeClient? = null
    var callsMap: MutableMap<String, StringeeCall> = HashMap()
    var calls2Map: MutableMap<String, StringeeCall2> = HashMap()
    var isInCall = false
    var currentCall: StringeeCall? = null
    var baseUrl = ""

    // Background connection management
    var isAppInBackground = false
    var reconnectTimer: Timer? = null
    var reconnectAttempts = 0
    var maxReconnectAttempts = 3
    var reconnectDelay = 5000L // 5 seconds
}
