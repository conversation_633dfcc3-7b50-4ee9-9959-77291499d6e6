package com.example.ez_calling.stringee_common

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.ez_calling.R
import java.util.Timer
import java.util.TimerTask

class StringeeCallService : Service() {
    
    companion object {
        private const val TAG = "StringeeCallService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "stringee_call_service"
        private const val SERVICE_TIMEOUT = 60000L // 60 seconds
        
        fun startService(context: Context, callId: String? = null) {
            val intent = Intent(context, StringeeCallService::class.java)
            callId?.let { intent.putExtra("callId", it) }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, StringeeCallService::class.java)
            context.stopService(intent)
        }
    }
    
    private var serviceTimer: Timer? = null
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "StringeeCallService created")
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "StringeeCallService started with action: ${intent?.action}")

        when (intent?.action) {
            "CALL_RECEIVED" -> {
                Log.d(TAG, "Call received notification")
                onCallReceived()
                return START_NOT_STICKY
            }
            "CALL_ENDED" -> {
                Log.d(TAG, "Call ended notification")
                onCallEnded()
                return START_NOT_STICKY
            }
            else -> {
                // Normal service start
                val callId = intent?.getStringExtra("callId")
                Log.d(TAG, "Service started for call: $callId")

                // Start foreground service
                startForeground(NOTIFICATION_ID, createNotification())

                // Initialize connection manager
                StringeeConnectionManager.initialize(applicationContext)

                // Connect to Stringee for incoming call handling
                StringeeUtils.connectStringee(applicationContext, {
                    Log.d(TAG, "Stringee connected in service")
                }, true)

                // Set up auto-stop timer
                setupAutoStopTimer()

                return START_NOT_STICKY // Don't restart if killed
            }
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "StringeeCallService destroyed")
        
        // Cancel timer
        serviceTimer?.cancel()
        serviceTimer = null
        
        // Disconnect if not in call
        if (!Common.isInCall) {
            StringeeConnectionManager.forceDisconnect()
        }
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Stringee Call Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Maintains connection for incoming calls"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Preparing for incoming call")
            .setContentText("Establishing connection...")
            .setSmallIcon(R.drawable.ic_answer_call)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    private fun setupAutoStopTimer() {
        serviceTimer?.cancel()
        serviceTimer = Timer()
        
        serviceTimer?.schedule(object : TimerTask() {
            override fun run() {
                Log.d(TAG, "Service timeout reached, stopping service")
                stopSelf()
            }
        }, SERVICE_TIMEOUT)
    }
    
    fun onCallReceived() {
        Log.d(TAG, "Call received, service can continue")
        // Cancel auto-stop timer since we have an active call
        serviceTimer?.cancel()
        serviceTimer = null
    }
    
    fun onCallEnded() {
        Log.d(TAG, "Call ended, stopping service")
        stopSelf()
    }
}
