package com.example.ez_calling.stringee_common

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.widget.Toast
import android.media.AudioManager
import com.example.ez_calling.apiinterface.ApiInterface
import com.example.ez_calling.apiinterface.UserStringeeTokenFetchResponseModel
import com.example.ez_calling.flutter_callkit_incoming.CallkitConstants
import com.example.ez_calling.flutter_callkit_incoming.CallkitIncomingBroadcastReceiver
import com.example.ez_calling.flutter_callkit_incoming.CallkitNotificationManager
import com.example.ez_calling.flutter_callkit_incoming.CallkitSoundPlayerService
import com.example.ez_calling.flutter_callkit_incoming.Data
import com.stringee.StringeeClient
import com.stringee.call.StringeeCall
import com.stringee.call.StringeeCall2
import com.stringee.exception.StringeeError
import com.stringee.listener.StatusListener
import com.stringee.listener.StringeeConnectionListener
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone
import java.util.Timer
import kotlin.concurrent.schedule


object StringeeUtils {

    fun connectStringee(context: Context, onConnected: () -> Unit = {}, onReceiveBg: Boolean = false) {
        // Initialize the connection manager if not already done
        StringeeConnectionManager.initialize(context)

        // Use the connection manager for better lifecycle handling
        StringeeConnectionManager.connectStringeeWithRetry(context, onConnected, onReceiveBg)
    }

    fun fetchStringeeToken(context: Context,onSuccess: (String)->Unit ) {
        val sharedPref =  context.getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE)
        val accessToken = sharedPref.getString("accessToken", "") ?: ""
        val fToken = sharedPref.getString("firebase_token", "") ?: ""

        if(accessToken.isEmpty()) return
        try{
            // start fetching stringee toke\
            val retrofitBuilder = Retrofit.Builder()
                    .addConverterFactory(GsonConverterFactory.create())
                    .baseUrl(Common.baseUrl).build()
                    .create(ApiInterface::class.java)
            val deviceinfo = HashMap<String, Any?>()
            val appVersion = getAppVersion(context)
            deviceinfo["app_version"] = appVersion
            deviceinfo["platform"] = "android"
            deviceinfo["firebase_token"] = fToken
            val encodedDeviceInfo = JSONObject(deviceinfo).toString()
            val retrofitData = retrofitBuilder.getData(accessToken, encodedDeviceInfo);
            retrofitData.enqueue(object : Callback<UserStringeeTokenFetchResponseModel?> {
                override fun onResponse(call: Call<UserStringeeTokenFetchResponseModel?>, response: Response<UserStringeeTokenFetchResponseModel?>) {
                    val responseBody = response.body()
                    if(responseBody != null) {
                        val stringeeToken = responseBody.data?.token ?: ""
                        if (stringeeToken.isEmpty()) return
                        with(sharedPref.edit()) {
                            putString("loginStringee", stringeeToken)
                            apply()
                        }
                        onSuccess(stringeeToken)
                    }
                }

                override fun onFailure(call: Call<UserStringeeTokenFetchResponseModel?>, t: Throwable) {
                    Log.d(context.packageName,"error: "+ t.message)
                }
            })

        }catch (err: Exception){
            Log.d(context.packageName,"exception ${err.message}")
        }
    }

    fun onCallReceivedBackgroundHandler(context: Context, currentCall: StringeeCall?) {
        val callkitParams = mapOf(
                "id" to currentCall?.callId,
                "nameCaller" to "${currentCall?.d} (${currentCall?.from?.removePrefix("agent_")})",
                "appName" to "The Advance",
                "avatar" to "https://ibb.co/yV3Kwqh",
                "handle" to currentCall?.from,
                "type" to 0,
                "missedCallNotification" to mapOf(
                        "showNotification" to true,
                        "isShowCallback" to true
                ),
                "duration" to 30000,
                "extra" to mapOf(
                        "callId" to currentCall?.callId,
                        "from" to currentCall?.from,
                        "fromAlias" to currentCall?.fromAlias,
                        "to" to currentCall?.to,
                        "toAlias" to currentCall?.toAlias,
                        "isVideoCall" to currentCall?.isVideoCall
                ),
                "android" to mapOf(
                        "isCustomNotification" to true,
                        "isShowLogo" to false,
                        "ringtonePath" to "system_ringtone_default",
                        "backgroundColor" to "#0955fa",
                        "backgroundUrl" to "https://i.pravatar.cc/500",
                        "actionColor" to "#4CAF50"
                )
        )

        val data = Data(callkitParams)
        data.from = "notification"
        //send BroadcastReceiver
        context.sendBroadcast(
                CallkitIncomingBroadcastReceiver.getIntentIncoming(
                        requireNotNull(context),
                        data.toBundle()
                )
        )

        currentCall?.setCallListener(object : StringeeCall.StringeeCallListener {
            override fun onSignalingStateChange(
                    stringeeCall: StringeeCall,
                    signalingState: StringeeCall.SignalingState,
                    reason: String,
                    sipCode: Int,
                    sipReason: String
            ) {
                Log.d(Common.TAG, "Background call state change: $signalingState")
                when (signalingState) {
                    StringeeCall.SignalingState.ENDED -> {
                        val notificationId = data.toBundle().getString(CallkitConstants.EXTRA_CALLKIT_ID, "callkit_incoming").hashCode()
                        stopRingtoneService(context)
                        CallkitNotificationManager.getDeclinePendingIntent(context, notificationId, data.toBundle()).send()
                    }
                    StringeeCall.SignalingState.ANSWERED -> {
                        // Call was answered, stop ringtone and clear notification
                        stopRingtoneService(context)
                        val callkitNotificationManager = CallkitNotificationManager(context)
                        callkitNotificationManager.clearIncomingNotification(data.toBundle(), true)
                    }
                    else -> {
                        // Other states - no action needed
                    }
                }
            }

            override fun onError(p0: StringeeCall?, p1: Int, p2: String?) {
                Log.e(Common.TAG, "Background call error: $p2")
            }

            override fun onHandledOnAnotherDevice(p0: StringeeCall?, p1: StringeeCall.SignalingState?, p2: String?) {
                Log.d(Common.TAG, "Call handled on another device: $p1")
                // Stop ringtone and clear notification
                stopRingtoneService(context)
                val callkitNotificationManager = CallkitNotificationManager(context)
                callkitNotificationManager.clearIncomingNotification(data.toBundle(), false)
            }

            override fun onMediaStateChange(p0: StringeeCall?, p1: StringeeCall.MediaState?) {}

            override fun onLocalStream(p0: StringeeCall?) {}

            override fun onRemoteStream(p0: StringeeCall?) {}

            override fun onCallInfo(p0: StringeeCall?, p1: JSONObject?) {}
        })
    }

    fun reportMessage(context: Context?, message: String?) {
        val toast = Toast.makeText(context, message, Toast.LENGTH_LONG)
        toast.setGravity(Gravity.CENTER, 0, 0)
        toast.show()
    }

    fun postDelay(runnable: Runnable?, delayMillis: Long) {
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed(runnable!!, delayMillis)
    }

    fun runOnUiThread(runnable: Runnable?) {
        val handler = Handler(Looper.getMainLooper())
        handler.post(runnable!!)
    }

    fun isTextEmpty(text: String?): Boolean {
        return if (text != null) {
            if (text.equals("null", ignoreCase = true)) {
                true
            } else {
                text.trim { it <= ' ' }.isEmpty()
            }
        } else {
            true
        }
    }



    /**
     * Get free call time from duration
     *
     * @param duration
     * @return
     */
    fun getCallTime(currentTime: Long, startTime: Long): String? {
        val time = currentTime - startTime
        val format = SimpleDateFormat("mm:ss")
        format.timeZone = TimeZone.getTimeZone("GMT")
        return format.format(Date(time))
    }

    fun getAppVersion(
            context: Context,
    ): String? {
        return try {
            val packageManager = context.packageManager
            val packageName = context.packageName
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(packageName, PackageManager.PackageInfoFlags.of(0))
            } else {
                packageManager.getPackageInfo(packageName, 0)
            }
            return packageInfo.versionName
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Force stop the ringtone service with multiple attempts
     */
    fun stopRingtoneService(context: Context) {
        try {
            Log.d("StringeeUtils", "Attempting to stop ringtone service...")

            // Method 1: Force stop all tracked ringtones first
            try {
                CallkitSoundPlayerService.forceStopAllRingtones()
                Log.d("StringeeUtils", "Force stopped all static ringtone references")
            } catch (e: Exception) {
                Log.e("StringeeUtils", "Error force stopping ringtones: ${e.message}")
            }

            // Method 2: Send stop command to service (this will call stopSelf())
            val stopCommandIntent = Intent(context, CallkitSoundPlayerService::class.java).apply {
                action = "STOP_SERVICE"
            }
            context.startService(stopCommandIntent)

            // Method 3: Regular stopService as backup
            val stopIntent = Intent(context, CallkitSoundPlayerService::class.java)
            val stopped = context.stopService(stopIntent)

            Log.d("StringeeUtils", "Ringtone service stop commands sent - stopService returned: $stopped")

            // Method 4: Force stop all system ringtones as additional safety
            try {
                val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                // This will stop any system ringtones that might be playing
                audioManager.mode = AudioManager.MODE_NORMAL
                Log.d("StringeeUtils", "Audio mode reset to normal")
            } catch (e: Exception) {
                Log.e("StringeeUtils", "Error resetting audio mode: ${e.message}")
            }

        } catch (e: Exception) {
            Log.e("StringeeUtils", "Error stopping ringtone service: ${e.message}")
        }
    }
}