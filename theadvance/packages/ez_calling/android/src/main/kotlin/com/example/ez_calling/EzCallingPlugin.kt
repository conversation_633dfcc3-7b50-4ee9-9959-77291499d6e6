package com.example.ez_calling

import android.app.Activity
import android.app.Activity.RESULT_CANCELED
import android.app.AlertDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.example.ez_calling.stringee_common.Common
import com.example.ez_calling.stringee_common.OutgoingCallActivity
import com.example.ez_calling.stringee_common.StringeeConnectionManager
import com.example.ez_calling.stringee_common.StringeeUtils
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.PluginRegistry
import android.os.Handler
import android.os.Looper

/** EzCallingPlugin */
class EzCallingPlugin: FlutterPlugin, MethodCallHandler, ActivityAware, PluginRegistry.ActivityResultListener, Result {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private lateinit var channel : MethodChannel
  private lateinit var context : Context
  private var activity: Activity? = null
  private lateinit var launcher: ActivityResultLauncher<Intent>

  override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    channel = MethodChannel(flutterPluginBinding.binaryMessenger, "ez_calling")
    context = flutterPluginBinding.applicationContext
    channel.setMethodCallHandler(this)
  }

  override fun onMethodCall(call: MethodCall, result: Result) {
    if (call.method == "loggedin") {
      val baseUrl = call.argument<String>("baseUrl") ?: ""
      Common.baseUrl = baseUrl
      // Initialize the connection manager for proper lifecycle handling
      StringeeConnectionManager.initialize(context)

      // Check for any pending calls from when app was killed
      StringeeConnectionManager.checkForPendingCall(context)

      // Fetch stringee token and connect with proper background handling
      StringeeUtils.fetchStringeeToken(context) {
        StringeeUtils.connectStringee(context)
      }
      return
    }
    if (call.method == "makeCall"){
      val to = call.argument<String>("to") ?: ""
      val toName = call.argument<String>("toName") ?: ""
      val avatarUrl = call.argument<String>("avatarUrl") ?: ""
      val isVideoCall = call.argument<Boolean>("isVideoCall") ?: false
      makeCall(to = to, toName = toName, avatarUrl = avatarUrl, isVideoCall = isVideoCall)
      return
    }
      result.notImplemented()
  }

  private fun makeCall(to: String, toName: String, avatarUrl: String = "", isVideoCall: Boolean = false) {
    if (to.trim { it <= ' ' }.isNotEmpty()) {
      // Ensure connection manager is initialized
      StringeeConnectionManager.initialize(context)

      if (Common.client?.isConnected == true) {
        val intent = Intent(context, OutgoingCallActivity::class.java)
        intent.putExtra("from", Common.client!!.userId)
        intent.putExtra("to", to)
        intent.putExtra("toName", toName)
        intent.putExtra("avatarUrl", avatarUrl)
        intent.putExtra("is_video_call", isVideoCall)
        launcher.launch(intent)
      } else {
        StringeeUtils.connectStringee(context, {
          if (Common.client?.isConnected == true) {
            val intent = Intent(context, OutgoingCallActivity::class.java)
            intent.putExtra("from", Common.client!!.userId)
            intent.putExtra("to", to)
            intent.putExtra("toName", toName)
            intent.putExtra("avatarUrl", avatarUrl)
            intent.putExtra("is_video_call", isVideoCall)
            launcher.launch(intent)
          } else {
            StringeeUtils.reportMessage(
              context,
              "Không thể kết nối Stringee, vui lòng thử lại"
            )
          }
        }, false)
      }
    }
  }

  private fun onPermissionCallback(result: ActivityResult) {
    if (result.resultCode == RESULT_CANCELED) if (result.data != null) {
      if (result.data!!.action != null && result.data!!.action
                      .equals("open_app_setting")
      ) {
        val builder =
                AlertDialog.Builder(context)

        val appName: String = context.packageManager.getApplicationLabel(
                context.packageManager.getApplicationInfo(context.packageName, PackageManager.GET_META_DATA)).toString()
        builder.setTitle(appName)

        builder.setTitle(appName)
        builder.setMessage("Permissions must be granted for the call")
        builder.setPositiveButton(
                "Ok"
        ) { dialogInterface: DialogInterface, _: Int -> dialogInterface.cancel() }
        builder.setNegativeButton(
                "Settings"
        ) { dialogInterface: DialogInterface, _: Int ->
          dialogInterface.cancel()
          // open app setting
          val intent =
                  Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
          intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
          val uri =
                  Uri.fromParts("package", context.packageName, null)
          intent.data = uri
          context.startActivity(intent)
        }
        builder.create().show()
      }
    }
  }

  override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    binding.addActivityResultListener(this)
    activity = binding.activity

     launcher = (activity as ComponentActivity).registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
    ) { result -> onPermissionCallback(result) }
  }

  override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
    channel.setMethodCallHandler(null)
  }

  override fun onDetachedFromActivityForConfigChanges() {
    // Not implemented
  }

  override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
    // Not implemented
  }

  override fun onDetachedFromActivity() {
    activity = null
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
    return false
  }

  override fun success(result: Any?) {
    // Not implemented
  }

  override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
    // Not implemented
  }

  override fun notImplemented() {
    // Not implemented
  }
}
