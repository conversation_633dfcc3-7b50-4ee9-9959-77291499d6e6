import 'package:custom_image_crop/src/models/model.dart';
import 'package:flutter/material.dart';

/// The controller that handles the cropping and
/// changing of the cropping area
class CustomImageCropController {
  /// Listener for the cropping area changes
  final listeners = <CustomImageCropListener>[];

  /// Crop the image
  Future<MemoryImage?> onCropImage() =>
      listeners.map((final e) => e.onCropImage()).first;

  /// The data that handles the transformation of the cropped image.
  CropImageData? get cropImageData => listeners.map((final e) => e.data).first;

  /// Add a new listener for the cropping area changes
  void addListener(final CustomImageCropListener listener)
   => listeners.add(listener);

  /// Remove a listener for the cropping area changes
  void removeListener(final CustomImageCropListener listener) =>
      listeners.remove(listener);

  /// Notify all listeners for the cropping area changes
  void notifyListeners() => addTransition(CropImageData());

  void dispose() => listeners.clear();

  /// Move the cropping area using the given translation
  void addTransition(final CropImageData transition) =>
      listeners.forEach((final e) => e.addTransition(transition));

  /// Reset the cropping area
  void reset() => setData(CropImageData());

  /// Update the cropping area
  void setData(final CropImageData data) => listeners.forEach((final e) => e.setData(data));
}

mixin CustomImageCropListener {
  /// The data that handles the transformation of the cropped image.
  var data = CropImageData();

  /// Move the cropping area using the given translation
  void addTransition(final CropImageData transition);

  /// Update the cropping area
  void setData(final CropImageData transition);

  /// Crop the image
  Future<MemoryImage?> onCropImage();
}
