{"@@last_modified": "2021-01-19T14:37:06.889186", "appName": "The Advance", "@appName": {"type": "text", "placeholders": {}}, "service": "<PERSON><PERSON><PERSON> v<PERSON>", "@service": {"type": "text", "placeholders": {}}, "showDirection": "Chỉ đường", "@showDirection": {"type": "text", "placeholders": {}}, "appointmentApproved": "approved", "@appointmentApproved": {"type": "text", "placeholders": {}}, "appointmentWaitingApproved": "waiting_approve", "@appointmentWaitingApproved": {"type": "text", "placeholders": {}}, "modifyAppointment": "<PERSON><PERSON><PERSON> l<PERSON>", "@modifyAppointment": {"type": "text", "placeholders": {}}, "cancelAppointment": "Huỷ lịch", "@cancelAppointment": {"type": "text", "placeholders": {}}, "guestUser": "Guest user", "@guestUser": {"type": "text", "placeholders": {}}, "loginByPhoneNumber": "<PERSON><PERSON><PERSON> nhập bằng số điện thoại", "@loginByPhoneNumber": {"type": "text", "placeholders": {}}, "unknown": "<PERSON><PERSON> lỗi x<PERSON> ra, vui lòng thử lại sau", "@unknown": {"type": "text", "placeholders": {}}, "tokenExpired": "<PERSON><PERSON><PERSON> đăng nhập đã hết hạn, vui lòng đăng nhập lại", "@tokenExpired": {"type": "text", "placeholders": {}}, "supportRequest": "<PERSON><PERSON><PERSON> cầu hỗ trợ", "@supportRequest": {"type": "text", "placeholders": {}}, "inboxIsEmpty": "Bạn chưa có thư hỗ trợ nào", "@inboxIsEmpty": {"type": "text", "placeholders": {}}, "servicesIsEmpty": "Bạn chưa đặt dịch vụ nào", "@servicesIsEmpty": {"type": "text", "placeholders": {}}, "createSupportRequest": "<PERSON><PERSON><PERSON> yêu cầu hỗ trợ", "@createSupportRequest": {"type": "text", "placeholders": {}}, "confirmLogout": "Bạn có muốn đăng xuất tài khoản này?", "@confirmLogout": {"type": "text", "placeholders": {}}, "confirmCancelAppointment": "Bạn có chắc chắn muốn huỷ lịch?", "@confirmCancelAppointment": {"type": "text", "placeholders": {}}, "callYouLater": "Sẽ có nhân viên gọi điện xác nhận trong thời gian sớm nhất", "@callYouLater": {"type": "text", "placeholders": {}}, "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "@logout": {"type": "text", "placeholders": {}}, "account": "<PERSON><PERSON><PERSON>", "@account": {"type": "text", "placeholders": {}}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {"type": "text", "placeholders": {}}, "history": "<PERSON><PERSON><PERSON> s<PERSON>", "@history": {"type": "text", "placeholders": {}}, "referral": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u kh<PERSON>ch hàng", "@referral": {"type": "text", "placeholders": {}}, "contactUs": "<PERSON><PERSON><PERSON>", "@contactUs": {"type": "text", "placeholders": {}}, "collaborator": "<PERSON><PERSON><PERSON> tác viên", "@collaborator": {"type": "text", "placeholders": {}}, "notUpdated": "<PERSON><PERSON><PERSON> c<PERSON> nh<PERSON>t", "@notUpdated": {"type": "text", "placeholders": {}}, "fullName": "Họ và tên", "@fullName": {"type": "text", "placeholders": {}}, "email": "Email", "@email": {"type": "text", "placeholders": {}}, "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "@phoneNumber": {"type": "text", "placeholders": {}}, "address": "Địa chỉ", "@address": {"type": "text", "placeholders": {}}, "save": "<PERSON><PERSON><PERSON>", "@save": {"type": "text", "placeholders": {}}, "edit": "<PERSON><PERSON><PERSON>", "@edit": {"type": "text", "placeholders": {}}, "errorOccurred": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật thông tin", "@errorOccurred": {"type": "text", "placeholders": {}}, "errorEmptyFullName": "<PERSON><PERSON> lòng nhập họ và tên", "@errorEmptyFullName": {"type": "text", "placeholders": {}}, "updateProfileSuccessful": "<PERSON><PERSON><PERSON> nhật thông tin cá nhân thành công", "@updateProfileSuccessful": {"type": "text", "placeholders": {}}, "personalInfo": "Thông tin cá nhân", "@personalInfo": {"type": "text", "placeholders": {}}, "emailHaveWhiteSpace": "<PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> chứa kho<PERSON>ng trắng", "@emailHaveWhiteSpace": {"type": "text", "placeholders": {}}, "emailWrongFormat": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "@emailWrongFormat": {"type": "text", "placeholders": {}}, "close": "Đ<PERSON><PERSON>", "@close": {"type": "text", "placeholders": {}}, "openSettings": "Mở cài đặt", "@openSettings": {"type": "text", "placeholders": {}}, "settings": "<PERSON>ài đặt chung", "@settings": {"type": "text", "placeholders": {}}, "fonts": "Phông chữ", "@fonts": {"type": "text", "placeholders": {}}, "fontSize": "Cỡ chữ", "@fontSize": {"type": "text", "placeholders": {}}, "smallSize": "Cỡ nhỏ", "@smallSize": {"type": "text", "placeholders": {}}, "bigSize": "Cỡ lớn", "@bigSize": {"type": "text", "placeholders": {}}, "done": "<PERSON><PERSON>", "@done": {"type": "text", "placeholders": {}}, "referralContent": "Giới thiệu khách hàng bằng cách chia sẻ mã giới thiệu hoặc nhập thông tin theo mẫu dưới để The Advance có thể hỗ trợ.", "@referralContent": {"type": "text", "placeholders": {}}, "pickServices": "L<PERSON>a chọn quà tặng", "@pickServices": {"type": "text", "placeholders": {}}, "pickYears": "<PERSON><PERSON><PERSON> n<PERSON> sinh", "@pickYears": {"type": "text", "placeholders": {}}, "send": "<PERSON><PERSON><PERSON>", "@send": {"type": "text", "placeholders": {}}, "inputContent": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "@inputContent": {"type": "text", "placeholders": {}}, "notes": "<PERSON><PERSON><PERSON>", "@notes": {"type": "text", "placeholders": {}}, "gift": "Quà tặng", "@gift": {"type": "text", "placeholders": {}}, "years": "<PERSON><PERSON><PERSON> (*)", "@years": {"type": "text", "placeholders": {}}, "phoneHintText": "<PERSON><PERSON><PERSON><PERSON> số đi<PERSON> (bắt buộc)", "@phoneHintText": {"type": "text", "placeholders": {}}, "phoneText": "<PERSON><PERSON> điện tho<PERSON> (*)", "@phoneText": {"type": "text", "placeholders": {}}, "fullNameHintText": "<PERSON><PERSON> (bắt buộc)", "@fullNameHintText": {"type": "text", "placeholders": {}}, "fullNameText": "<PERSON><PERSON> tên (*)", "@fullNameText": {"type": "text", "placeholders": {}}, "pickGift": "<PERSON><PERSON><PERSON> quà tặng", "@pickGift": {"type": "text", "placeholders": {}}, "notEnoughInfo": "<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin bắt buộc", "@notEnoughInfo": {"type": "text", "placeholders": {}}, "ageRequired": "<PERSON>ố tuổi không hợ<PERSON> lệ, ph<PERSON>i lớn hơn hoặc bằng 25 tuổi", "@ageRequired": {"type": "text", "placeholders": {}}, "resendOTP": "Gửi lại OTP", "@resendOTP": {"type": "text", "placeholders": {}}, "confirmOTP": "<PERSON><PERSON><PERSON>", "@confirmOTP": {"type": "text", "placeholders": {}}, "otpInfo": "Một mã xác thực đã được gửi đến \n", "@otpInfo": {"type": "text", "placeholders": {}}, "continuous": "<PERSON><PERSON><PERSON><PERSON>", "@continuous": {"type": "text", "placeholders": {}}, "appointments": "<PERSON><PERSON><PERSON> hẹn", "@appointments": {"type": "text", "placeholders": {}}, "appointmentDetails": "<PERSON> tiết lịch hẹn", "@appointmentDetails": {"type": "text", "placeholders": {}}, "phoneIncorrect": "<PERSON><PERSON> điện tho<PERSON><PERSON> khô<PERSON>, vui lòng kiểm tra lại", "@phoneIncorrect": {"type": "text", "placeholders": {}}, "formIncorrect": "Thông tin không hợ<PERSON> l<PERSON>, vui lòng kiểm tra lại", "@formIncorrect": {"type": "text", "placeholders": {}}, "loginMessage": "<PERSON><PERSON><PERSON> khách vui lòng dùng số điện thoại đã cung cấp khi dùng dịch vụ tại <PERSON>", "@loginMessage": {"type": "text", "placeholders": {}}, "agree": "<PERSON><PERSON>i đồng ý với ", "@agree": {"type": "text", "placeholders": {}}, "termsAndConditions": "điều kiện và điều khoản sử dụng", "@termsAndConditions": {"type": "text", "placeholders": {}}, "copyright": "của The Advance", "@copyright": {"type": "text", "placeholders": {}}, "uploadAvatarSuccessful": "<PERSON><PERSON><PERSON> nh<PERSON>t hình <PERSON>nh đại diện thành công", "@uploadAvatarSuccessful": {"type": "text", "placeholders": {}}, "startUsingApp": "<PERSON><PERSON><PERSON> đ<PERSON>u tr<PERSON><PERSON> ng<PERSON>", "@startUsingApp": {"type": "text", "placeholders": {}}, "skip": "Để sau", "@skip": {"type": "text", "placeholders": {}}, "emptyPage": "<PERSON><PERSON><PERSON> có nội dung", "@emptyPage": {"type": "text", "placeholders": {}}, "accept": "Đồng ý", "@accept": {"type": "text", "placeholders": {}}, "requestLocation": "<PERSON>ui lòng cho phép ứng dụng truy cập vị trí", "@requestLocation": {"type": "text", "placeholders": {}}, "getLocationSuccesful": "<PERSON><PERSON><PERSON> thông tin vị trí hoàn tất", "@getLocationSuccesful": {"type": "text", "placeholders": {}}, "getLocationFailure": "<PERSON><PERSON><PERSON> thông tin vị trí thất bại", "@getLocationFailure": {"type": "text", "placeholders": {}}, "connectionError": "<PERSON><PERSON><PERSON><PERSON> có kết nối internet, vui lòng kiểm tra lại", "@connectionError": {"type": "text", "placeholders": {}}, "referralCode": "<PERSON>ã giới thiệu", "@referralCode": {"type": "text", "placeholders": {}}, "referralMessage": "CTV có thể chia sẻ mã bằng đưa voucher có mã CTV (Voucher do The Advance cung cấp) hoặc chia sẻ QR code cho KH, KH sau đó có thể sử dụng voucher hoặc mã QR khi thanh toán.", "@referralMessage": {"type": "text", "placeholders": {}}, "shareQRCode": "Chia sẻ mã QR", "@shareQRCode": {"type": "text", "placeholders": {}}, "invalidScannedCode": "Thông tin quét mã không đúng", "@invalidScannedCode": {"type": "text", "placeholders": {}}, "referralCollaborator": "G<PERSON><PERSON>i thiệu CTV", "@referralCollaborator": {"type": "text", "placeholders": {}}, "news": "<PERSON> tức", "@news": {"type": "text", "placeholders": {}}, "androidConfirmExit": "<PERSON><PERSON><PERSON> lần nữa để tho<PERSON>t <PERSON>ng dụng", "@androidConfirmExit": {"type": "text", "placeholders": {}}, "home": "Trang chủ", "@home": {"type": "text", "placeholders": {}}, "sendFeedback": "<PERSON><PERSON><PERSON> đ<PERSON>h giá", "@sendFeedback": {"type": "text", "placeholders": {}}, "sendResponse": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi", "@sendResponse": {"type": "text", "placeholders": {}}, "feedbackServiceMessage": "<PERSON>ui lòng đánh giá sao dịch vụ", "@feedbackServiceMessage": {"type": "text", "placeholders": {}}, "feedbackServiceTitle": "Đánh gi<PERSON> d<PERSON>ch vụ", "@feedbackServiceTitle": {"type": "text", "placeholders": {}}, "addMoreComment": "<PERSON><PERSON><PERSON><PERSON> thêm <PERSON> kiến", "@addMoreComment": {"type": "text", "placeholders": {}}, "inputPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "@inputPhoneNumber": {"type": "text", "placeholders": {}}, "needSupportRequestType": "<PERSON><PERSON><PERSON> c<PERSON>u cần hỗ trợ (kh<PERSON><PERSON> b<PERSON>t buộc)", "@needSupportRequestType": {"type": "text", "placeholders": {}}, "chooseSupportRequestType": "<PERSON><PERSON><PERSON> yêu cầu", "@chooseSupportRequestType": {"type": "text", "placeholders": {}}, "selectChooseSupportRequestType": "<PERSON><PERSON><PERSON> chọn yêu cầu", "@selectChooseSupportRequestType": {"type": "text", "placeholders": {}}, "noteSupportRequest": "<PERSON><PERSON><PERSON>", "@noteSupportRequest": {"type": "text", "placeholders": {}}, "inputInformationSupportRequest": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "@inputInformationSupportRequest": {"type": "text", "placeholders": {}}, "missingInformationSupportRequest": "<PERSON><PERSON><PERSON><PERSON> thông tin", "@missingInformationSupportRequest": {"type": "text", "placeholders": {}}, "hintNeedSupportRequest": "<PERSON><PERSON> lòng nhập nội dung yêu cầu hỗ trợ", "@hintNeedSupportRequest": {"type": "text", "placeholders": {}}, "sendSupportRequestSuccessfully": "<PERSON><PERSON> gửi yêu cầu hỗ trợ", "@sendSupportRequestSuccessfully": {"type": "text", "placeholders": {}}, "sendSupportRequestFail": "<PERSON><PERSON><PERSON> yêu cầu hỗ trợ thất bại", "@sendSupportRequestFail": {"type": "text", "placeholders": {}}, "alert": "<PERSON><PERSON><PERSON><PERSON> báo", "@alert": {"type": "text", "placeholders": {}}, "requestPermissionLibrary": "Mở quyền cho phép truy cập thư viện ảnh để sử dụng được tính năng này", "@requestPermissionLibrary": {"type": "text", "placeholders": {}}, "requestPermissionCamera": "Mở quyền cho phép truy cập camera để sử dụng đư<PERSON><PERSON> t<PERSON>h năng này", "@requestPermissionCamera": {"type": "text", "placeholders": {}}, "requestPermissionStorage": "Mở quyền cho phép truy cập tập tin để sử dụng đư<PERSON><PERSON> tính năng này", "@requestPermissionStorage": {"type": "text", "placeholders": {}}, "requestPermissionMicro": "Mở quyền cho phép truy cập micro để sử dụng đượ<PERSON> tính năng này", "@requestPermissionMicro": {"type": "text", "placeholders": {}}, "requestPermissionNotification": "Mở quyền cho phép thông báo để sử dụng đượ<PERSON> t<PERSON>h năng này", "@requestPermissionNotification": {"type": "text", "placeholders": {}}, "takePicture": "<PERSON><PERSON><PERSON>", "@takePicture": {"type": "text", "placeholders": {}}, "retakePicture": "<PERSON><PERSON><PERSON> l<PERSON>", "@retakePicture": {"type": "text", "placeholders": {}}, "photoLibrary": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "@photoLibrary": {"type": "text", "placeholders": {}}, "welcome": "<PERSON><PERSON> ch<PERSON>o", "@welcome": {"type": "text", "placeholders": {}}, "calendarEvents": "<PERSON><PERSON><PERSON> s<PERSON> ki<PERSON>n", "@calendarEvents": {"type": "text", "placeholders": {}}, "calendarOthersEvents": "<PERSON><PERSON><PERSON> s<PERSON> kiện kh<PERSON>c", "@calendarOthersEvents": {"type": "text", "placeholders": {}}, "detailEvent": "<PERSON> tiết sự kiện", "@detailEvent": {"type": "text", "placeholders": {}}, "seeMore": "<PERSON><PERSON>", "@seeMore": {"type": "text", "placeholders": {}}, "collapse": "<PERSON><PERSON>", "@collapse": {"type": "text", "placeholders": {}}, "subscribeEvent": "ĐĂNG KÝ THAM GIA", "@subscribeEvent": {"type": "text", "placeholders": {}}, "subscribedEventMessage": "BẠN ĐÃ ĐĂNG KÝ THAM GIA SỰ KIỆN NÀY", "@subscribedEventMessage": {"type": "text", "placeholders": {}}, "subscribeEventSuccess": "<PERSON><PERSON><PERSON> ký tham dự thành công", "@subscribeEventSuccess": {"type": "text", "placeholders": {}}, "waitForUsAcceptEvent": "<PERSON><PERSON> lòng chờ xác nhận của chúng tôi", "@waitForUsAcceptEvent": {"type": "text", "placeholders": {}}, "subscribedEvent": "Sự kiện đã đăng ký", "@subscribedEvent": {"type": "text", "placeholders": {}}, "detail": "<PERSON> ti<PERSON>", "@detail": {"type": "text", "placeholders": {}}, "eVoucher": "E-Vouchers", "@eVoucher": {"type": "text", "placeholders": {}}, "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "@viewAll": {"type": "text", "placeholders": {}}, "inputVoucherCode": "Nhập mã Vouchers", "@inputVoucherCode": {"type": "text", "placeholders": {}}, "getItNow": "<PERSON><PERSON><PERSON> ngay", "@getItNow": {"type": "text", "placeholders": {}}, "detailShop": "<PERSON> tiết ưu đãi", "@detailShop": {"type": "text", "placeholders": {}}, "detailEvaluationResult": "<PERSON> tiết kết quả đánh giá", "@detailEvaluationResult": {"type": "text", "placeholders": {}}, "getItSuccess": "<PERSON><PERSON><PERSON> thành công", "@getItSuccess": {"type": "text", "placeholders": {}}, "getItShopMessageDone": "BẠN ĐÃ ĐÃ ĐỔI ƯU ĐÃI NÀY", "@getItShopMessageDone": {"type": "text", "placeholders": {}}, "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "@promotion": {"type": "text", "placeholders": {}}, "promotionOthers": "Các Ưu đãi khác", "@promotionOthers": {"type": "text", "placeholders": {}}, "subscribedEventCaution": "<PERSON>ạn muốn nhận vé tham gia sự kiện này", "@subscribedEventCaution": {"type": "text", "placeholders": {}}, "confirm": "<PERSON><PERSON><PERSON>", "@confirm": {"type": "text", "placeholders": {}}, "ofYou": "<PERSON><PERSON><PERSON> b<PERSON>n", "@ofYou": {"type": "text", "placeholders": {}}, "expired": "<PERSON><PERSON><PERSON>", "@expired": {"type": "text", "placeholders": {}}, "rewardPoints": "<PERSON><PERSON><PERSON><PERSON> Thưởng", "@rewardPoints": {"type": "text", "placeholders": {}}, "historyOfRewardPoints": "<PERSON><PERSON><PERSON> sử điểm thưởng", "@historyOfRewardPoints": {"type": "text", "placeholders": {}}, "preferentialShop": "Shop quà", "@preferentialShop": {"type": "text", "placeholders": {}}, "currentlyUnderDevelopment": "<PERSON><PERSON><PERSON> năng này hiện đang được phát triển !", "@currentlyUnderDevelopment": {"type": "text", "placeholders": {}}, "keepAccumulatingPoints": "<PERSON><PERSON><PERSON> tiếp tục tích điểm để nhận nhiều ưu đãi hơn đến từ TMV The Advance.", "@keepAccumulatingPoints": {"type": "text", "placeholders": {}}, "aboutYou": "<PERSON><PERSON><PERSON> cho bạn", "@aboutYou": {"type": "text", "placeholders": {}}, "vipSilver": "<PERSON><PERSON><PERSON><PERSON> viên bạc", "@vipSilver": {"type": "text", "placeholders": {}}, "vipGold": "<PERSON><PERSON><PERSON><PERSON> viên vàng", "@vipGold": {"type": "text", "placeholders": {}}, "vipDiamond": "<PERSON><PERSON><PERSON><PERSON> viên kim c<PERSON>", "@vipDiamond": {"type": "text", "placeholders": {}}, "member": "<PERSON><PERSON><PERSON><PERSON> viên", "@member": {"type": "text", "placeholders": {}}, "hotNews": "<PERSON> tức nổi bật", "@hotNews": {"type": "text", "placeholders": {}}, "version": "<PERSON><PERSON><PERSON>", "@version": {"type": "text", "placeholders": {}}, "notYetRated": "<PERSON><PERSON><PERSON> c<PERSON> hạng", "@notYetRated": {"type": "text", "placeholders": {}}, "category": "<PERSON><PERSON>", "@category": {"type": "text", "placeholders": {}}, "sort": "<PERSON><PERSON><PERSON>p", "@sort": {"type": "text", "placeholders": {}}, "getThisGift": "Bạn muốn đổi phần quà này?", "@getThisGift": {"type": "text", "placeholders": {}}, "amount": "Số lượng", "@amount": {"type": "text", "placeholders": {}}, "jumpToTop": "TRỞ LÊN TRÊN", "@jumpToTop": {"type": "text", "placeholders": {}}, "myGift": "<PERSON><PERSON><PERSON> c<PERSON>a bạn", "@myGift": {"type": "text", "placeholders": {}}, "giftCode": "Mã phần quà: ", "@giftCode": {"type": "text", "placeholders": {}}, "newReward": "<PERSON><PERSON><PERSON>", "@newReward": {"type": "text", "placeholders": {}}, "rewardUsed": "Đã dùng", "@rewardUsed": {"type": "text", "placeholders": {}}, "transactionHistory": "<PERSON><PERSON><PERSON> sử giao dịch", "@transactionHistory": {"type": "text", "placeholders": {}}, "emptyTransaction": "<PERSON><PERSON><PERSON><PERSON> có giao dịch", "@emptyTransaction": {"type": "text", "placeholders": {}}, "usedPromotion": "ĐÃ SỬ DỤNG ƯU ĐÃI", "@usedPromotion": {"type": "text", "placeholders": {}}, "allowNotification": "<PERSON> phép thông báo", "@allowNotification": {"type": "text", "placeholders": {}}, "beautyLive": "Beauty Live", "@beautyLive": {"type": "text", "placeholders": {}}, "liveStream": "Đang live stream", "@liveStream": {"type": "text", "placeholders": {}}, "previousStream": "Live stream trước", "@previousStream": {"type": "text", "placeholders": {}}, "playingStream": "<PERSON><PERSON> ph<PERSON>t: ", "@playingStream": {"type": "text", "placeholders": {}}, "joinEventCode": "<PERSON><PERSON> tham gia sự kiện: ", "@joinEventCode": {"type": "text", "placeholders": {}}, "voucherCode": "Mã voucher", "@voucherCode": {"type": "text", "placeholders": {}}, "giveVouchers": "Tặng Vouchers", "@giveVouchers": {"type": "text", "placeholders": {}}, "name": "<PERSON><PERSON> tên", "@name": {"type": "text", "placeholders": {}}, "brother": "<PERSON><PERSON>", "@brother": {"type": "text", "placeholders": {}}, "sister": "Chị", "@sister": {"type": "text", "placeholders": {}}, "give": "Tặng", "@give": {"type": "text", "placeholders": {}}, "infoReceivedPerson": "<PERSON>h<PERSON>ng tin người nhận", "@infoReceivedPerson": {"type": "text", "placeholders": {}}, "giveSuccessfulGift": "Bạn đã tặng Voucher thành công", "@giveSuccessfulGift": {"type": "text", "placeholders": {}}, "schedulerTitle": "Đặt lịch", "@schedulerTitle": {"type": "text", "placeholders": {}}, "findNearestBranch": "TÌM CHI NHÁNH GẦN NHẤT", "@findNearestBranch": {"type": "text", "placeholders": {}}, "choiceAppointmentDay": "CHỌN THỜI GIAN", "@choiceAppointmentDay": {"type": "text", "placeholders": {}}, "back": "Trở về", "@back": {"type": "text", "placeholders": {}}, "next": "<PERSON><PERSON><PERSON><PERSON>", "@next": {"type": "text", "placeholders": {}}, "choiceService": "CHỌN DỊCH VỤ", "@choiceService": {"type": "text", "placeholders": {}}, "chosenInfo": "THÔNG TIN ĐÃ CHỌN", "@chosenInfo": {"type": "text", "placeholders": {}}, "branchSelectionStep": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "@branchSelectionStep": {"type": "text", "placeholders": {}}, "timeSelectionStep": "<PERSON><PERSON><PERSON> thời gian", "@timeSelectionStep": {"type": "text", "placeholders": {}}, "serviceSelectionStep": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "@serviceSelectionStep": {"type": "text", "placeholders": {}}, "finishStep": "<PERSON><PERSON><PERSON> t<PERSON>t", "@finishStep": {"type": "text", "placeholders": {}}, "darkMode": "Dark Mode", "@darkMode": {"type": "text", "placeholders": {}}, "type": "<PERSON><PERSON><PERSON>", "@type": {"type": "text", "placeholders": {}}, "haveANote": "Bạn có một lời nh<PERSON>c", "@haveANote": {"type": "text", "placeholders": {}}, "letNgocDungKnowYou": "<PERSON><PERSON><PERSON> cho The Advance biết về bạn!", "@letNgocDungKnowYou": {"type": "text", "placeholders": {}}, "yourName": "Tên của bạn là gì?", "@yourName": {"type": "text", "placeholders": {}}, "letLocation": "<PERSON> phép vị trí", "@letLocation": {"type": "text", "placeholders": {}}, "beginDiscovery": "<PERSON>ắt đầu khám phá", "@beginDiscovery": {"type": "text", "placeholders": {}}, "thankToScheduler": "<PERSON><PERSON>m ơn bạn đã gửi thông tin đặt lịch", "@thankToScheduler": {"type": "text", "placeholders": {}}, "maybeHaveStaffCallYouSoon": "Sẽ có nhân viên gọi điện xác nhận trong thời gian sớm nhất.", "@maybeHaveStaffCallYouSoon": {"type": "text", "placeholders": {}}, "choiceProvince": "<PERSON><PERSON>n tỉnh thành", "@choiceProvince": {"type": "text", "placeholders": {}}, "notCompleteStep": "<PERSON><PERSON>n chưa hoàn thành b<PERSON><PERSON>c này", "@notCompleteStep": {"type": "text", "placeholders": {}}, "part": "ph<PERSON>n", "@part": {"type": "text", "placeholders": {}}, "failToFetchSupportRequest": "failed to fetch support requests", "@failToFetchSupportRequest": {"type": "text", "placeholders": {}}, "expiredGift": "<PERSON><PERSON><PERSON> hết hạn", "@expiredGift": {"type": "text", "placeholders": {}}, "successReceiveGift": "<PERSON><PERSON><PERSON><PERSON> quà thành công", "@successReceiveGift": {"type": "text", "placeholders": {}}, "receiveGift": "<PERSON><PERSON><PERSON><PERSON> quà", "@receiveGift": {"type": "text", "placeholders": {}}, "wantToReceiveGift": "Bạn muốn nhận phần quà này?", "@wantToReceiveGift": {"type": "text", "placeholders": {}}, "scanVoucher": "<PERSON><PERSON><PERSON>", "@scanVoucher": {"type": "text", "placeholders": {}}, "giveVoucher": "Tặng Voucher", "@giveVoucher": {"type": "text", "placeholders": {}}, "rank": "Hạng", "@rank": {"type": "text", "placeholders": {}}, "schedulerNow": "Đặt lịch ngay", "@schedulerNow": {"type": "text", "placeholders": {}}, "readAllMessage": "Bạn có chắc chắn muốn đánh dấu đã đọc tất cả thông báo?", "@readAllMessage": {"type": "text", "placeholders": {}}, "font": "Fonts", "@font": {"type": "text", "placeholders": {}}, "haveNotAppointment": "Bạn chưa có lịch hẹn nào", "@haveNotAppointment": {"type": "text", "placeholders": {}}, "choiceSchedule": "<PERSON><PERSON> lòng chọn Đặt lịch để tạo lịch hẹn mới.", "@choiceSchedule": {"type": "text", "placeholders": {}}, "receivePromotion": "<PERSON><PERSON><PERSON><PERSON> đãi", "@receivePromotion": {"type": "text", "placeholders": {}}, "notAllowedToExchangeGift": "<PERSON><PERSON><PERSON> kh<PERSON>ch không đủ điều kiện đổi quà", "@notEnoughPointForExchangeGift": {"type": "text", "placeholders": {}}, "beautyNews": "<PERSON> tức làm đẹp", "@beautyNews": {"type": "text", "placeholders": {}}, "expertTitle": "Chuyên gia", "@expertTitle": {"type": "text", "placeholders": {}}, "mainExpertTitle": "Video", "@mainExpertTitle": {"type": "text", "placeholders": {}}, "follow": "<PERSON>", "@follow": {"type": "text", "placeholders": {}}, "unfollow": "Bỏ theo dõi", "@unfollow": {"type": "text", "placeholders": {}}, "expertPart": "<PERSON><PERSON><PERSON> ch<PERSON>n gia", "@expertPart": {"type": "text", "placeholders": {}}, "hotVideos": "<PERSON> nổi bật", "@hotVideos": {"type": "text", "placeholders": {}}, "viewResponse": "<PERSON><PERSON> h<PERSON>", "@viewResponse": {"type": "text", "placeholders": {}}, "comment": "<PERSON><PERSON><PERSON> lu<PERSON>", "@comment": {"type": "text", "placeholders": {}}, "cannotLoadVideo": "<PERSON><PERSON><PERSON><PERSON> thể hiển thị video", "@cannotLoadVideo": {"type": "text", "placeholders": {}}, "addComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "@addComment": {"type": "text", "placeholders": {}}, "noComment": "<PERSON><PERSON><PERSON><PERSON> có bình luận", "@noComment": {"type": "text", "placeholders": {}}, "codeCheckIn": "Mã check in", "@codeCheckIn": {"type": "text", "placeholders": {}}, "code": "Mã số", "@code": {"type": "text", "placeholders": {}}, "checkin": "Check in", "@checkin": {"type": "text", "placeholders": {}}, "survey": "<PERSON><PERSON><PERSON><PERSON>", "@survey": {"type": "text", "placeholders": {}}, "noMoreVideo": "Chưa có video mới", "@noMoreVideo": {"type": "text", "placeholders": {}}, "systemBranch": "<PERSON><PERSON> thống chi nh<PERSON>h", "@systemBranch": {"type": "text", "placeholders": {}}, "hotline": "Hotline", "@hotline": {"type": "text", "placeholders": {}}, "choiceDistrict": "<PERSON><PERSON><PERSON>/Huyện", "@choiceDistrict": {"type": "text", "placeholders": {}}, "choiceWard": "Chọn Phường/Xã", "@choiceWard": {"type": "text", "placeholders": {}}, "noPhone": "<PERSON><PERSON><PERSON><PERSON> có số điện thoại", "@noPhone": {"type": "text", "placeholders": {}}, "noCreateAppointment": "<PERSON><PERSON><PERSON> tại không thể đặt lịch", "@noCreateAppointment": {"type": "text", "placeholders": {}}, "newsCare": "<PERSON><PERSON><PERSON> sóc sau làm", "@newsCare": {"type": "text", "placeholders": {}}, "emptyBranch": "<PERSON><PERSON><PERSON><PERSON> có chi nhánh", "@emptyBranch": {"type": "text", "placeholders": {}}, "connectUs": "<PERSON><PERSON><PERSON> n<PERSON>i với chúng tôi:", "@connectUs": {"type": "text", "placeholders": {}}, "surveyMsg": "BẠN QUAN TÂM DỊCH VỤ NÀO CỦA The Advance ?", "@surveyMsg": {"type": "text", "placeholders": {}}, "emailLogin": "<PERSON><PERSON>", "@emailLogin": {"type": "text", "placeholders": {}}, "password": "<PERSON><PERSON><PERSON>", "@password": {"type": "text", "placeholders": {}}, "login": "<PERSON><PERSON><PERSON>", "@login": {"type": "text", "placeholders": {}}, "exitAccount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "@exitAccount": {"type": "text", "placeholders": {}}, "ignore": "Bỏ qua", "@ignore": {"type": "text", "placeholders": {}}, "contentTouchId": "Sử dụng dấu vân tay để mở khóa ứng dụng một cách nhanh chóng và tiện lợi", "@contentTouchId": {"type": "text", "placeholders": {}}, "titleTouchId": "Mở khóa bằng vân tay", "@titleTouchId": {"type": "text", "placeholders": {}}, "turnOn": "<PERSON><PERSON><PERSON>", "@turnOn": {"type": "text", "placeholders": {}}, "turnOff": "Tắt", "@turnOff": {"type": "text", "placeholders": {}}, "loginTouchId": "<PERSON><PERSON><PERSON> nhập bằng vân tay", "@loginTouchId": {"type": "text", "placeholders": {}}, "localizeReasonTouchId": "<PERSON><PERSON>t vân tay của bạn để xác thực", "@localizeReasonTouchId": {"type": "text", "placeholders": {}}, "fingerprintRequired": "<PERSON><PERSON><PERSON> cầu đăng kí vân tay", "@fingerprintRequired": {"type": "text", "placeholders": {}}, "iOSLockOut": "Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.", "@iOSLockOut": {"type": "text", "placeholders": {}}, "androidFingerprintSuccess": "<PERSON><PERSON><PERSON><PERSON> dạng thành công", "@androidFingerprintSuccess": {"type": "text", "placeholders": {}}, "androidFingerprintNotRecognized": "Nhận dạng thái bại! Xin thử lại!", "@androidFingerprintNotRecognized": {"type": "text", "placeholders": {}}, "events": "<PERSON><PERSON> kiện", "@events": {"type": "text", "placeholders": {}}, "noTicketSaved": "<PERSON><PERSON><PERSON>ng có mã tài sản mới cần lưu !", "@noTicketSaved": {"type": "text", "placeholders": {}}, "dailyInventory": "<PERSON><PERSON><PERSON> kê thiết bị hằng ngày", "@dailyInventory": {"type": "text", "placeholders": {}}, "normalInventory": "<PERSON><PERSON><PERSON> kê thông thường", "@normalInventory": {"type": "text", "placeholders": {}}, "searchInfoSuccess": "<PERSON><PERSON><PERSON> thông tin thành công", "@searchInfoSuccess": {"type": "text", "placeholders": {}}, "searchInfoFailure": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin", "@searchInfoFailure": {"type": "text", "placeholders": {}}, "cantInputManual": "<PERSON><PERSON><PERSON> sản này không đ<PERSON><PERSON><PERSON> nh<PERSON>y, vui lòng dùng chức năng <PERSON> Q<PERSON>ode", "@cantInputManual": {"type": "text", "placeholders": {}}, "inventoryTicket": "<PERSON><PERSON><PERSON> kiểm kê", "@inventoryTicket": {"type": "text", "placeholders": {}}, "inventory": "<PERSON><PERSON><PERSON>", "@inventory": {"type": "text", "placeholders": {}}, "agency": "<PERSON> n<PERSON>h", "@agency": {"type": "text", "placeholders": {}}, "accountLogin": "<PERSON><PERSON><PERSON>n đ<PERSON>ng nh<PERSON>p", "@accountLogin": {"type": "text", "placeholders": {}}, "hello": "<PERSON><PERSON> ch<PERSON>o", "@hello": {"type": "text", "placeholders": {}}, "emailEmpty": "<PERSON><PERSON> kh<PERSON>ng được bỏ trống", "@emailEmpty": {"type": "text", "placeholders": {}}, "passwordHaveWhiteSpace": "<PERSON><PERSON><PERSON> kh<PERSON>u không đ<PERSON><PERSON><PERSON> chứa khoảng trắng", "@passwordHaveWhiteSpace": {"type": "text", "placeholders": {}}, "passEmpty": "<PERSON><PERSON><PERSON> khẩu không được bỏ trống", "@passEmpty": {"type": "text", "placeholders": {}}, "passwordWrongFormat": "<PERSON><PERSON>t khẩu ít nhất phải là 6 ký tự", "@passwordWrongFormat": {"type": "text", "placeholders": {}}, "loginWithFingerprint": "<PERSON><PERSON><PERSON> nhập bằng vân tay", "@loginWithFingerprint": {"type": "text", "placeholders": {}}, "logoutAccount": "THOÁT TÀI KHOẢN", "@logoutAccount": {"type": "text", "placeholders": {}}, "countTicket": "<PERSON><PERSON> p<PERSON>", "@countTicket": {"type": "text", "placeholders": {}}, "department": "<PERSON><PERSON> phận", "@department": {"type": "text", "placeholders": {}}, "validateFormInventory": "<PERSON>ui lòng hoàn thành tất cả thông tin", "@validateFormInventory": {"type": "text", "placeholders": {}}, "needInventory": "<PERSON><PERSON><PERSON> ki<PERSON>m kê", "@needInventory": {"type": "text", "placeholders": {}}, "inventoried": "<PERSON><PERSON> kiểm kê", "@inventoried": {"type": "text", "placeholders": {}}, "scanQRCode": "Quét QR Code", "@scanQRCode": {"type": "text", "placeholders": {}}, "inputBarcode": "<PERSON><PERSON><PERSON><PERSON> tay", "@inputBarcode": {"type": "text", "placeholders": {}}, "assetBarcode": "Mã code tài sản", "@assetBarcode": {"type": "text", "placeholders": {}}, "complete": "<PERSON><PERSON><PERSON> t<PERSON>t", "@complete": {"type": "text", "placeholders": {}}, "completeThisAssetInventory": "<PERSON>à<PERSON> sản đã kiểm kê trước đó", "@completeThisAssetInventory": {"type": "text", "placeholders": {}}, "completedInventory": "<PERSON><PERSON><PERSON> kê thành công", "@completedInventory": {"type": "text", "placeholders": {}}, "notFindInventory": "<PERSON><PERSON><PERSON> sản không thuộc bộ phận hiện tại !", "@notFindInventory": {"type": "text", "placeholders": {}}, "ok": "OK", "@ok": {"type": "text", "placeholders": {}}, "staff": "Nhân viên", "@staff": {"type": "text", "placeholders": {}}, "staffInventory": "<PERSON><PERSON><PERSON> viên kiểm kê", "@staffInventory": {"type": "text", "placeholders": {}}, "staffAccountant": "<PERSON><PERSON><PERSON> viên kế toán", "@staffAccountant": {"type": "text", "placeholders": {}}, "staffOther": "Nhân viên kh<PERSON>c", "@staffOther": {"type": "text", "placeholders": {}}, "listInventory": "<PERSON><PERSON> s<PERSON>ch kiểm kê", "@listInventory": {"type": "text", "placeholders": {}}, "createNewInventory": "<PERSON><PERSON><PERSON> phiếu kiểm kê mới", "@createNewInventory": {"type": "text", "placeholders": {}}, "search": "<PERSON><PERSON><PERSON>", "@search": {"type": "text", "placeholders": {}}, "listIsEmpty": "<PERSON><PERSON> s<PERSON>ch tr<PERSON>ng", "@listIsEmpty": {"type": "text", "placeholders": {}}, "filterBy": "<PERSON><PERSON><PERSON> theo", "@filterBy": {"type": "text", "placeholders": {}}, "filter": "LỌC", "@filter": {"type": "text", "placeholders": {}}, "fromDay": "<PERSON><PERSON> ngày", "@fromDay": {"type": "text", "placeholders": {}}, "toDay": "<PERSON><PERSON><PERSON>", "@toDay": {"type": "text", "placeholders": {}}, "checkIn": "<PERSON><PERSON><PERSON> công vào", "@checkIn": {"type": "text", "placeholders": {}}, "checkOut": "<PERSON><PERSON>m công ra", "@checkOut": {"type": "text", "placeholders": {}}, "unmap": "unmap", "@unmap": {"type": "text", "placeholders": {}}, "ticketCode": "Mã check in", "@ticketCode": {"type": "text", "placeholders": {}}, "voucherCheckIn": "Voucher check in", "@voucherCheckIn": {"type": "text", "placeholders": {}}, "scan": "<PERSON><PERSON><PERSON>", "@scan": {"type": "text", "placeholders": {}}, "reward": "Trao thưởng", "@reward": {"type": "text", "placeholders": {}}, "rewardTitle": "Voucher nhận quà", "@rewardTitle": {"type": "text", "placeholders": {}}, "confirmCheckInMessage": "Bạn có chắc chắn check in cho khách hàng này?", "@confirmCheckInMessage": {"type": "text", "placeholders": {}}, "confirmCheckOutMessage": "Bạn có chắc chắn check out cho khách hàng này?", "@confirmCheckOutMessage": {"type": "text", "placeholders": {}}, "confirmRewardMessage": "Bạn có chắc chắn trao quà cho khách hàng này?", "@confirmRewardMessage": {"type": "text", "placeholders": {}}, "confirmUnmapVoucherMessage": "Bạn có chắc chắn hủy voucher của khách hàng này?", "@confirmUnmapVoucherMessage": {"type": "text", "placeholders": {}}, "ticketCodeIsEmpty": "Mã check in kh<PERSON>ng đư<PERSON>c để trống", "@ticketCodeIsEmpty": {"type": "text", "placeholders": {}}, "voucherCodeIsEmpty": "Mã voucher không đư<PERSON><PERSON> để trống", "@voucherCodeIsEmpty": {"type": "text", "placeholders": {}}, "rankIsEmpty": "Giải thưởng không được để trống", "@rankIsEmpty": {"type": "text", "placeholders": {}}, "politePrefix": "<PERSON><PERSON> lòng nh<PERSON>p", "@politePrefix": {"type": "text", "placeholders": {}}, "yearOfBirth": "<PERSON><PERSON><PERSON>h", "@yearOfBirth": {"type": "text", "placeholders": {}}, "voucherCheckInIsEmptywarningText": "<PERSON><PERSON><PERSON> nh<PERSON>p voucher check in", "@voucherCheckInIsEmptywarningText": {"type": "text", "placeholders": {}}, "createTicket": "TẠO TICKET", "@createTicket": {"type": "text", "placeholders": {}}, "allowLocation": "<PERSON> phép vị trí", "@allowLocation": {"type": "text", "placeholders": {}}, "getStarted": "<PERSON>ắt đầu khám phá", "@getStarted": {"type": "text", "placeholders": {}}, "accountEmpty": "<PERSON><PERSON><PERSON> điền tài khoản đăng nhập", "@accountEmpty": {"type": "text", "placeholders": {}}, "branchEmpty": "<PERSON><PERSON><PERSON> ch<PERSON>n chi nh<PERSON>h", "@branchEmpty": {"type": "text", "placeholders": {}}, "approval": "<PERSON><PERSON>", "@approval": {"type": "text", "placeholders": {}}, "waiting": "<PERSON><PERSON> chờ", "@waiting": {"type": "text", "placeholders": {}}, "approved": "Đ<PERSON>", "@approved": {"type": "text", "placeholders": {}}, "rejected": "Đ<PERSON> từ chối", "@rejected": {"type": "text", "placeholders": {}}, "myApproval": "<PERSON><PERSON> của tôi", "@myApproval": {"type": "text", "placeholders": {}}, "staffCode": "Mã nhân viên", "@staffCode": {"type": "text", "placeholders": {}}, "staffName": "<PERSON><PERSON><PERSON> nhân viên", "@staffName": {"type": "text", "placeholders": {}}, "sendDate": "<PERSON><PERSON><PERSON>", "@sendDate": {"type": "text", "placeholders": {}}, "editCheckIn": "<PERSON>ày chỉnh công", "@editCheckIn": {"type": "text", "placeholders": {}}, "totalCheckIn": "<PERSON><PERSON><PERSON> ng<PERSON>y công", "@totalCheckIn": {"type": "text", "placeholders": {}}, "checkInTime": "Giờ check - in", "@checkInTime": {"type": "text", "placeholders": {}}, "checkOutTime": "<PERSON><PERSON><PERSON> check out", "@checkOutTime": {"type": "text", "placeholders": {}}, "typeUpdateCheckIn": "<PERSON><PERSON><PERSON> công cập nh<PERSON>t", "@typeUpdateCheckIn": {"type": "text", "placeholders": {}}, "hour": "giờ", "@hour": {"type": "text", "placeholders": {}}, "sendAWish": "g<PERSON>i lời ch<PERSON>c", "@sendAWish": {"type": "text", "placeholders": {}}, "waitingApprove": "ch<PERSON>", "@waitingApprove": {"type": "text", "placeholders": {}}, "mine": "c<PERSON>a tôi", "@mine": {"type": "text", "placeholders": {}}, "reject": "<PERSON><PERSON> chối", "@reject": {"type": "text", "placeholders": {}}, "emptyHistoryApproval": "<PERSON><PERSON><PERSON> c<PERSON>u không có lịch sử nào?", "@emptyHistoryApproval": {"type": "text", "placeholders": {}}, "reason": "Lý do", "@reason": {"type": "text", "placeholders": {}}, "hintReason": "<PERSON><PERSON><PERSON><PERSON> nội dung ...", "@hintReason": {"type": "text", "placeholders": {}}, "choice": "<PERSON><PERSON><PERSON> theo", "@choice": {"type": "text", "placeholders": {}}, "typeAsset": "Loại tài sán", "@typeAsset": {"type": "text", "placeholders": {}}, "nameAsset": "<PERSON><PERSON><PERSON> tài s<PERSON>", "@nameAsset": {"type": "text", "placeholders": {}}, "price": "Giá", "@price": {"type": "text", "placeholders": {}}, "recommendationList": "DANH SÁCH GỢI Ý", "@recommendationList": {"type": "text", "placeholders": {}}, "recentSearch": "TÌM KIẾM GẦN ĐÂY", "@recentSearch": {"type": "text", "placeholders": {}}, "chat": "Chats", "@chat": {"type": "text", "placeholders": {}}, "skype": "Skype", "@skype": {"type": "text", "placeholders": {}}, "contract": "<PERSON><PERSON><PERSON> đồng lao động", "@contract": {"type": "text", "placeholders": {}}, "position": "<PERSON><PERSON><PERSON> v<PERSON>", "@position": {"type": "text", "placeholders": {}}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "@status": {"type": "text", "placeholders": {}}, "reportTo": "Báo cáo cho", "@reportTo": {"type": "text", "placeholders": {}}, "group": "Nhóm", "@group": {"type": "text", "placeholders": {}}, "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "@gender": {"type": "text", "placeholders": {}}, "dob": "<PERSON><PERSON><PERSON>", "@dob": {"type": "text", "placeholders": {}}, "form": "<PERSON><PERSON><PERSON>", "@form": {"type": "text", "placeholders": {}}, "totalSalary": "TỔNG LƯƠNG", "@totalSalary": {"type": "text", "placeholders": {}}, "ctt": "<PERSON><PERSON><PERSON> thực tế", "@ctt": {"type": "text", "placeholders": {}}, "timekeeper": "<PERSON><PERSON><PERSON> ch<PERSON>m công", "@timekeeper": {"type": "text", "placeholders": {}}, "takingCheckinPhoto": "<PERSON><PERSON><PERSON> check-in", "@takingCheckinPhoto": {"type": "text", "placeholders": {}}, "wordkedDay": "<PERSON><PERSON><PERSON> t<PERSON> l<PERSON>", "@wordkedDay": {"type": "text", "placeholders": {}}, "payrollRate": "Tỉ lệ nhận lương", "@payrollRate": {"type": "text", "placeholders": {}}, "annualLeave": "<PERSON><PERSON><PERSON>", "@annualLeave": {"type": "text", "placeholders": {}}, "unusedAnnualLeave": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> còn lại", "@unusedAnnualLeave": {"type": "text", "placeholders": {}}, "timekeepingOverview": "<PERSON><PERSON><PERSON> hợp chấm công", "@timekeepingOverview": {"type": "text", "placeholders": {}}, "day": "ng<PERSON>y", "@day": {"type": "text", "placeholders": {}}, "salaryDetail": "<PERSON><PERSON><PERSON> công chi tiết", "@salaryDetail": {"type": "text", "placeholders": {}}, "month": "th<PERSON>g", "@month": {"type": "text", "placeholders": {}}, "year": "năm", "@year": {"type": "text", "placeholders": {}}, "totalWorkInDay": "T<PERSON>ng công trong ngày", "@totalWorkInDay": {"type": "text", "placeholders": {}}, "timeCheckIn": "Giờ vào", "@timeCheckIn": {"type": "text", "placeholders": {}}, "timeCheckOut": "Giờ ra", "@timeCheckOut": {"type": "text", "placeholders": {}}, "createUpdateWorkRequest": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u sửa công", "@createUpdateWorkRequest": {"type": "text", "placeholders": {}}, "workType": "<PERSON><PERSON><PERSON> c<PERSON>ng", "@workType": {"type": "text", "placeholders": {}}, "totalWork": "<PERSON><PERSON><PERSON> công", "@totalWork": {"type": "text", "placeholders": {}}, "request": "<PERSON><PERSON><PERSON> c<PERSON>u", "@request": {"type": "text", "placeholders": {}}, "daysOff": "nghỉ phép", "@daysOff": {"type": "text", "placeholders": {}}, "daysOffLeft": "<PERSON><PERSON><PERSON> p<PERSON> còn", "@daysOffLeft": {"type": "text", "placeholders": {}}, "business": "công tác", "@business": {"type": "text", "placeholders": {}}, "purpose": "m<PERSON><PERSON>", "@purpose": {"type": "text", "placeholders": {}}, "mth": "THG", "@mth": {"type": "text", "placeholders": {}}, "areYouSureSigningOut": "Bạn có chắc chắn thoát tài k<PERSON>n?", "@areYouSureSigningOut": {"type": "text", "placeholders": {}}, "workingProgress": "<PERSON><PERSON><PERSON> trình làm việc", "@workingProgress": {"type": "text", "placeholders": {}}, "connectionErrorPleaseRefresh": "Lỗi kết n<PERSON>, vui lòng tải lại trang", "@connectionErrorPleaseRefresh": {"type": "text", "placeholders": {}}, "refresh": "<PERSON><PERSON><PERSON> l<PERSON>i", "@refresh": {"type": "text", "placeholders": {}}, "prize": "Giải thưởng", "@prize": {"type": "text", "placeholders": {}}, "check": "<PERSON><PERSON><PERSON> tra", "@check": {"type": "text", "placeholders": {}}, "change": "<PERSON><PERSON> đ<PERSON>i", "@change": {"type": "text", "placeholders": {}}, "noRouteDefinedYet": "<PERSON><PERSON><PERSON> có đường dẫn", "@noRouteDefinedYet": {"type": "text", "placeholders": {}}, "otpCodeIsNotEnoughNumbers": "<PERSON>ã x<PERSON>c thực ch<PERSON>a đủ 4 số", "@otpCodeIsNotEnoughNumbers": {"type": "text", "placeholders": {}}, "statistic": "<PERSON><PERSON><PERSON><PERSON> kê", "@statistic": {"type": "text", "placeholders": {}}, "inputPhoneReceiver": "<PERSON><PERSON><PERSON><PERSON> số điện thoại người nhận", "@inputPhoneReceiver": {"type": "text", "placeholders": {}}, "noMessage": "<PERSON><PERSON><PERSON><PERSON> có tin nhắn", "@noMessage": {"type": "text", "placeholders": {}}, "confirmSendImage": "Bạn có chắc muốn gửi <PERSON>nh này ?", "@confirmSendImage": {"type": "text", "placeholders": {}}, "donotJob": "Chưa xử lí", "@donotJob": {"type": "text", "placeholders": {}}, "expiredJob": "<PERSON><PERSON><PERSON> h<PERSON>n", "@expiredJob": {"type": "text", "placeholders": {}}, "doneJob": "Đã xử lí", "@doneJob": {"type": "text", "placeholders": {}}, "createTicketSuccess": "<PERSON><PERSON><PERSON> phi<PERSON>u thành công", "@createTicketSuccess": {"type": "text", "placeholders": {}}, "validateInventoryType": "<PERSON><PERSON> lựa chọn hình thức kiểm kê", "@validateInventoryType": {"type": "text", "placeholders": {}}, "inputOTP": "<PERSON>hậ<PERSON> mã OTP", "@inputOTP": {"type": "text", "placeholders": {}}, "isReceiveOTP": "Bạn chưa nhận đượ<PERSON>?", "@isReceiveOTP": {"type": "text", "placeholders": {}}, "forgetPassword": "<PERSON>uên mật khẩu?", "@forgetPassword": {"type": "text", "placeholders": {}}, "inputPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "@inputPassword": {"type": "text", "placeholders": {}}, "setPassword": "Đặt mật khẩu", "@setPassword": {"type": "text", "placeholders": {}}, "total": "Tổng", "@total": {"type": "text", "placeholders": {}}, "selectMonth": "<PERSON><PERSON><PERSON>g", "@selectMonth": {"type": "text", "placeholders": {}}, "goToWorkLate": "đi mu<PERSON>n", "@goToWorkLate": {"type": "text", "placeholders": {}}, "offWork": "nghỉ làm", "@offWork": {"type": "text", "placeholders": {}}, "timeAttendance": "<PERSON><PERSON><PERSON> công", "@timeAttendance": {"type": "text", "placeholders": {}}, "updateCheckin": "<PERSON><PERSON><PERSON> nh<PERSON>t công", "@updateCheckin": {"type": "text", "placeholders": {}}, "fetchHistoryCheckinFailed": "<PERSON><PERSON> lỗi tải dữ liệu chấm công", "@fetchHistoryCheckinFailed": {"type": "text", "placeholders": {}}, "fetchWorkTypeFailed": "<PERSON><PERSON> lỗi tải danh sách loại công", "@fetchWorkTypeFailed": {"type": "text", "placeholders": {}}, "requestUpdateCheckinFailed": "<PERSON><PERSON> lỗi yêu cầu chỉnh sửa công", "@requestUpdateCheckinFailed": {"type": "text", "placeholders": {}}, "expiredDate": "<PERSON><PERSON><PERSON>", "@expiredDate": {"type": "text", "placeholders": {}}, "detailJob": "<PERSON> tiết công việc", "@detailJob": {"type": "text", "placeholders": {}}, "from": "Từ", "@from": {"type": "text", "placeholders": {}}, "attachedFile": "<PERSON> đ<PERSON> k<PERSON>m", "@attachedFile": {"type": "text", "placeholders": {}}, "conservation": "<PERSON><PERSON><PERSON><PERSON>", "@conservation": {"type": "text", "placeholders": {}}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"type": "text", "placeholders": {}}, "asignee": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "@asignee": {"type": "text", "placeholders": {}}, "sendFeedBack": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi", "@sendFeedBack": {"type": "text", "placeholders": {}}, "fileAttach": "<PERSON><PERSON><PERSON> k<PERSON> file", "@fileAttach": {"type": "text", "placeholders": {}}, "sendFeedBackSuccess": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi thành công", "@sendFeedBackSuccess": {"type": "text", "placeholders": {}}, "checkinFailure": "Check in thất bại", "@checkinFailure": {"type": "text", "placeholders": {}}, "tryAgain": "<PERSON><PERSON><PERSON> lại", "@tryAgain": {"type": "text", "placeholders": {}}, "timekeeping": "<PERSON><PERSON><PERSON> công", "@timekeeping": {"type": "text", "placeholders": {}}, "checkinSuccess": "Check in thành công", "@checkinSuccess": {"type": "text", "placeholders": {}}, "getIn": "Vào", "@getIn": {"type": "text", "placeholders": {}}, "more": "<PERSON><PERSON><PERSON><PERSON>", "@more": {"type": "text", "placeholders": {}}, "open": "Mở", "@open": {"type": "text", "placeholders": {}}, "defaultError": "Đã có lỗi xảy ra", "@defaultError": {"type": "text", "placeholders": {}}, "defaultText": "Mặc định", "@defaultText": {"type": "text", "placeholders": {}}, "creatingWork": "<PERSON><PERSON><PERSON> c<PERSON>ng vi<PERSON>c", "@creatingWork": {"type": "text", "placeholders": {}}, "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "@title": {"type": "text", "placeholders": {}}, "dateExpired": "<PERSON><PERSON><PERSON>", "@dateExpired": {"type": "text", "placeholders": {}}, "prioritize": "Ưu tiên", "@prioritize": {"type": "text", "placeholders": {}}, "creatWork": "Tự tạo việc", "@creatWork": {"type": "text", "placeholders": {}}, "choiceOffice": "<PERSON><PERSON><PERSON> phòng ban", "@choiceOffice": {"type": "text", "placeholders": {}}, "create": "Tạo", "@create": {"type": "text", "placeholders": {}}, "yourChoice": "Bạn đã chọn", "@yourChoice": {"type": "text", "placeholders": {}}, "findByName": "<PERSON><PERSON><PERSON> theo tên", "@findByName": {"type": "text", "placeholders": {}}, "choiceMember": "<PERSON><PERSON><PERSON> thành viên", "@choiceMember": {"type": "text", "placeholders": {}}, "choiceAll": "<PERSON><PERSON><PERSON> tất cả", "@choiceAll": {"type": "text", "placeholders": {}}, "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "@confirmPassword": {"type": "text", "placeholders": {}}, "wrongConfirmPass": "<PERSON><PERSON><PERSON> kh<PERSON>u xác nhận không đúng", "@wrongConfirmPass": {"type": "text", "placeholders": {}}, "loginBy": "<PERSON><PERSON><PERSON> nhập bằng", "@loginBy": {"type": "text", "placeholders": {}}, "loginByFingerPrint": "vân tay cho nh<PERSON>ng lần sau", "@loginByFingerPrint": {"type": "text", "placeholders": {}}, "loginByFaceID": "Face ID cho nh<PERSON><PERSON> lần sau", "@loginByFaceID": {"type": "text", "placeholders": {}}, "asset": "<PERSON><PERSON><PERSON>", "@asset": {"type": "text", "placeholders": {}}, "socialInsurance": "<PERSON><PERSON><PERSON> xã hội", "@socialInsurance": {"type": "text", "placeholders": {}}, "laborContract": "<PERSON><PERSON><PERSON> đồng lao động", "@laborContract": {"type": "text", "placeholders": {}}, "indentify": "Số CCCD", "@indentify": {"type": "text", "placeholders": {}}, "birthday": "<PERSON><PERSON><PERSON> th<PERSON>g n<PERSON>m sinh", "@birthday": {"type": "text", "placeholders": {}}, "numberBank": "Số tài k<PERSON>n ngân hàng", "@numberBank": {"type": "text", "placeholders": {}}, "company": "<PERSON><PERSON>ng ty", "@company": {"type": "text", "placeholders": {}}, "positionMember": "<PERSON><PERSON><PERSON> danh", "@positionMember": {"type": "text", "placeholders": {}}, "office": "Phòng ban", "@office": {"type": "text", "placeholders": {}}, "productCode": "<PERSON><PERSON> hàng hóa", "@productCode": {"type": "text", "placeholders": {}}, "assetCode": "<PERSON><PERSON> tài sản", "@assetCode": {"type": "text", "placeholders": {}}, "productName": "<PERSON><PERSON><PERSON> hàng h<PERSON>a", "@productName": {"type": "text", "placeholders": {}}, "activeDate": "<PERSON><PERSON><PERSON>", "@activeDate": {"type": "text", "placeholders": {}}, "employeePart": "Phần ng<PERSON><PERSON>i lao động", "@employeePart": {"type": "text", "placeholders": {}}, "getImageInCamera": "<PERSON><PERSON><PERSON> mới", "@getImageInCamera": {"type": "text", "placeholders": {}}, "getImageInGallery": "<PERSON><PERSON><PERSON>nh có sẵn", "@getImageInGallery": {"type": "text", "placeholders": {}}, "avatar": "Ảnh đại diện", "@avatar": {"type": "text", "placeholders": {}}, "titleNoti": "<PERSON> tức", "@titleNoti": {"type": "text", "placeholders": {}}, "notSupportDevice": "<PERSON><PERSON><PERSON><PERSON> bị của bạn không cài đặt mở khóa khuôn mặt và vân tay", "@notSupportDevice": {"type": "text", "placeholders": {}}, "approvalTitle": "<PERSON><PERSON><PERSON><PERSON>", "@approvalTitle": {"type": "text", "placeholders": {}}, "rejectReason": "<PERSON>ý do từ chối", "@rejectReason": {"type": "text", "placeholders": {}}, "typeApprove": "<PERSON><PERSON><PERSON>", "@typeApprove": {"type": "text", "placeholders": {}}, "signal": "<PERSON><PERSON> ký", "@signal": {"type": "text", "placeholders": {}}, "otp": "OTP", "@otp": {"type": "text", "placeholders": {}}, "sendEform": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "@sendEform": {"type": "text", "placeholders": {}}, "detailEform": "<PERSON> tiết yêu cầu", "@detailEform": {"type": "text", "placeholders": {}}, "sender": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i", "@sender": {"type": "text", "placeholders": {}}, "time": "<PERSON><PERSON><PERSON><PERSON> gian", "@time": {"type": "text", "placeholders": {}}, "loginFaceID": "<PERSON><PERSON><PERSON> nhập bằng khuôn mặt", "@loginFaceID": {"type": "text", "placeholders": {}}, "creatWorkSuccess": "Bạn đã tạo công việc thành công", "@creatWorkSuccess": {"type": "text", "placeholders": {}}, "authenRequired": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c thực", "@authenRequired": {"type": "text", "placeholders": {}}, "authenToLogin": "<PERSON><PERSON> lòng xác thực để đăng nhập", "@authenToLogin": {"type": "text", "placeholders": {}}, "repeatWork": "<PERSON><PERSON><PERSON> việc lặp lại", "@repeatWork": {"type": "text", "placeholders": {}}, "repeatWorkTitle": "<PERSON><PERSON><PERSON> thức lặp lại", "@repeatWorkTitle": {"type": "text", "placeholders": {}}, "msgDoneTask": "<PERSON><PERSON><PERSON> thành công việc", "@msgDoneTask": {"type": "text", "placeholders": {}}, "infoAccount": "Thông tin tài k<PERSON>n", "@infoAccount": {"type": "text", "placeholders": {}}, "mainScreen": "<PERSON><PERSON><PERSON> h<PERSON>", "@mainScreen": {"type": "text", "placeholders": {}}, "pickHistoryCheckinDate": "<PERSON><PERSON><PERSON> chấm công", "@pickHistoryCheckinDate": {"type": "text", "placeholders": {}}, "content": "<PERSON><PERSON>i dung", "@content": {"type": "text", "placeholders": {}}, "image": "Ảnh", "@image": {"type": "text", "placeholders": {}}, "language": "<PERSON><PERSON><PERSON>", "@language": {"type": "text", "placeholders": {}}, "biometricVerification": "<PERSON><PERSON><PERSON> thực sinh trắc học", "@biometricVerification": {"type": "text", "placeholders": {}}, "turnOnFaceID": "<PERSON><PERSON><PERSON> t<PERSON>h năng mở bằng khuôn mặt", "@turnOnFaceID": {"type": "text", "placeholders": {}}, "turnOffFaceID": "T<PERSON>t t<PERSON>h năng mở bằng khuôn mặt", "@turnOffFaceID": {"type": "text", "placeholders": {}}, "turnOnTouchID": "<PERSON><PERSON><PERSON> t<PERSON>h năng mở bằng vân tay", "@turnOnTouchID": {"type": "text", "placeholders": {}}, "turnOffTouchID": "T<PERSON>t tính năng mở bằng vân tay", "@turnOffTouchID": {"type": "text", "placeholders": {}}, "setUpBiometrics": "<PERSON><PERSON> lòng thiết lập mở khóa vân tay hoặc khuôn mặt", "@setUpBiometrics": {"type": "text", "placeholders": {}}, "vnLanguage": "Tiếng <PERSON>", "@vnLanguage": {"type": "text", "placeholders": {}}, "enLanguage": "English", "@enLanguage": {"type": "text", "placeholders": {}}, "infoCustomer": "Thông tin khách hàng", "@infoCustomer": {"type": "text", "placeholders": {}}, "job": "<PERSON><PERSON><PERSON>", "@job": {"type": "text", "placeholders": {}}, "pathological": "<PERSON><PERSON><PERSON> lý kh<PERSON>ch hàng", "@pathological": {"type": "text", "placeholders": {}}, "expression": "<PERSON><PERSON><PERSON><PERSON> biểu hiện c<PERSON>a <PERSON>", "@expression": {"type": "text", "placeholders": {}}, "otherExpression": "<PERSON><PERSON><PERSON><PERSON>", "@otherExpression": {"type": "text", "placeholders": {}}, "infoCustomerBrief": "Thông tin KH", "@infoCustomerBrief": {"type": "text", "placeholders": {}}, "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "@task": {"type": "text", "placeholders": {}}, "sendRequest": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "@sendRequest": {"type": "text", "placeholders": {}}, "typeEform": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "@typeEform": {"type": "text", "placeholders": {}}, "start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "@start": {"type": "text", "placeholders": {}}, "collaboratorNews": "<PERSON> tức", "@collaboratorNews": {"type": "text", "placeholders": {}}, "alwayEnableNotificationForNews": "<PERSON><PERSON><PERSON> bật thông báo để thông tin tới quý khách tin tức hàng ngày.", "@alwayEnableNotificationForNews": {"type": "text", "placeholders": {}}, "taskInformation": "Thông tin công việc", "@taskInformation": {"type": "text", "placeholders": {}}, "checkinManagement": "<PERSON><PERSON><PERSON><PERSON> lý chấm công", "@checkinManagement": {"type": "text", "placeholders": {}}, "efficientCheckinManagement": "<PERSON><PERSON><PERSON><PERSON> lý chấm công ch<PERSON>, d<PERSON> dàng", "@efficientCheckinManagement": {"type": "text", "placeholders": {}}, "checkinMonthDetail": "<PERSON> tiết công tháng {month}", "@checkinMonthDetail": {"placeholders": {"month": {"type": "int?", "example": "1"}}}, "assetHistory": "<PERSON><PERSON><PERSON> sử tài sản", "@assetHistory": {"type": "text", "placeholders": {}}, "transfer": "<PERSON><PERSON><PERSON><PERSON>", "@transfer": {"type": "text", "placeholders": {}}, "maintenance": "<PERSON><PERSON><PERSON> trì", "@maintenance": {"type": "text", "placeholders": {}}, "jobType": "<PERSON><PERSON><PERSON> công vi<PERSON>c", "@jobType": {"type": "text", "placeholders": {}}, "requirementEmployee": "NV yêu cầu", "@requirementEmployee": {"type": "text", "placeholders": {}}, "maintenanceEmployee": "NV bảo trì", "@maintenanceEmployee": {"type": "text", "placeholders": {}}, "completionDate": "<PERSON><PERSON><PERSON> ho<PERSON>n thành", "@completionDate": {"type": "text", "placeholders": {}}, "createNewGroup": "Tạo nhóm mới", "@createNewGroup": {"type": "text", "placeholders": {}}, "updateSuccess": "Bạn đã cập nhật thành công", "@updateSuccess": {"type": "text", "placeholders": {}}, "createSuccess": "Bạn đã tạo thành công", "@createSuccess": {"type": "text", "placeholders": {}}, "copyLink": "<PERSON>o chép đường dẫn", "@copyLink": {"type": "text", "placeholders": {}}, "checkinReminder": "Nhắc nhở chấm công", "@checkinReminder": {"type": "text", "placeholders": {}}, "removeUserConfirmation": "Bạn có muốn xóa tài khoản này?", "@removeUserConfirmation": {"type": "text", "placeholders": {}}, "removeUserAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "@removeUserAccount": {"type": "text", "placeholders": {}}, "removeUserAccountMessage": "- <PERSON><PERSON><PERSON>ê<PERSON>, <PERSON><PERSON> đại diện cùng thông tin tài khoản\n- <PERSON><PERSON><PERSON> tất cả nhật ký tin nhắn và lịch sử gọi\n- Tất cả dữ liệu sẽ bị xóa vĩnh viễn không thể khôi phục", "@removeUserAccountMessage": {"type": "text", "placeholders": {}}, "specification": "<PERSON><PERSON><PERSON> h<PERSON>nh", "@specification": {"type": "text", "placeholders": {}}, "incomingCount": "<PERSON><PERSON> lần đến", "@incomingCount": {"type": "text", "placeholders": {}}, "averageRevenue": "DT bình quân", "@averageRevenue": {"type": "text", "placeholders": {}}, "totalCost": "Tổng tiền đã chi", "@totalCost": {"type": "text", "placeholders": {}}, "cardBalance": "Số dư thẻ", "@cardBalance": {"type": "text", "placeholders": {}}, "paid": "<PERSON><PERSON> thanh toán", "@paid": {"type": "text", "placeholders": {}}, "payBack": "<PERSON><PERSON><PERSON> ti<PERSON>n", "@payBack": {"type": "text", "placeholders": {}}, "loan": "<PERSON><PERSON><PERSON> tiền nợ", "@loan": {"type": "text", "placeholders": {}}, "submittedCost": "<PERSON><PERSON><PERSON> ti<PERSON>n n<PERSON>p", "@submittedCost": {"type": "text", "placeholders": {}}, "payWithCard": "Thanh toán bằng thẻ", "@payWithCard": {"type": "text", "placeholders": {}}, "pickFloor": "Chọn tầng", "@pickFloor": {"type": "text", "placeholders": {}}, "pickProvince": "Chọn tỉnh thành", "@pickProvince": {"type": "text", "placeholders": {}}, "pickBranchAndFloor": "<PERSON><PERSON><PERSON> chi <PERSON>, tầng", "@pickBranchAndFloor": {"type": "text", "placeholders": {}}, "goBack": "Trở về", "@goBack": {"type": "text", "placeholders": {}}, "pickBranchFloorAndBed": "<PERSON><PERSON><PERSON> t<PERSON>, <PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON>", "@pickBranchFloorAndBed": {"type": "text", "placeholders": {}}, "pickBed": "<PERSON><PERSON><PERSON>", "@pickBed": {"type": "text", "placeholders": {}}, "pickRoom": "<PERSON><PERSON><PERSON> phòng", "@pickRoom": {"type": "text", "placeholders": {}}, "customerList": "<PERSON>h sách khách hàng", "@customerList": {"type": "text", "placeholders": {}}, "selectCustomerBranchNote": "<PERSON><PERSON> lòng chọn chi <PERSON>, tầng để xem danh sách khách hàng!", "@selectCustomerBranchNote": {"type": "text", "placeholders": {}}, "choose": "Chọn", "@choose": {"type": "text", "placeholders": {}}, "consultationFile": "<PERSON><PERSON> sơ tư vấn", "@consultationFile": {"type": "text", "placeholders": {}}, "consultationManager": "<PERSON><PERSON><PERSON><PERSON> lý tư vấn", "@consultationAgent": {"type": "text", "placeholders": {}}, "consultationAgent": "<PERSON><PERSON><PERSON> viên tư vấn", "consultationCharge": "<PERSON><PERSON><PERSON><PERSON> nhận tư vấn", "@consultationCharge": {"type": "text", "placeholders": {}}, "doctorCharge": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "@doctorCharge": {"type": "text", "placeholders": {}}, "consultationContent": "<PERSON><PERSON><PERSON> dung tư vấn", "@consultationContent": {"type": "text", "placeholders": {}}, "exploitPlan": "<PERSON><PERSON> hoạch khai thác", "@exploitPlan": {"type": "text", "placeholders": {}}, "botReport": "B<PERSON>o bot", "@botReport": {"type": "text", "placeholders": {}}, "chooseBed": "<PERSON><PERSON><PERSON>", "@chooseBed": {"type": "text", "placeholders": {}}, "out": "<PERSON> về", "@out": {"type": "text", "placeholders": {}}, "treatmentFile": "<PERSON><PERSON> sơ điều trị", "@treatmentFile": {"type": "text", "placeholders": {}}, "searchDepartment": "T<PERSON>m theo tên phòng", "@searchDepartment": {"type": "text", "placeholders": {}}, "searchService": "<PERSON><PERSON><PERSON> theo tên dịch vụ", "@searchService": {"type": "text", "placeholders": {}}, "treatmentDetail": "<PERSON> tiết điều trị", "@treatmentDetail": {"type": "text", "placeholders": {}}, "treatmentService": "<PERSON><PERSON><PERSON> vụ điều trị", "@treatmentService": {"type": "text", "placeholders": {}}, "useCount": "SL sử dụng", "@useCount": {"type": "text", "placeholders": {}}, "reExaminationDate": "<PERSON><PERSON><PERSON> t<PERSON> k<PERSON>m", "@reExaminationDate": {"type": "text", "placeholders": {}}, "attendingDoctor": "<PERSON><PERSON><PERSON> sĩ điều trị", "@attendingDoctor": {"type": "text", "placeholders": {}}, "agentIncharge": "NV phụ trách", "@agentIncharge": {"type": "text", "placeholders": {}}, "treatment": "điều trị", "@treatment": {"type": "text", "placeholders": {}}, "picture": "<PERSON><PERSON><PERSON> ảnh", "@picture": {"type": "text", "placeholders": {}}, "medicalPrescription": "toa thu<PERSON><PERSON>", "@medicalPrescription": {"type": "text", "placeholders": {}}, "morning": "sáng", "@morning": {"type": "text", "placeholders": {}}, "noon": "trưa", "@noon": {"type": "text", "placeholders": {}}, "afternoon": "chiều", "@afternoon": {"type": "text", "placeholders": {}}, "night": "tô<PERSON>i", "@night": {"type": "text", "placeholders": {}}, "medicineDetail": "<PERSON> tiết thuốc", "@medicineDetail": {"type": "text", "placeholders": {}}, "searchCustomer": "<PERSON><PERSON><PERSON> h<PERSON>ng", "@searchCustomer": {"type": "text", "placeholders": {}}, "listCustomer": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "@listCustomer": {"type": "text", "placeholders": {}}, "returnBed": "<PERSON><PERSON><PERSON> gi<PERSON>", "@returnBed": {"type": "text", "placeholders": {}}, "changeBed": "Đổi giườ<PERSON>", "@changeBed": {"type": "text", "placeholders": {}}, "picking": "Bạn đang chọn", "@picking": {"type": "text", "placeholders": {}}, "floor": "<PERSON><PERSON><PERSON>", "@floor": {"type": "text", "placeholders": {}}, "product": "Sản phẩm", "@product": {"type": "text", "placeholders": {}}, "serviceAndProduct": "Dịch vụ/Sản phẩm", "@serviceAndProduct": {"type": "text", "placeholders": {}}, "findService": "Tìm theo tên dịch vụ", "@findService": {"type": "text", "placeholders": {}}, "findProduct": "Tìm theo tên sản phẩm", "@findProduct": {"type": "text", "placeholders": {}}, "buyCount": "SL Mua", "@buyCount": {"type": "text", "placeholders": {}}, "existCount": "SL Còn", "@existCount": {"type": "text", "placeholders": {}}, "totalMoney": "Tổng tiền", "@totalMoney": {"type": "text", "placeholders": {}}, "detailService": "Chi tiết dịch vụ", "@detailService": {"type": "text", "placeholders": {}}, "handle": "<PERSON><PERSON> lý", "@handle": {"type": "text", "placeholders": {}}, "importantNote": "<PERSON><PERSON> chú quan trọng", "@importantNote": {"type": "text", "placeholders": {}}, "createNew": "<PERSON><PERSON><PERSON> mới", "@createNew": {"type": "text", "placeholders": {}}, "firstName": "<PERSON><PERSON><PERSON>", "@firstName": {"type": "text", "placeholders": {}}, "requestStoragePermission": "<PERSON> phép truy cập tệp tin", "@requestStoragePermission": {"type": "text", "placeholders": {}}, "storageRequest1": "<PERSON><PERSON><PERSON> trữ tệp tải về", "@storageRequest1": {"type": "text", "placeholders": {}}, "cameraRequest1": "<PERSON><PERSON><PERSON> hình cập nhật thông tin cá nhân", "@cameraRequestExplain1": {"type": "text", "placeholders": {}}, "cameraRequest2": "Quét mã code dễ dàng", "@cameraRequestExplain2": {"type": "text", "placeholders": {}}, "microPermissionRequest": "<PERSON> phép ghi âm thiết bị", "@microPermissionRequest": {"type": "text", "placeholders": {}}, "microPermissionSettings": "<PERSON>n cho phép quyền ghi âm trong cài đặt thiết bị", "@microPermissionSettings": {"type": "text", "placeholders": {}}, "record": "<PERSON><PERSON> <PERSON><PERSON>", "@record": {"type": "text", "placeholders": {}}, "microRequest1": "<PERSON><PERSON><PERSON>", "@microRequest1": {"type": "text", "placeholders": {}}, "microRequest2": "<PERSON><PERSON><PERSON> ghi âm hỗ trợ", "@microRequest2": {"type": "text", "placeholders": {}}, "photoRequest1": "<PERSON><PERSON><PERSON> nh<PERSON>t <PERSON>nh thông tin cá nhân", "@photoRequest1": {"type": "text", "placeholders": {}}, "onTheNextScreen": "ở màn hình tiếp theo để:", "@onTheNextScreen": {"type": "text", "placeholders": {}}, "changeSettingsLater": "Bạn có thể thay đổi quyền này sau ở trong mục Cài đặt của ứng dụng", "@changeSettingsLater": {"type": "text", "placeholders": {}}, "notificationRequest1": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> tin tức, d<PERSON><PERSON> v<PERSON> mới nhất", "@notificationRequest1": {"type": "text", "placeholders": {}}, "employeeInfoShort": "TT nhân viên", "@employeeInfoShort": {"type": "text", "placeholders": {}}, "performEmployee": "<PERSON><PERSON><PERSON> thực hiện", "@performEmployee": {"type": "text", "placeholders": {}}, "caredQL": "QL đã chăm sóc", "@caredQL": {"type": "text", "placeholders": {}}, "pleaseSelect": "<PERSON><PERSON> lòng ch<PERSON>n", "@pleaseSelect": {"type": "text", "placeholders": {}}, "dateCountReturn": "<PERSON><PERSON>i khám sau", "@dateCountReturn": {"type": "text", "placeholders": {}}, "takeEmployee": "<PERSON><PERSON> thự<PERSON> hi<PERSON>n", "@takeEmployee": {"type": "text", "placeholders": {}}, "adviseEmployee": "NV tư vấn", "@adviseEmployee": {"type": "text", "placeholders": {}}, "takeDoctor": "<PERSON><PERSON><PERSON> s<PERSON> thực hiện", "@takeDoctor": {"type": "text", "placeholders": {}}, "medicine": "<PERSON><PERSON><PERSON><PERSON>", "@medicine": {"type": "text", "placeholders": {}}, "dosage": "<PERSON><PERSON><PERSON>", "@dosage": {"type": "text", "placeholders": {}}, "point": "<PERSON><PERSON><PERSON><PERSON>", "@point": {"type": "text", "placeholders": {}}, "avgPointByOthers": "<PERSON><PERSON><PERSON><PERSON> trung bình (ngư<PERSON>i khác đ<PERSON>h giá)", "@avgPointByOthers": {"type": "text", "placeholders": {}}, "khacnho": "Khấc nhỏ", "@khacnho": {"type": "text", "placeholders": {}}, "differentGap": "<PERSON><PERSON><PERSON>", "@differentGap": {"type": "text", "placeholders": {}}, "reExaminateAfter": "<PERSON><PERSON>i khám sau", "@reExaminateAfter": {"type": "text", "placeholders": {}}, "machine": "<PERSON><PERSON><PERSON>", "@machine": {"type": "text", "placeholders": {}}, "spotsise": "Spotsise", "@spotsise": {"type": "text", "placeholders": {}}, "originStatus": "TTBD", "@originStatus": {"type": "text", "placeholders": {}}, "inkColor": "<PERSON><PERSON><PERSON>", "@inkColor": {"type": "text", "placeholders": {}}, "prescriptionTemplate": "<PERSON><PERSON> thu<PERSON>c mẫu", "@prescriptionTemplate": {"type": "text", "placeholders": {}}, "pleaseSelectPrescriptionTemplate": "<PERSON><PERSON> lòng chọn toa thuốc mẫu", "@pleaseSelectPrescriptionTemplate": {"type": "text", "placeholders": {}}, "list": "<PERSON><PERSON>", "@list": {"type": "text", "placeholders": {}}, "diagnosis": "<PERSON><PERSON><PERSON> đ<PERSON>", "@diagnosis": {"type": "text", "placeholders": {}}, "advice": "Lời dặn", "@advice": {"type": "text", "placeholders": {}}, "prescriber": "<PERSON><PERSON><PERSON><PERSON> kê toa", "@prescriber": {"type": "text", "placeholders": {}}, "usage": "<PERSON><PERSON><PERSON> d<PERSON>", "@usage": {"type": "text", "placeholders": {}}, "unit": "Đơn vị t<PERSON>h", "@unit": {"type": "text", "placeholders": {}}, "addMedicine": "<PERSON><PERSON><PERSON><PERSON> thu<PERSON>c mới", "@addMedicine": {"type": "text", "placeholders": {}}, "addMedicineForms": "<PERSON><PERSON><PERSON><PERSON> thuốc theo mẫu", "@addMedicineForms": {"type": "text", "placeholders": {}}, "addContent": "<PERSON><PERSON><PERSON><PERSON> nội dung...", "@addContent": {"type": "text", "placeholders": {}}, "action": "<PERSON><PERSON>", "@action": {"type": "text", "placeholders": {}}, "netFare": "Đơn giá", "@netFare": {"type": "text", "placeholders": {}}, "discount": "G<PERSON>ảm giá", "@discount": {"type": "text", "placeholders": {}}, "finalCost": "<PERSON><PERSON><PERSON><PERSON> tiền", "@finalCost": {"type": "text", "placeholders": {}}, "trackingTransparency": "<PERSON>n phé<PERSON> được theo dõi hành vi người dùng ở màn hình kế tiếp để:", "@trackingTransparency": {"type": "text", "placeholders": {}}, "locationRequest1": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> tin tứ<PERSON>, d<PERSON><PERSON> v<PERSON> ch<PERSON>h x<PERSON>c h<PERSON>n", "@locationRequest1": {"type": "text", "placeholders": {}}, "checkedOut": "Đã về", "@checkedOut": {"type": "text", "placeholders": {}}, "orderFood": "Đặt cơm", "@orderFood": {"type": "text", "placeholders": {}}, "morningSession": "Sáng", "@morningSession": {"type": "text", "placeholders": {}}, "noonSession": "Trưa", "@noonSession": {"type": "text", "placeholders": {}}, "afternoonSession": "<PERSON><PERSON><PERSON>", "@afternoonSession": {"type": "text", "placeholders": {}}, "update": "<PERSON><PERSON><PERSON>", "@update": {"type": "text", "placeholders": {}}, "success": "<PERSON><PERSON><PERSON><PERSON> công", "@success": {"type": "text", "placeholders": {}}, "orderFoodUpdateSuccess": "<PERSON><PERSON><PERSON> nhật đặt cơm thành công", "@orderFoodUpdateSuccess": {"type": "text", "placeholders": {}}, "overAmount": "Số lượng sử dụng đã quá số lượng còn lại", "@overAmount": {"type": "text", "placeholders": {}}, "monday": "Thứ 2", "@monday": {"type": "text", "placeholders": {}}, "tuesday": "Thứ 3", "@tuesday": {"type": "text", "placeholders": {}}, "wednesday": "Thứ 4", "@wednesday": {"type": "text", "placeholders": {}}, "thursday": "Thứ 5", "@thursday": {"type": "text", "placeholders": {}}, "friday": "Thứ 6", "@friday": {"type": "text", "placeholders": {}}, "saturday": "Thứ 7", "@saturday": {"type": "text", "placeholders": {}}, "sunDay": "Chủ Nhậ<PERSON>", "@sunDay": {"type": "text", "placeholders": {}}, "nation": "Quốc gia", "@nation": {"type": "text", "placeholders": {}}, "visitReason": "<PERSON><PERSON><PERSON><PERSON> từ đâu ?", "@visitReason": {"type": "text", "placeholders": {}}, "introducer": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "@introducer": {"type": "text", "placeholders": {}}, "firstVisit": "<PERSON><PERSON><PERSON> đ<PERSON> đ<PERSON>n", "@firstVisit": {"type": "text", "placeholders": {}}, "lastVisit": "<PERSON><PERSON><PERSON> cu<PERSON> đ<PERSON>n", "@lastVisit": {"type": "text", "placeholders": {}}, "scheduleInfo": "Thông tin đặt hẹn", "@scheduleInfo": {"type": "text", "placeholders": {}}, "customerCharacteristics": "Đặc điểm của khách hàng", "@customerCharacteristics": {"type": "text", "placeholders": {}}, "currentWeek": "<PERSON><PERSON><PERSON> hi<PERSON>n tại", "@currentWeek": {"type": "text", "placeholders": {}}, "optionQuestion": "Bạn muốn thực hiện thao tác?", "@optionQuestion": {"type": "text", "placeholders": {}}, "assignTask": "Gán CV", "@assignTask": {"type": "text", "placeholders": {}}, "bookingSchedule": "<PERSON><PERSON><PERSON> đặt hẹn", "@bookingSchedule": {"type": "text", "placeholders": {}}, "information": "Thông tin", "@information": {"type": "text", "placeholders": {}}, "bookedService": "DV đã đặt hẹn", "@bookedService": {"type": "text", "placeholders": {}}, "noVisit": "<PERSON><PERSON> lần đến", "@noVisit": {"type": "text", "placeholders": {}}, "firstVisited": "<PERSON><PERSON><PERSON> đầu đến", "@firstVisited": {"type": "text", "placeholders": {}}, "lastVisited": "<PERSON><PERSON>n gần đ<PERSON>y", "@lastVisited": {"type": "text", "placeholders": {}}, "balanceMoney": "Số dư thẻ", "@balanceMoney": {"type": "text", "placeholders": {}}, "lastMoney": "<PERSON> gần đây", "@lastMoney": {"type": "text", "placeholders": {}}, "totalSpent": "Tổng chi", "@totalSpent": {"type": "text", "placeholders": {}}, "avgRevenue": "DT bình quân", "@avgRevenue": {"type": "text", "placeholders": {}}, "takeCareEmpName": "<PERSON><PERSON>ân viên ch<PERSON>m s<PERSON>c", "@takeCareEmpName": {"type": "text", "placeholders": {}}, "used": "Đã sử dụng", "@used": {"type": "text", "placeholders": {}}, "unUsed": "Chưa sử dụng", "@unUsed": {"type": "text", "placeholders": {}}, "customerHaveBook": "<PERSON><PERSON><PERSON><PERSON> hàng đặt lịch hẹn", "@customerHaveBook": {"type": "text", "placeholders": {}}, "noCheckin": "<PERSON><PERSON><PERSON>", "@noCheckin": {"type": "text", "placeholders": {}}, "hasCheckin": "<PERSON><PERSON> đến", "@hasCheckin": {"type": "text", "placeholders": {}}, "beingTakeCare": "<PERSON><PERSON> ch<PERSON> s<PERSON>c", "@beingTakeCare": {"type": "text", "placeholders": {}}, "today": "<PERSON><PERSON><PERSON> nay", "@today": {"type": "text", "placeholders": {}}, "tomorrow": "<PERSON><PERSON><PERSON> mai", "@tomorrow": {"type": "text", "placeholders": {}}, "treatmentAvailable": "<PERSON><PERSON><PERSON> liệu trình", "@treatmentAvailable": {"type": "text", "placeholders": {}}, "notAssignTask": "Chưa gán CV", "@notAssignTask": {"type": "text", "placeholders": {}}, "assignedTask": "Đã gán CV", "@assignedTask": {"type": "text", "placeholders": {}}, "customerAssign": "{total} <PERSON><PERSON> ch<PERSON>n", "@customerAssign": {"type": "text", "placeholders": {"total": {"type": "String"}}}, "deleteAssignTaskMessage": "Bạn có chắc muốn hủy công việc đã gán?", "@deleteAssignTaskMessage": {"type": "text", "placeholders": {}}, "sendFailure": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "@sendFailure": {"type": "text", "placeholders": {}}, "sendNoteSuccess": "Bạn đã gửi thông tin thành công", "@sendNoteSuccess": {"type": "text", "placeholders": {}}, "input": "<PERSON><PERSON><PERSON><PERSON>", "@input": {"type": "text", "placeholders": {}}, "bookingService": "<PERSON><PERSON><PERSON> vụ đặt hẹn", "@bookingService": {"type": "text", "placeholders": {}}, "bookingNote": "<PERSON><PERSON> chú đặt hẹn", "@bookingNote": {"type": "text", "placeholders": {}}, "pasteToConsultation": "<PERSON><PERSON> vào nội dung tư vấn", "@pasteToConsultation": {"type": "text", "placeholders": {}}, "printTicket": "In phiếu", "@printTicket": {"type": "text", "placeholders": {}}, "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "@noData": {"type": "text", "placeholders": {}}, "pullToLoadMore": "<PERSON><PERSON><PERSON> để tải thêm", "@pullToLoadMore": {"type": "text", "placeholders": {}}, "checkinList": "<PERSON><PERSON> s<PERSON> check-in", "@checkinList": {"type": "text", "placeholders": {}}, "notAssigned": "Chưa gán", "@notAssigned": {"type": "text", "placeholders": {}}, "assigned": "Đã gán", "@assigned": {"type": "text", "placeholders": {}}, "perform": "<PERSON><PERSON><PERSON><PERSON>", "@perform": {"type": "text", "placeholders": {}}, "taskList": "<PERSON><PERSON> s<PERSON>ch công vi<PERSON>c", "@taskList": {"type": "text", "placeholders": {}}, "collectInformation": "<PERSON>hu thập thông tin", "@collectInformation": {"type": "text", "placeholders": {}}, "boughtServices": "{count} d<PERSON><PERSON> v<PERSON> đã mua", "@boughtServices": {"placeholders": {"count": {"type": "int", "example": "2"}}}, "assignWork": "<PERSON><PERSON> c<PERSON> vi<PERSON>c", "@assignWork": {"type": "text", "placeholders": {}}, "selecting": "{count} đ<PERSON> ch<PERSON>n", "@selecting": {"placeholders": {"count": {"type": "int", "example": "2"}}}, "onlyAssignMax": "Chỉ đư<PERSON><PERSON> gán tối đa {count} nhân viên", "@onlyAssignMax": {"placeholders": {"count": {"type": "int", "example": "2"}}}, "select": "<PERSON><PERSON><PERSON>", "@select": {"type": "text", "placeholders": {}}, "confirmFinishTask": "Bạn chắc chắn đã hoàn tất công việc?", "@confirmFinishTask": {"type": "text", "placeholders": {}}, "tutorial": "Hướng dẫn", "@tutorial": {"type": "text", "placeholders": {}}, "takePictureBefore": "<PERSON><PERSON><PERSON> hình tr<PERSON><PERSON><PERSON> làm", "@takePictureBefore": {"type": "text", "placeholders": {}}, "takePictureAfter": "<PERSON><PERSON><PERSON> hình sau làm", "@takePictureAfter": {"type": "text", "placeholders": {}}, "warningFinish": "<PERSON><PERSON> lòng thực hiện đầy đủ các công việc để hoàn tất", "@warningFinish": {"type": "text", "placeholders": {}}, "warningFinish2": "<PERSON><PERSON> lòng thực hiện ít nhất một công việc để hoàn tất", "@warningFinish2": {"type": "text", "placeholders": {}}, "beforeDone": "<PERSON><PERSON><PERSON><PERSON><PERSON> là<PERSON>", "@beforeDone": {"type": "text", "placeholders": {}}, "afterDone": "Sau làm", "@afterDone": {"type": "text", "placeholders": {}}, "attachImages": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>", "@attachImages": {"type": "text", "placeholders": {}}, "customerCompleteServiceConfirm": "Bạn có chắc chắn khách hàng đã hoàn tất dịch vụ?", "@customerCompleteServiceConfirm": {"type": "text", "placeholders": {}}, "searchEmployee": "<PERSON><PERSON><PERSON> theo tên, mã nhân viên ...", "@searchEmployee": {"type": "text", "placeholders": {}}, "requiredSelectRoom": "<PERSON>ui lòng chọn phòng", "@requiredSelectRoom": {"type": "text", "placeholders": {}}, "getLocationSuccessful": "<PERSON><PERSON><PERSON> thông tin vị trí hoàn tất", "@getLocationSuccessful": {"type": "text", "placeholders": {}}, "microRequest3": "<PERSON><PERSON> thập cải thiện chất l<PERSON><PERSON><PERSON> dịch vụ", "@microRequest3": {"type": "text", "placeholders": {}}, "province": "Tỉnh/Thành phố", "@province": {"type": "text", "placeholders": {}}, "district": "Quận/Huyện", "@district": {"type": "text", "placeholders": {}}, "ward": "Phường/Xã", "@ward": {"type": "text", "placeholders": {}}, "male": "Nam", "@male": {"type": "text", "placeholders": {}}, "female": "<PERSON><PERSON>", "@female": {"type": "text", "placeholders": {}}, "selectGender": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "@selectGender": {"type": "text", "placeholders": {}}, "selectJob": "<PERSON><PERSON><PERSON> ngh<PERSON> nghiệp", "@selectJob": {"type": "text", "placeholders": {}}, "branch": "<PERSON> n<PERSON>h", "@branch": {"type": "text", "placeholders": {}}, "addCustomer": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "@addCustomer": {"type": "text", "placeholders": {}}, "addCustomerSuccess": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "@addCustomerSuccess": {"type": "text", "placeholders": {}}, "addNew": "<PERSON><PERSON><PERSON><PERSON> mới", "@addNew": {"type": "text", "placeholders": {}}, "endRecord": "<PERSON><PERSON><PERSON> th<PERSON>", "@endRecord": {"type": "text", "placeholders": {}}, "recordWarning": "<PERSON><PERSON><PERSON> hình tiếp theo sẽ thực hiện ghi âm vui lòng xác nhận để tiếp tục", "@recordWarning": {"type": "text", "placeholders": {}}, "recordPerform": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON> ghi <PERSON>", "@recordPerform": {"type": "text", "placeholders": {}}, "delete": "Xóa", "@delete": {"type": "text", "placeholders": {}}, "requiredField": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "@requiredField": {"type": "text", "placeholders": {}}, "all": "<PERSON><PERSON><PERSON> c<PERSON>", "@all": {"type": "text", "placeholders": {}}, "nextService": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> d<PERSON> v<PERSON>c", "@nextService": {"type": "text", "placeholders": {}}, "changeRoom": "Chuyển phòng", "@changeRoom": {"type": "text", "placeholders": {}}, "ttbd": "TTBD", "@ttbd": {"type": "text", "placeholders": {}}, "selectDate": "<PERSON><PERSON><PERSON>", "@selectDate": {"type": "text", "placeholders": {}}, "selectContent": "<PERSON><PERSON><PERSON> n<PERSON>i dung", "@selectContent": {"type": "text", "placeholders": {}}, "searchCustomerPhone": "<PERSON><PERSON><PERSON> theo số điện thoại KH", "@searchCustomerPhone": {"type": "text", "placeholders": {}}, "manualCheckin": "<PERSON>in thủ công", "@manualCheckin": {"type": "text", "placeholders": {}}, "editCustomerInfo": "<PERSON><PERSON><PERSON> thông tin khách hàng", "@editCustomerInfo": {"type": "text", "placeholders": {}}, "room": "Phòng {roomCode}", "@room": {"type": "text", "placeholders": {"roomCode": {"type": "String", "example": "101"}}}, "pickStaff": "<PERSON><PERSON><PERSON> nhân viên", "@pickStaff": {"type": "text", "placeholders": {}}, "bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@bed": {"type": "text", "placeholders": {}}, "consultation": "<PERSON><PERSON> vấn", "@consultation": {"type": "text", "placeholders": {}}, "updateConsultation": "<PERSON><PERSON><PERSON> nhật tư vấn", "@updateConsultation": {"type": "text", "placeholders": {}}, "technical": "<PERSON><PERSON>", "@technical": {"type": "text", "placeholders": {}}, "consultationCost": "<PERSON><PERSON><PERSON><PERSON> nhận tư vấn", "@consultationCost": {"type": "text", "placeholders": {}}, "notificationBot": "<PERSON><PERSON><PERSON><PERSON> báo bot", "@notificationBot": {"type": "text", "placeholders": {}}, "affective": "<PERSON><PERSON><PERSON> quả", "@affective": {"type": "text", "placeholders": {}}, "nonAffective": "<PERSON><PERSON><PERSON><PERSON> hiệu quả", "@nonAffective": {"type": "text", "placeholders": {}}, "adviseContinuous": "<PERSON><PERSON> vấn tiếp", "minNetFareError": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> phép thấp hơn giá ban đầu", "@minNetFareError": {"type": "text", "placeholders": {}}, "supportConsultation": "Hỗ trợ tư vấn", "@supportConsultation": {"type": "text", "placeholders": {}}, "selfConsultation": "<PERSON>ự tư vấn", "@selfConsultation": {"type": "text", "placeholders": {}}, "selectConsultation": "<PERSON><PERSON><PERSON> hình thức tư vấn", "@selectConsultation": {"type": "text", "placeholders": {}}, "notConsultation": "<PERSON><PERSON><PERSON> tư vấn", "@notConsultation": {"type": "text", "placeholders": {}}, "didConsultation": "Đ<PERSON> tư vấn", "@didConsultation": {"type": "text", "placeholders": {}}, "recept": "<PERSON><PERSON><PERSON><PERSON>", "@recept": {"type": "text", "placeholders": {}}, "evaluationPeriodList": "<PERSON><PERSON> s<PERSON>ch kỳ đánh giá", "@evaluationPeriodList": {"type": "text", "placeholders": {}}, "noEvaluationPeriodAvailable": "Chưa tới kỳ đánh giá", "@noEvaluationPeriodAvailable": {"type": "text", "placeholders": {}}, "evaluationResult": "<PERSON><PERSON><PERSON> quả đ<PERSON>h giá", "@evaluationResult": {"type": "text", "placeholders": {}}, "evaluation": "Đánh giá", "@evaluation": {"type": "text", "placeholders": {}}, "completeRating": "<PERSON><PERSON>n thành đánh giá?", "@completeRating": {"type": "text", "placeholders": {}}, "questionList": "<PERSON><PERSON> s<PERSON>ch câu hỏi", "@questionList": {"type": "text", "placeholders": {}}, "previous": "Quay lại", "@previous": {"type": "text", "placeholders": {}}, "questionNumber": "Câu hỏi", "@questionNumber": {"type": "text", "placeholders": {}}, "consultationContentShort": "Nội dung TV", "@consultationContentShort": {"type": "text", "placeholders": {}}, "callLogs": "LS cuộc gọi", "@callLogs": {"type": "text", "placeholders": {}}, "call": "<PERSON><PERSON><PERSON> đ<PERSON>", "@call": {"type": "text", "placeholders": {}}, "messageLogs": "LS tin nhắn", "@messageLogs": {"type": "text", "placeholders": {}}, "bookingLogs": "LS đặt hẹn", "@bookingLogs": {"type": "text", "placeholders": {}}, "comeDateTime": "<PERSON><PERSON><PERSON> g<PERSON> đến", "@comeDateTime": {"type": "text", "placeholders": {}}, "bookSource": "Đặt từ", "@bookSource": {"type": "text", "placeholders": {}}, "copied": "Sao chép", "@copied": {"type": "text", "placeholders": {}}, "advisoryTicketStatus": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u ghi", "@advisoryTicketStatus": {"type": "text", "placeholders": {}}, "skillsNeedToImprove": "<PERSON><PERSON><PERSON> n<PERSON>ng lực c<PERSON>n c<PERSON>i thi<PERSON>n", "@skillsNeedToImprove": {"type": "text", "placeholders": {}}, "emptyEvaluationResult": "<PERSON><PERSON><PERSON> có kết quả đánh giá", "@emptyEvaluationResult": {"type": "text", "placeholders": {}}, "ratingSuccess": "<PERSON><PERSON><PERSON> đ<PERSON>h giá thành công", "@ratingSuccess": {"type": "text", "placeholders": {}}, "ratingFailure": "<PERSON><PERSON><PERSON><PERSON> lưu đ<PERSON> dữ liệu", "@ratingFailure": {"type": "text", "placeholders": {}}, "tabNew": "<PERSON><PERSON><PERSON>", "@tabNew": {"type": "text", "placeholders": {}}, "tabPending": "Pending", "@tabPending": {"type": "text", "placeholders": {}}, "tabSuccess": "Đã xử lý", "@tabSuccess": {"type": "text", "placeholders": {}}, "searchHint": "<PERSON><PERSON><PERSON> the<PERSON>,s<PERSON><PERSON>, ng<PERSON><PERSON>i", "@searchHint": {"type": "text", "placeholders": {}}, "ticketFilter": "<PERSON><PERSON><PERSON>hi", "@ticketFilter": {"type": "text", "placeholders": {}}, "statusTicket": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u ghi", "@statusTicket": {"type": "text", "placeholders": {}}, "dateFollow": "<PERSON><PERSON><PERSON> t<PERSON> t<PERSON>c", "@dateFollow": {"type": "text", "placeholders": {}}, "dateCreate": "<PERSON><PERSON><PERSON>", "@dateCreate": {"type": "text", "placeholders": {}}, "titleTimeInit": "- <PERSON><PERSON><PERSON> -", "@titleTimeInit": {"type": "text", "placeholders": {}}, "titleStatusInit": "- <PERSON><PERSON><PERSON> trạng thái -", "@titleStatusInit": {"type": "text", "placeholders": {}}, "isEnd": "- <PERSON><PERSON> hết -", "@isEnd": {"type": "text", "placeholders": {}}, "accessReject": "<PERSON>ừ chối truy cập", "@accessReject": {"type": "text", "placeholders": {}}, "booking": "Đặt hẹn", "@booking": {"type": "text", "placeholders": {}}, "fromPhone": "Số gọi ra", "@fromPhone": {"type": "text", "placeholders": {}}, "customerPhone": "<PERSON><PERSON> kh<PERSON>ch hàng", "@customerPhone": {"type": "text", "placeholders": {}}, "call2": "Gọi", "@call2": {"type": "text", "placeholders": {}}, "connecting": "<PERSON><PERSON> kết nối...", "@connecting": {"type": "text", "placeholders": {}}, "end": "<PERSON><PERSON><PERSON>", "@end": {"type": "text", "placeholders": {}}, "userTicket": "<PERSON><PERSON> s<PERSON>ch phi<PERSON>u ghi", "@userTicket": {"type": "text", "placeholders": {}}, "comeDate": "<PERSON><PERSON><PERSON>", "@comeDate": {"type": "text", "placeholders": {}}, "comeTime": "<PERSON><PERSON><PERSON><PERSON> gian đến", "@comeTime": {"type": "text", "placeholders": {}}, "totalBooking": "<PERSON><PERSON><PERSON> lịch đã đặt", "@totalBooking": {"type": "text", "placeholders": {}}, "detailService2": "DV chi tiết", "@detailService2": {"type": "text", "placeholders": {}}, "sendMethod": "<PERSON><PERSON><PERSON> th<PERSON> g<PERSON>i", "@sendMethod": {"type": "text", "placeholders": {}}, "sendRecordQuestion": "Bạn có muốn gửi ghi <PERSON>m ?", "@sendRecordQuestion": {"type": "text", "placeholders": {}}, "pleaseSelectDate": "<PERSON><PERSON> lòng chọn ngày đặt lịch", "@pleaseSelectDate": {"type": "text", "placeholders": {}}, "planSchedule": "<PERSON><PERSON><PERSON> k<PERSON> ho<PERSON>ch", "@planSchedule": {"type": "text", "placeholders": {}}, "sendSMS": "Gửi SMS", "@sendSMS": {"type": "text", "placeholders": {}}, "sendZNS": "<PERSON><PERSON><PERSON>", "@sendZNS": {"type": "text", "placeholders": {}}, "sendAppNoti": "Gửi Noti app", "@sendAppNoti": {"type": "text", "placeholders": {}}, "callTo": "<PERSON><PERSON><PERSON>", "@callTo": {"type": "text", "placeholders": {}}, "cancelAll": "<PERSON><PERSON><PERSON> tất cả", "@cancelAll": {"type": "text", "placeholders": {}}, "createBookingWarning": "Bạn có một lịch kế hoạch chưa đư<PERSON><PERSON> chọn. Bỏ qua?", "@createBookingWarning": {"type": "text", "placeholders": {}}, "bookingSuccess": "Đặt lịch thành công", "@bookingSuccess": {"type": "text", "placeholders": {}}, "serviceHasExist": "<PERSON><PERSON><PERSON> vụ đã tồn tại", "@serviceHasExist": {"type": "text", "placeholders": {}}, "consultationCreate": "<PERSON><PERSON><PERSON> tư vấn", "@consultationCreate": {"type": "text", "placeholders": {}}, "updateBookingSuccess": "<PERSON><PERSON> cập nhật lịch hẹn thành công", "@updateBookingSuccess": {"type": "text", "placeholders": {}}, "updateNDTVSuccess": "Cập nhật NDTV thành công", "@updateNDTVSuccess": {"type": "text", "placeholders": {}}, "selectProduct": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "@selectProduct": {"type": "text", "placeholders": {}}, "selectMethod": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức", "@selectMethod": {"type": "text", "placeholders": {}}, "selectUnit": "<PERSON><PERSON><PERSON> đơn vị t<PERSON>h", "@selectUnit": {"type": "text", "placeholders": {}}, "notificationType": "<PERSON><PERSON><PERSON> tin thông báo", "@notificationType": {"type": "text", "placeholders": {}}, "buocSong": "Bước sóng", "@buocSong": {"type": "text", "placeholders": {}}, "cacBuoc": "<PERSON><PERSON><PERSON>", "@cacBuoc": {"type": "text", "placeholders": {}}, "dauMay": "<PERSON><PERSON><PERSON>", "@dauMay": {"type": "text", "placeholders": {}}, "postsize": "Spotsize", "@postsize": {"type": "text", "placeholders": {}}, "shot": "Shot", "@shot": {"type": "text", "placeholders": {}}, "vung": "<PERSON><PERSON><PERSON>", "@vung": {"type": "text", "placeholders": {}}, "tanso": "<PERSON><PERSON><PERSON> s<PERSON>", "@tanso": {"type": "text", "placeholders": {}}, "nangluong": "<PERSON><PERSON><PERSON>", "@nangluong": {"type": "text", "placeholders": {}}, "energyRate": "<PERSON><PERSON><PERSON>", "@energyRate": {"type": "text", "placeholders": {}}, "specs": "<PERSON>h<PERSON><PERSON> số", "@specs": {"type": "text", "placeholders": {}}, "drinkProduct": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> u<PERSON>", "@drinkProduct": {"type": "text", "placeholders": {}}, "latherProduct": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> b<PERSON>i", "@latherProder": {"type": "text", "placeholders": {}}, "drinkAndLatherProductOfFirm": "<PERSON><PERSON>n phẩm bôi và uống của TMV", "@drinkAndLatherProductOfFirm": {"type": "text", "placeholders": {}}, "drinkAndLatherProductOfClient": "<PERSON><PERSON>n phẩm bôi và uống của KH", "@drinkAndLatherProductOfClient": {"type": "text", "placeholders": {}}, "sang": "<PERSON><PERSON><PERSON>", "@sang": {"type": "text", "placeholders": {}}, "trua": "Trưa", "@trua": {"type": "text", "placeholders": {}}, "chieu": "<PERSON><PERSON><PERSON>", "@chieu": {"type": "text", "placeholders": {}}, "search2": "<PERSON><PERSON><PERSON> theo từ khoá", "@search2": {"type": "text", "placeholders": {}}, "intro": "Hướng dẫn sử dụng", "@intro": {"type": "text", "placeholders": {}}, "selectLather": "<PERSON><PERSON><PERSON> sản ph<PERSON>m bôi", "@selectLather": {"type": "text", "placeholders": {}}, "selectDrink": "<PERSON><PERSON><PERSON> sản phẩm u<PERSON>ng", "@selectDrink": {"type": "text", "placeholders": {}}, "hintNotes": "<PERSON><PERSON><PERSON><PERSON> ghi chú...", "@hintNotes": {"type": "text", "placeholders": {}}, "hintTotal": "<PERSON><PERSON><PERSON><PERSON>ố l<PERSON>...", "@hintTotal": {"type": "text", "placeholders": {}}, "titleTotal": "Số lượng", "@titleTotal": {"type": "text", "placeholders": {}}, "selectWorking": "<PERSON><PERSON><PERSON> công vi<PERSON>c", "@selectWorking": {"type": "text", "placeholders": {}}, "nvtv": "NVTV", "@nvtv": {"type": "text", "placeholders": {}}, "nvth": "NVTH", "bsth": "BSTH", "sl": "SL", "@sl": {"type": "text", "placeholders": {}}, "times": "<PERSON><PERSON><PERSON>", "@times": {"type": "text", "placeholders": {}}, "devFeatures": "<PERSON><PERSON><PERSON> năng đang phát triển", "@devFeatures": {"type": "text", "placeholders": {}}, "reasonVisit": "<PERSON><PERSON><PERSON><PERSON> từ đâu ?", "@reasonVisit": {"type": "text", "placeholders": {}}, "titleDay": "<PERSON><PERSON><PERSON>", "@titleDay": {"type": "text", "placeholders": {}}, "setCheckinTime": "Cài đặt giờ chấm công vào", "@setCheckinTime": {"type": "text", "placeholders": {}}, "setCheckoutTime": "Cài đặt giờ chấm công ra", "@setCheckoutTime": {"type": "text", "placeholders": {}}, "setupCheckin": "<PERSON><PERSON>i đặt chấm công", "@setupCheckin": {"type": "text", "placeholders": {}}, "exampleNote": "ví dụ: Ăn no trước khi uống", "@exampleNote": {"type": "text", "placeholders": {}}, "technicalQL": "QL tay nghề", "@technicalQL": {"type": "text", "placeholders": {}}, "recordCheckingStaff": "NV kiểm tra hồ sơ", "@recordCheckingStaff": {"type": "text", "placeholders": {}}, "stopRecordConfirm": "Bạn muốn dừng ghi âm?", "@stopRecordConfirm": {"type": "text", "placeholders": {}}, "stopRecordSuccess": "<PERSON><PERSON><PERSON> mừng bạn đã hoàn tất ghi âm", "@stopRecordSuccess": {"type": "text", "placeholders": {}}, "printingProgress": "<PERSON><PERSON> lý: {value}%", "@printingProgress": {"type": "text", "placeholders": {"value": {"type": "String"}}}, "pleaseInputDescription": "<PERSON><PERSON> lòng nh<PERSON><PERSON> miêu tả", "@pleaseInputDescription": {"type": "text", "placeholders": {}}, "searchByCodeOrName": "T<PERSON><PERSON> theo mã KH hoặc tên KH", "@searchByCodeOrName": {"type": "text", "placeholders": {}}, "diary": "MXH", "@diary": {"type": "text", "placeholders": {}}, "explore": "Khám phá", "@explore": {"type": "text", "placeholders": {}}, "internal": "<PERSON><PERSON><PERSON> bộ", "@internal": {"type": "text", "placeholders": {}}, "recentlyChat": "vừa mới chat", "@recentlyChat": {"type": "text", "placeholders": {}}, "message": "<PERSON>", "@message": {"type": "text", "placeholders": {}}, "groupName": "<PERSON><PERSON><PERSON>", "@groupName": {"type": "text", "placeholders": {}}, "createGroup": "Tạo nhóm", "@createGroup": {"type": "text", "placeholders": {}}, "customer": "<PERSON><PERSON><PERSON><PERSON>", "@customer": {"type": "text", "placeholders": {}}, "timeAttendanceNow": "<PERSON><PERSON><PERSON> công ngay", "@timeAttendanceNow": {"type": "text", "placeholders": {}}, "cameraAgain": "<PERSON><PERSON><PERSON> l<PERSON>", "@cameraAgain": {"type": "text", "placeholders": {}}, "imageAttendance": "Ảnh chấm công", "@imageAttendance": {"type": "text", "placeholders": {}}, "inputReason": "<PERSON><PERSON>ậ<PERSON> lý do", "@inputReason": {"type": "text", "placeholders": {}}, "typeAction": "<PERSON><PERSON><PERSON> lo<PERSON>i thao tác", "@typeAction": {"type": "text", "placeholders": {}}, "dayApply": "<PERSON><PERSON><PERSON>", "@dayApply": {"type": "text", "placeholders": {}}, "fromDayToDay": "Từ ngày đến ngày", "@fromDayToDay": {"type": "text", "placeholders": {}}, "personInfo": "Thông tin tài k<PERSON>n", "@personInfo": {"type": "text", "placeholders": {}}, "bio": "Bio", "@bio": {"type": "text", "placeholders": {}}, "hintBio": "<PERSON><PERSON> tả bản thân", "@hintBio": {"type": "text", "placeholders": {}}, "nickName": "<PERSON><PERSON><PERSON>", "@nickName": {"type": "text", "placeholders": {}}, "hintNickName": "<PERSON><PERSON><PERSON> n<PERSON> nickname...", "@hintNickName": {"type": "text", "placeholders": {}}, "gallery": "Hình <PERSON>nh/Video", "@gallery": {"type": "text", "placeholders": {}}, "limitImage": "<PERSON><PERSON><PERSON> thêm <PERSON>nh cho phép", "@limitImage": {"type": "text", "placeholders": {}}, "changePermission": "cài đặt lại quyền", "@changePermission": {"type": "text", "placeholders": {}}, "limitImageIOS": "<PERSON><PERSON><PERSON> lại <PERSON>nh cho phép", "@limitImageIOS": {"type": "text", "placeholders": {}}, "previewImage": "<PERSON><PERSON><PERSON> nh<PERSON>t <PERSON>nh đ<PERSON>n", "@previewImage": {"type": "text", "placeholders": {}}, "andOthers": "và {count} ng<PERSON><PERSON><PERSON> k<PERSON>c", "@andOthers": {"type": "text", "placeholders": {"count": {"type": "int", "example": "2"}}}, "groupDetail": "<PERSON> tiết nh<PERSON>m", "@groupDetail": {"type": "text", "placeholders": {}}, "createGroupSuccess": "<PERSON><PERSON><PERSON> nhóm thành công", "@createGroupSuccess": {"type": "text", "placeholders": {}}, "msnv": "MSNV", "@msnv": {"type": "text", "placeholders": {}}, "requiredGroupName": "<PERSON><PERSON> lòng nhập tên nhóm", "@requiredGroupName": {"type": "text", "placeholders": {}}, "minuteAgo": "ph<PERSON><PERSON> tr<PERSON>", "@minuteAgo": {"type": "text", "placeholders": {}}, "hoursAgo": "giờ trước", "@hoursAgo": {"type": "text", "placeholders": {}}, "now": "<PERSON><PERSON> bây giờ", "@now": {"type": "text", "placeholders": {}}, "like": "<PERSON><PERSON><PERSON><PERSON>", "@like": {"type": "text", "placeholders": {}}, "reply": "<PERSON><PERSON><PERSON>", "@reply": {"type": "text", "placeholders": {}}, "boss": "<PERSON><PERSON><PERSON>", "@boss": {"type": "text", "placeholders": {}}, "eventsNews": "Event", "@eventsNews": {"type": "text", "placeholders": {}}, "viewLike": "<PERSON><PERSON> th<PERSON>ch", "@viewLike": {"type": "text", "placeholders": {}}, "titleLike": "<PERSON><PERSON><PERSON><PERSON>", "@titleLike": {"type": "text", "placeholders": {}}, "statusHint": "Hôm nay bạn thế nào?", "@statusHint": {"type": "text", "placeholders": {}}, "story": "<PERSON><PERSON><PERSON><PERSON> ký", "@story": {"type": "text", "placeholders": {}}, "messenger": "Nhắn tin", "@messenger": {"type": "text", "placeholders": {}}, "deleteGroup": "Xóa nhóm", "@deleteGroup": {"type": "text", "placeholders": {}}, "turnOffNoti": "<PERSON><PERSON><PERSON> thông báo", "@turnOffNoti": {"type": "text", "placeholders": {}}, "document": "<PERSON><PERSON><PERSON> l<PERSON>", "@document": {"type": "text", "placeholders": {}}, "link": "<PERSON><PERSON><PERSON>", "@link": {"type": "text", "placeholders": {}}, "downloadComplete": "<PERSON><PERSON><PERSON> xu<PERSON>ng thành công", "@downloadComplete": {"type": "text", "placeholders": {}}, "video": "Video", "@video": {"type": "text", "placeholders": {}}, "pathNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đường dẫn thư mục", "@pathNotFound": {"type": "text", "placeholders": {}}, "permissionDenied": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "@permissionDenied": {"type": "text", "placeholders": {}}, "media": "Ảnh/video", "@media": {"type": "text", "placeholders": {}}, "dayNow": "<PERSON><PERSON><PERSON> nay", "@dayNow": {"type": "text", "placeholders": {}}, "addMember": "<PERSON><PERSON><PERSON><PERSON> thành viên", "@addMember": {"type": "text", "placeholders": {}}, "leaveGroup": "Rờ<PERSON>", "@leaveGroup": {"type": "text", "placeholders": {}}, "warningRemoveMember": "Bạn có chắc muốn xóa thành viên này?", "@warningRemoveMember": {"type": "text", "placeholders": {}}, "warningLeaveGroup": "Bạn có chắc muốn rời nhóm?", "@warningLeaveGroup": {"type": "text", "placeholders": {}}, "ruleStory": "<PERSON><PERSON><PERSON> tư<PERSON> của bài viết", "@ruleStory": {"type": "text", "placeholders": {}}, "postStory": "<PERSON><PERSON><PERSON>", "@postStory": {"type": "text", "placeholders": {}}, "titleEditOptionStory": "Chỉnh sửa bài viết", "@titleEditOptionStory": {"type": "text", "placeholders": {}}, "titleDeleteOptionStory": "<PERSON>óa b<PERSON>i vi<PERSON>t", "@titleDeleteOptionStory": {"type": "text", "placeholders": {}}, "titleEditOptionComment": "Chỉnh sửa bình luận", "@titleEditOptionComment": {"type": "text", "placeholders": {}}, "titleDeleteOptionComment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "@titleDeleteOptionComment": {"type": "text", "placeholders": {}}, "hintContentStory": "Bạn đang nghĩ gì?", "@hintContentStory": {"type": "text", "placeholders": {}}, "recently": "<PERSON><PERSON><PERSON><PERSON>", "@recently": {"type": "text", "placeholders": {}}, "chatConservation": "<PERSON><PERSON><PERSON>", "@chatConservation": {"type": "text", "placeholders": {}}, "nearAddress": "Địa chỉ gần đây", "@nearAddress": {"type": "text", "placeholders": {}}, "currentAddress": "Đ<PERSON>a chỉ của bạn", "@currentAddress": {"type": "text", "placeholders": {}}, "shareStaff": "Chia sẻ người khác", "@shareStaff": {"type": "text", "placeholders": {}}, "shareAddress": "Chia sẻ địa chỉ", "@shareAddress": {"type": "text", "placeholders": {}}, "hintFindStaff": "<PERSON><PERSON><PERSON> tên <PERSON>", "@hintFindStaff": {"type": "text", "placeholders": {}}, "hintFindAddress": "<PERSON><PERSON><PERSON> đ<PERSON>a chỉ cần share", "@hintFindAddress": {"type": "text", "placeholders": {}}, "isTags": "c<PERSON>ng với", "@isTags": {"type": "text", "placeholders": {}}, "isLocation": "tại", "@isLocation": {"type": "text", "placeholders": {}}, "isLocation2": "đang ở", "@isLocation2": {"type": "text", "placeholders": {}}, "notCheckin": "<PERSON><PERSON><PERSON><PERSON> chấm công", "@notCheckin": {"type": "text", "placeholders": {}}, "getLocationCurrent": "<PERSON><PERSON><PERSON> địa chỉ hiện tại", "@getLocationCurrent": {"type": "text", "placeholders": {}}, "checkin2": "Vào", "@checkin2": {"type": "text", "placeholders": {}}, "checkout2": "Ra", "@checkout2": {"type": "text", "placeholders": {}}, "selectTag": "<PERSON><PERSON><PERSON><PERSON> đã chọn", "@selectTag": {"type": "text", "placeholders": {}}, "selectAddress": "Địa chỉ đã chọn", "@selectAddress": {"type": "text", "placeholders": {}}, "unSelect": "Bỏ chọn", "@unSelect": {"type": "text", "placeholders": {}}, "groupOrPerson": "Nhóm hoặc cá nhân", "@groupOrPerson": {"type": "text", "placeholders": {}}, "replyTo": "<PERSON><PERSON><PERSON> lờ<PERSON> {name}", "@replyTo": {"type": "text", "placeholders": {"name": {"type": "String"}}}, "groupCreatedRecently": "Nhóm vừa đ<PERSON><PERSON><PERSON> tạo", "@groupCreatedRecently": {"type": "text", "placeholders": {}}, "userSentObject": "{name} đ<PERSON> g<PERSON>i một {object}", "@userSentObject": {"type": "text", "placeholders": {"name": {"type": "String"}, "object": {"type": "String"}}}, "file": "<PERSON><PERSON><PERSON> tin", "@file": {"type": "text", "placeholders": {}}, "openTime": "<PERSON><PERSON><PERSON><PERSON> gian làm việc: ", "@openTime": {"type": "text", "placeholders": {}}, "creatingStory": "<PERSON><PERSON> đ<PERSON>ng bài ", "@creatingStory": {"type": "text", "placeholders": {}}, "edited": "Đã sửa", "@edited": {"type": "text", "placeholders": {}}, "storyPreviewImage": "<PERSON><PERSON>", "@storyPreviewImage": {"type": "text", "placeholders": {}}, "watchImage": "<PERSON><PERSON>", "@watchImage": {"type": "text", "placeholders": {}}, "updateAvatar": "<PERSON><PERSON><PERSON> nh<PERSON>t <PERSON>nh đ<PERSON>n", "@updateAvatar": {"type": "text", "placeholders": {}}, "pinConversation": "<PERSON><PERSON> h<PERSON> tho<PERSON>i", "@pinConversation": {"type": "text", "placeholders": {}}, "unpinConversation": "Bỏ ghim hộp tho<PERSON>i", "@unpinConversation": {"type": "text", "placeholders": {}}, "departmentRoom": "Phòng ban", "@departmentRoom": {"type": "text", "placeholders": {}}, "inDay": "<PERSON><PERSON><PERSON><PERSON>", "@inDay": {"type": "text", "placeholders": {}}, "validateBranchDepartment": "<PERSON><PERSON> lòng bổ sung chi nhánh và bộ phận", "@validateBranchDepartment": {"type": "text", "placeholders": {}}, "validateNote": "<PERSON><PERSON> sung địa điểm công tác vào ô <PERSON>hi chú", "@validateNote": {"type": "text", "placeholders": {}}, "validateInday": "<PERSON><PERSON> lòng chọn bu<PERSON>i", "@validateInday": {"type": "text", "placeholders": {}}, "titleEmoji": "<PERSON><PERSON>n cảm thấy thế nào?", "@titleEmoji": {"type": "text", "placeholders": {}}, "feeling": "<PERSON><PERSON> cảm thấy", "@feeling": {"type": "text", "placeholders": {}}, "forward": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>", "@forward": {"type": "text", "placeholders": {}}, "forwardTo": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> {name}", "@forwardTo": {"type": "text", "placeholders": {"name": {"type": "String"}}}, "forwardFrom": "<PERSON><PERSON><PERSON><PERSON> tiếp từ {name}", "@forwardFrom": {"type": "text", "placeholders": {"name": {"type": "String"}}}, "forwardMessageSuccess": "<PERSON><PERSON><PERSON><PERSON> tiếp tin nhắn thành công", "@forwardMessageSuccess": {"type": "text", "placeholders": {}}, "storyDetail": "<PERSON><PERSON><PERSON> vi<PERSON>", "@storyDetail": {"type": "text", "placeholders": {}}, "updating": "<PERSON><PERSON> cập nhật...", "@updating": {"type": "text", "placeholders": {}}, "warningDeleteGroup": "Bạn có chắc muốn xóa nhóm?", "@warningDeleteGroup": {"type": "text", "placeholders": {}}, "bugReport": "Báo lỗi", "@bugReport": {"type": "text", "placeholders": {}}, "doYouDeleteStory": "Xoá bài viết?", "doYouDeleteComment": "<PERSON><PERSON><PERSON> bình luận?", "notFoundTag": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhân viên", "@notFoundTag": {"type": "text", "placeholders": {}}, "turnOnNoti": "<PERSON><PERSON><PERSON> thông báo", "@turnOnNoti": {"type": "text", "placeholders": {}}, "downloading": "<PERSON><PERSON> tả<PERSON> x<PERSON> {progress}", "@downloading": {"type": "text", "placeholders": {"progress": {"type": "String"}}}, "updateBackground": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> n<PERSON>n", "@updateBackground": {"type": "text", "placeholders": {}}, "reaction": "<PERSON><PERSON><PERSON>", "@reaction": {"type": "text", "placeholders": {}}, "playSpeed": "<PERSON><PERSON><PERSON> độ phát", "@playSpeed": {"type": "text", "placeholders": {}}, "playSpeedNomal": "<PERSON><PERSON><PERSON>", "@playSpeedNomal": {"type": "text", "placeholders": {}}, "uploadFileFailure": "Upload thất b<PERSON>i", "@uploadFileFailure": {"type": "text", "placeholders": {}}, "joinNow": "<PERSON>ham gia ngay", "@joinNow": {"type": "text", "placeholders": {}}, "joinGroup": "<PERSON>ham gia nhóm", "@joinGroup": {"type": "text", "placeholders": {}}, "inviteLink": "<PERSON><PERSON><PERSON> kết mời", "@inviteLink": {"type": "text", "placeholders": {}}, "copiedInviteLink": "Đã sao chép link tham gia nhóm", "@copiedInviteLink": {"type": "text", "placeholders": {}}, "titleNewPost": "<PERSON><PERSON><PERSON> bài mới", "@titleNewPost": {"type": "text", "placeholders": {}}, "updateCheckinFailure": "<PERSON><PERSON><PERSON> nhật công thất bại", "@updateCheckinFailure": {"type": "text", "placeholders": {}}, "callByApp": "<PERSON><PERSON><PERSON> qua ứng dụng", "@callByApp": {"type": "text", "placeholders": {}}, "vpnDetectionWarning": "Bạn đang bật VPN vui lòng tắt để tiếp tục sử dụng ứng dụng", "@vpnDetectionWarning": {"type": "text", "placeholders": {}}, "doYouDeleteNoti": "Xoá thông báo này?", "@doYouDeleteNoti": {"type": "text", "placeholders": {}}, "recordToSendMessage": "Ứng dụng sử dụng quyền truy cập để ghi âm tin nhắn thoại", "@recordToSendMessage": {"type": "text", "placeholders": {}}, "changePassword": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "@changePassword": {"type": "text", "placeholders": {}}, "currentPassword": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "@currentPassword": {"type": "text", "placeholders": {}}, "newPassword": "<PERSON><PERSON><PERSON> mới", "@newPassword": {"type": "text", "placeholders": {}}, "changePasswordSuccess": "<PERSON><PERSON><PERSON> nhật mật khẩu thành công", "@changePasswordSuccess": {"type": "text", "placeholders": {}}, "warningUploadFile": "<PERSON><PERSON> đ<PERSON>ng tin, bạn có muốn huỷ để sang tab khác?", "@warningUploadFile": {"type": "text", "placeholders": {}}, "confirmRemoveUploadFile": "Huỷ ngay", "@confirmRemoveUploadFile": {"type": "text", "placeholders": {}}, "keepPage": "Ở lại trang", "@keepPage": {"type": "text", "placeholders": {}}, "createFolder": "<PERSON><PERSON><PERSON> thư mục", "@createFolder": {"type": "text", "placeholders": {}}, "recommendFolder": "<PERSON><PERSON> xuất thư mục", "@recommendFolder": {"type": "text", "placeholders": {}}, "unread": "<PERSON><PERSON><PERSON>", "@unread": {"type": "text", "placeholders": {}}, "addUnreadFolder": "<PERSON><PERSON><PERSON><PERSON> <PERSON>thêm’ để tạo thư mục <PERSON><PERSON> đọc", "@addUnreadFolder": {"type": "text", "placeholders": {}}, "addPersonalFolder": "<PERSON><PERSON><PERSON><PERSON> <PERSON>thêm’ để tạo thư mục <PERSON> nhân", "@addPersonalFolder": {"type": "text", "placeholders": {}}, "personal": "Cá nhân", "@personal": {"type": "text", "placeholders": {}}, "folder": "<PERSON><PERSON><PERSON>", "@folder": {"type": "text", "placeholders": {}}, "createAFolder": "Tạo 1 thư mục", "@createAFolder": {"type": "text", "placeholders": {}}, "allConversations": "<PERSON><PERSON><PERSON> cả các cuộc trò chuy<PERSON>n", "@allConversations": {"type": "text", "placeholders": {}}, "editFolderHint": "<PERSON><PERSON><PERSON><PERSON> ‘sửa’ để thay đổi hoặc xóa thư mục", "@editFolderHint": {"type": "text", "placeholders": {}}, "createFolderIntro": "<PERSON><PERSON><PERSON> thư mục cho các nhóm trò chuyện khác nhau và nhanh chóng chuyển đổi.", "@createFolderIntro": {"type": "text", "placeholders": {}}, "newFolder": "<PERSON><PERSON><PERSON> m<PERSON> mới", "@newFolder": {"type": "text", "placeholders": {}}, "folderName": "<PERSON><PERSON><PERSON> th<PERSON> mục", "@folderName": {"type": "text", "placeholders": {}}, "addConversation": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>", "@addConversation": {"type": "text", "placeholders": {}}, "selectedConversation": "<PERSON><PERSON><PERSON> tho<PERSON>i đã chọn", "@selectedConversation": {"type": "text", "placeholders": {}}, "addConversationHint": "<PERSON><PERSON><PERSON><PERSON> hội thoại sẽ xuất hiện trong thư mục này.", "@addConversationHint": {"type": "text", "placeholders": {}}, "warningRemoveFolder": "Bạn có chắc muốn xóa thư mục này?", "@warningRemoveFolder": {"type": "text", "placeholders": {}}, "editFolder": "<PERSON><PERSON><PERSON> th<PERSON> mục", "@editFolder": {"type": "text", "placeholders": {}}, "inefficient": "<PERSON><PERSON><PERSON><PERSON> hiệu quả", "@inefficient": {"type": "text", "placeholders": {}}, "titleTreatMent": "<PERSON><PERSON><PERSON> trình", "@titleTreatMent": {"type": "text", "placeholders": {}}, "titleCommit": "<PERSON>", "@titleCommit": {"type": "text", "placeholders": {}}, "titleNVCS": "NVCS", "@titleNVCS": {"type": "text", "placeholders": {}}, "titleEndTreatMent": "<PERSON><PERSON><PERSON> th<PERSON>c li<PERSON>u trình", "@titleEndTreatMent": {"type": "text", "placeholders": {}}, "titleCommitInfo": "Thông tin lúc cam kết", "@titleCommitInfo": {"type": "text", "placeholders": {}}, "titleProductDrinkLather": "<PERSON><PERSON><PERSON> phẩm (b<PERSON><PERSON>,uống ghi rõ công thức)", "@titleProductDrinkLather": {"type": "text", "placeholders": {}}, "titleTreatMentTime": "<PERSON>h<PERSON><PERSON> gian bao lâu đi 1 lần, 1 lần bao nhi<PERSON>u ph<PERSON>t", "@titleTreatMentTime": {"type": "text", "placeholders": {}}, "titleTotalMoney": "T<PERSON>ng số tiền làm ốm", "@titleTotalMoney": {"type": "text", "placeholders": {}}, "titleTreatmentZone": "<PERSON><PERSON> xu<PERSON>, c<PERSON><PERSON> ng<PERSON>, vùng điều trị", "@titleTreatmentZone": {"type": "text", "placeholders": {}}, "titleHigh": "<PERSON><PERSON><PERSON> cao", "@titleHigh": {"type": "text", "placeholders": {}}, "titleWeight": "Cân nặng", "@titleWeight": {"type": "text", "placeholders": {}}, "titleMeasurements": "Số đo", "@titleMeasurements": {"type": "text", "placeholders": {}}, "pinnedMessage": "<PERSON> ghim", "@pinnedMessage": {"type": "text", "placeholders": {}}, "pinMessage": "<PERSON><PERSON> tin n<PERSON>n", "@pinMessage": {"type": "text", "placeholders": {}}, "unpinMessage": "Bỏ ghim tin nhắn", "@unpinMessage": {"type": "text", "placeholders": {}}, "warningHotfixUpdate": "Đã có bản cập nhật mới, bạn có muốn khởi động lại ứng dụng để hoàn tất cập nhật ?", "@warningHotfixUpdate": {"type": "text", "placeholders": {}}, "checkUpdate": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t", "@checkUpdate": {"type": "text", "placeholders": {}}, "noAvailableUpdate": "<PERSON><PERSON><PERSON><PERSON> có bản cập nhật khả dụng", "@noAvailableUpdate": {"type": "text", "placeholders": {}}, "titleAddOption": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON> ch<PERSON>n", "@titleAddOption": {"type": "text", "placeholders": {}}, "option": "<PERSON><PERSON>", "@option": {"type": "text", "placeholders": {}}, "inputTitle": "<PERSON><PERSON><PERSON><PERSON> tiêu đề", "@inputTitle": {"type": "text", "placeholders": {}}, "maxOption": "Bạn có thể tạo tối đa 8 tu<PERSON> chọn", "@maxOption": {"type": "text", "placeholders": {}}, "unVote": "<PERSON><PERSON><PERSON>", "@unVote": {"type": "text", "placeholders": {}}, "vote": "<PERSON><PERSON><PERSON>", "@vote": {"type": "text", "placeholders": {}}, "timeoutMessage": "<PERSON><PERSON><PERSON> thời gian chờ xử lý, vui lòng thử lại", "@timeoutMessage": {"type": "text", "placeholders": {}}, "beforeDay": "<PERSON><PERSON><PERSON>", "@beforeDay": {"type": "text", "placeholders": {}}, "readAllNoti": "<PERSON><PERSON><PERSON> tất cả", "@readAllNoti": {"type": "text", "placeholders": {}}, "titleAllNoti": "<PERSON><PERSON><PERSON> cả thông báo", "@titleAllNoti": {"type": "text", "placeholders": {}}, "unpinAllMessageWarning": "Bạn có chắc muốn bỏ ghim tất cả tin nhắn?", "@unpinAllMessageWarning": {"type": "text", "placeholders": {}}, "unpinAllMessage": "Bỏ ghim tất cả tin nhắn", "@unpinAllMessage": {"type": "text", "placeholders": {}}, "createPoll": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>n", "@createPoll": {"type": "text", "placeholders": {}}, "voting": "<PERSON><PERSON><PERSON>", "@voting": {"type": "text", "placeholders": {}}, "optional": "<PERSON><PERSON><PERSON>", "@optional": {"type": "text", "placeholders": {}}, "createNewOptional": "Tạo lựa chọn mới", "@createNewOptional": {"type": "text", "placeholders": {}}, "canCreate8Optional": "Bạn có thể tạo tối đa 8 lựa chọn", "@canCreate8Optional": {"type": "text", "placeholders": {}}, "anonymousVoting": "Ẩn người lựa chọn", "@anonymousVoting": {"type": "text", "placeholders": {}}, "multipleAnswers": "<PERSON><PERSON><PERSON> nhi<PERSON>u l<PERSON>a chọn", "@multipleAnswers": {"type": "text", "placeholders": {}}, "addOptional": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> ch<PERSON>n", "@addOptional": {"type": "text", "placeholders": {}}, "voted": "<PERSON><PERSON> b<PERSON>nh ch<PERSON>n", "@voted": {"type": "text", "placeholders": {}}, "anonymous": "Ẩn danh", "@anonymous": {"type": "text", "placeholders": {}}, "viewResults": "<PERSON><PERSON> kế<PERSON> quả", "@viewResults": {"type": "text", "placeholders": {}}, "retractVote": "Bỏ bình ch<PERSON>n", "@retractVote": {"type": "text", "placeholders": {}}, "stopVote": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> b<PERSON>nh <PERSON>n", "@stopVote": {"type": "text", "placeholders": {}}, "addImage": "<PERSON><PERSON><PERSON><PERSON>", "@addImage": {"type": "text", "placeholders": {}}, "addSomeImage": "(<PERSON><PERSON> thêm {total} ảnh)", "@addSomeImage": {"type": "text", "placeholders": {"total": {"type": "int"}}}, "addImageFromSystem": "<PERSON><PERSON><PERSON>nh từ phần mềm", "@addImageFromSystem": {"type": "text", "placeholders": {}}, "effective": "<PERSON><PERSON><PERSON> quả", "@effective": {"type": "text", "placeholders": {}}, "notEffective": "<PERSON><PERSON><PERSON><PERSON> hiệu quả", "@notEffective": {"type": "text", "placeholders": {}}, "workPausedTitle": "<PERSON>uy trình đã tạm dừng", "@workPausedTitle": {"type": "text", "placeholders": {}}, "workPausedContent": "\nBạn không thể tiếp tục thao tác.\n<PERSON><PERSON> lòng thực hiện lại khi có thông báo.", "@workPausedContent": {"type": "text", "placeholders": {}}, "paused": "<PERSON><PERSON><PERSON>", "@paused": {"type": "text", "placeholders": {}}, "noFaceDetected": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khuôn mặt trong ảnh, bạn có muốn tiếp tục ?", "@noFaceDetected": {"type": "text", "placeholders": {}}, "multipleFaceDetected": "<PERSON><PERSON> khuôn mặt trong ảnh, vui lòng thử lại", "@multipleFaceDetected": {"type": "text", "placeholders": {}}, "lookStraight": "<PERSON><PERSON> lòng nhìn thẳng", "@lookStraight": {"type": "text", "placeholders": {}}, "lookStraightAndBlink": "<PERSON><PERSON> lòng nhìn thẳng và chớp mắt", "@lookStraightAndBlink": {"type": "text", "placeholders": {}}, "lookStraightAndSmile": "<PERSON><PERSON> lòng nhìn thẳng và cười", "@lookStraightAndSmile": {"type": "text", "placeholders": {}}, "turnLeft": "<PERSON>ui lòng xoay trái", "@turnLeft": {"type": "text", "placeholders": {}}, "turnRight": "<PERSON><PERSON> lòng xoay ph<PERSON>i", "@turnRight": {"type": "text", "placeholders": {}}, "faceVerify": "<PERSON><PERSON><PERSON> thực khu<PERSON>n mặt", "@faceVerify": {"type": "text", "placeholders": {}}, "titlePoll": "Câu hỏi", "@titlePoll": {"type": "text", "placeholders": {}}, "inputTitlePoll": "<PERSON><PERSON><PERSON><PERSON> câu hỏi", "@inputTitlePoll": {"type": "text", "placeholders": {}}, "titleOption": "<PERSON><PERSON><PERSON>", "@titleOption": {"type": "text", "placeholders": {}}, "inputTitleOption": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>n", "@inputTitleOption": {"type": "text", "placeholders": {}}, "inputCreateOption": "Tạo lựa chọn mới", "@inputCreateOption": {"type": "text", "placeholders": {}}, "max8Option": "Bạn có thể tạo tối đa 8 lựa chọn", "@max8Option": {"type": "text", "placeholders": {}}, "hiddenVoted": "Ẩn người lựa chọn", "@hiddenVoted": {"type": "text", "placeholders": {}}, "multipleVoted": "<PERSON><PERSON><PERSON> nhi<PERSON>u l<PERSON>a chọn", "@multipleVoted": {"type": "text", "placeholders": {}}, "titleModePoll": "<PERSON><PERSON><PERSON> ch<PERSON>n <PERSON>n danh", "@titleModePoll": {"type": "text", "placeholders": {}}, "emptyVote": "<PERSON><PERSON><PERSON><PERSON> có bình chọn", "@emptyVote": {"type": "text", "placeholders": {}}, "createVote": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>n", "@createVote": {"type": "text", "placeholders": {}}, "editPoll": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>n", "@editPoll": {"type": "text", "placeholders": {}}, "only8Option": "Chỉ tối đa 8 tu<PERSON> chọn", "@only8Option": {"type": "text", "placeholders": {}}, "refunOption": "<PERSON><PERSON><PERSON> l<PERSON>", "@refunOption": {"type": "text", "placeholders": {}}, "remindKYC": "Bạn chưa thực hiện xác thực khuôn mặt, vui lòng thực hiện để hỗ trợ việc chấm công chính xác hơn", "@remindKYC": {"type": "text", "placeholders": {}}, "alreadyKYCMessage": "Bạn đã xác thực khuôn mặt, vui lòng liên hệ nhân sự nếu cần xác thực lại", "@alreadyKYCMessage": {"type": "text", "placeholders": {}}, "ticket": "Ticket", "@ticket": {"type": "text", "placeholders": {}}, "createdBy": "tạo bởi", "@createdBy": {"type": "text", "placeholders": {}}, "by": "bởi", "@by": {"type": "text", "placeholders": {}}, "removeFilter": "Xoá lọ<PERSON>", "@removeFilter": {"type": "text", "placeholders": {}}, "groupRecieve": "<PERSON><PERSON><PERSON><PERSON>n", "@groupRecieve": {"type": "text", "placeholders": {}}, "qrCode": "Mã QR", "@qrCode": {"type": "text", "placeholders": {}}, "hintTextMedia": "Thê<PERSON> hình <PERSON>nh/video/file đ<PERSON>h kèm", "@hintTextMedia": {"type": "text", "placeholders": {}}, "hintTextOnlyMedia": "Thê<PERSON> h<PERSON>nh <PERSON>nh/video", "@hintTextOnlyMedia": {"type": "text", "placeholders": {}}, "hintTextTypeSelect": "-- <PERSON><PERSON><PERSON> lo<PERSON> --", "hintTextGroupRecieveSelect": "-- <PERSON><PERSON><PERSON> nhóm nhận --", "contentTicket": "Nội dung ticket", "messageOrderFoodSuccess": "<PERSON><PERSON><PERSON> mừng bạn đã đặt cơm thành công", "@messageOrderFoodSuccess": {"type": "text", "placeholders": {}}, "messageCreatedTicketSuccess": "<PERSON><PERSON><PERSON> mừng bạn đã tạo thành công ticket", "@messageCreatedTicketSuccess": {"type": "text", "placeholders": {}}, "messageCancelOrderFood": "Bạn có chắc chắn muốn huỷ đặt cơm?", "@messageCancelOrderFood": {"type": "text", "placeholders": {}}, "barCode": "Mã code", "@barCode": {"type": "text", "placeholders": {}}, "noteQR": "<PERSON>ui lòng đưa mã QR này cho nhà bếp để xác nhận phần cơm", "@noteQR": {"type": "text", "placeholders": {}}, "complaint": "Góp ý", "@complaint": {"type": "text", "placeholders": {}}, "regulations": "<PERSON><PERSON>", "@regulations": {"type": "text", "placeholders": {}}, "messageRatingFood": "Bạn thấy món ăn hôm nay như thế nào?", "@messageRatingFood": {"type": "text", "placeholders": {}}, "messageOrderFoodComplaint": "<PERSON><PERSON><PERSON>", "@messageOrderFoodComplaint": {"type": "text", "placeholders": {}}, "expiredOrderFood": "<PERSON><PERSON><PERSON><PERSON> gian chốt c<PERSON>m còn", "@expiredOrderFood": {"type": "text", "placeholders": {}}, "ticketType": "Loại ticket", "@ticketType": {"type": "text", "placeholders": {}}, "statusType": "Loại trạng thái", "@statusType": {"type": "text", "placeholders": {}}, "cameraV2": "Camera", "@cameraV2": {"type": "text", "placeholders": {}}, "thuoc": "<PERSON><PERSON><PERSON><PERSON>", "@thuoc": {"type": "text", "placeholders": {}}, "reply2": "<PERSON><PERSON><PERSON> lờ<PERSON>", "@reply2": {"type": "text", "placeholders": {}}, "allActive": "<PERSON><PERSON><PERSON> cả hoạt động", "@allActive": {"type": "text", "placeholders": {}}, "titleCollection": "<PERSON><PERSON><PERSON> mục tải lên", "@titleCollection": {"type": "text", "placeholders": {}}, "statusTypeSelected": "<PERSON><PERSON><PERSON> k<PERSON>m với trạng thái", "@statusTypeSelected": {"type": "text", "placeholders": {}}, "ticketTypeSelected": "Lọc kè<PERSON> với ticket", "@ticketTypeSelected": {"type": "text", "placeholders": {}}, "reasonCancel": "Lý do huỷ", "@reasonCancel": {"type": "text", "placeholders": {}}, "unRecept": "Bỏ tiếp nhận", "@unRecept": {"type": "text", "placeholders": {}}, "receptSuccess": "<PERSON><PERSON><PERSON><PERSON> nhận thành công", "@receptSuccess": {"type": "text", "placeholders": {}}, "completeTicketSuccess": "Bạn đã hoàn tất xong ticket", "@completeTicketSuccess": {"type": "text", "placeholders": {}}, "deleteTicketSuccess": "Bạn vừa huỷ 1 ticket", "@deleteTicketSuccess": {"type": "text", "placeholders": {}}, "deleteReceptSuccess": "Bạn vừa huỷ bỏ tiếp nhận ticket", "@deleteReceptSuccess": {"type": "text", "placeholders": {}}, "creatingTicket": "Đang tạo ticket", "@creatingTicket": {"type": "text", "placeholders": {}}, "createNewTicket": "Tạo mới ticket", "@createNewTicket": {"type": "text", "placeholders": {}}, "messageReportSuccess": "Đã góp ý thành công", "@messageReportSuccess": {"type": "text", "placeholders": {}}, "orderedFood": "<PERSON><PERSON> chốt c<PERSON>m", "@orderedFood": {"type": "text", "placeholders": {}}, "cancelRice": "Bạn đã huỷ đặt", "@cancelRice": {"type": "text", "placeholders": {}}, "expiredQR": "Mã QR đã hết hạn", "@expiredQR": {"type": "text", "placeholders": {}}, "imageEdit": "Bạn có muốn thoát?", "@imageEdit": {"type": "text", "placeholders": {}}, "createStickers": "Tạo sticker", "@createStickers": {"type": "text", "placeholders": {}}, "expireFood": "<PERSON><PERSON><PERSON><PERSON> gian chốt c<PERSON>m còn", "@expireFood": {"type": "text", "placeholders": {}}, "inputCodeManual": "<PERSON><PERSON><PERSON><PERSON> mã thủ công", "@inputCodeManual": {"type": "text", "placeholders": {}}, "scanCodeSuccess": "<PERSON><PERSON>t mã {code} thành công", "@scanQrCodeSuccess": {"type": "text", "placeholders": {"code": {"type": "String"}}}, "defaultAddress": "Địa chỉ mặc định", "@defaultAddress": {"type": "text", "placeholders": {}}, "setDefaultAddress": "Đặt làm địa chỉ mặc định", "@setDefaultAddress": {"type": "text", "placeholders": {}}, "addAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "@addAddress": {"type": "text", "placeholders": {}}, "remindBookingMealMessage": "Bạn có muốn đặt cơm cho ngày mai?", "@remindBookingMealMessage": {"type": "text", "placeholders": {}}, "pleaseSelectMealAddress": "<PERSON><PERSON> lòng chọn địa chỉ đặt cơm", "@pleaseSelectMealAddress": {"type": "text", "placeholders": {}}, "getOTPOnAcc": "<PERSON>ui lòng truy cập ACC để lấy OTP!", "@getOTPOnAcc": {"type": "text", "placeholders": {}}, "anonymousMessage": "Ẩn danh (<PERSON><PERSON><PERSON> ý của bạn sẽ được gửi ẩn danh)", "@anonymousMessage": {"type": "text", "placeholders": {}}, "detailProfile": "<PERSON><PERSON> sơ chi tiết", "@detailProfile": {"type": "text", "placeholders": {}}, "titleTreatmentDate": "<PERSON><PERSON><PERSON> tham gia", "@titleTreatmentDate": {"type": "text", "placeholders": {}}, "titleAssureWeight": "Số kg", "@titleAssureWeight": {"type": "text", "placeholders": {}}, "titleAssureSize": "Số đo", "@titleAssureSize": {"type": "text", "placeholders": {}}, "titleLoseWeight": "Số kg đã xuống", "@titleLoseWeight": {"type": "text", "placeholders": {}}, "titleTTKHAge": "<PERSON><PERSON><PERSON> ( ng<PERSON>y, tháng, n<PERSON>m sinh )", "@titleTTKHAge": {"type": "text", "placeholders": {}}, "titleTTKHOccupation": "<PERSON><PERSON><PERSON>", "@titleTTKHOccupation": {"type": "text", "placeholders": {}}, "titleTTKHMeasure": "Số đo 3 vòng", "@titleTTKHMeasure": {"type": "text", "placeholders": {}}, "titleTTKHOverWeight": "<PERSON><PERSON>", "@titleTTKHOverWeight": {"type": "text", "placeholders": {}}, "titleTypeOfBone": "Xuơng to hay nhỏ", "@titleTypeOfBone": {"type": "text", "placeholders": {}}, "titleTypeOfBirth": "Đã sinh con chưa? Sinh thường hay mổ?", "@titleTypeOfBirth": {"type": "text", "placeholders": {}}, "titleIsTreatment": "Có đang điều trị bệnh không?", "@titleIsTreatment": {"type": "text", "placeholders": {}}, "titleTTKHMeal": "Chế độ ăn hàng ngày ( 1 ngày mấy bữa, Thường ăn gì? )", "@titleTTKHMeal": {"type": "text", "placeholders": {}}, "titleTTKHDoExercise": "<PERSON>ậ<PERSON> luy<PERSON> ( đang tập gì?, <PERSON><PERSON> <PERSON><PERSON> phút 1 ngày? )", "@titleTTKHDoExercise": {"type": "text", "placeholders": {}}, "titleTTKHHouseWork": "Bạn có làm việc nhà hay không? Cụ thể công việc ? Mất bao nhiêu phút?", "@titleTTKHHouseWork": {"type": "text", "placeholders": {}}, "titleTTKHHistoryFit": "Có từng uống thuốc ốm không? Hay điều trị giảm béo ở đâu ? G<PERSON> rõ cụ thể", "@titleTTKHHistoryFit": {"type": "text", "placeholders": {}}, "titleTTKH": "TT khách hàng", "@titleTTKH": {"type": "text", "placeholders": {}}, "titleGNKQ": "<PERSON><PERSON> <PERSON>h<PERSON>n kết quả", "@titleGNKQ": {"type": "text", "placeholders": {}}, "titleGNKQSTT": "STT", "@titleGNKQSTT": {"type": "text", "placeholders": {}}, "titleGNKQDate": "<PERSON><PERSON><PERSON>", "@titleGNKQDate": {"type": "text", "placeholders": {}}, "titleGNKQArea": "<PERSON><PERSON><PERSON> điều trị, số đo", "@titleGNKQArea": {"type": "text", "placeholders": {}}, "titleGNKQMhs": "<PERSON><PERSON> trà", "@titleGNKQMhs": {"type": "text", "placeholders": {}}, "titleGNKQMhsEmpName": "Ng<PERSON>ời ra toa", "@titleGNKQMhsEmpName": {"type": "text", "placeholders": {}}, "titleGNKQDetailTreatment": "<PERSON> tiết điều trị tại nhà", "@titleGNKQDetailTreatment": {"type": "text", "placeholders": {}}, "titleGNKQLoseWeight": "Tổng g<PERSON>", "@titleGNKQLoseWeight": {"type": "text", "placeholders": {}}, "titleGNKQWeight": "Cân", "@titleGNKQWeight": {"type": "text", "placeholders": {}}, "titleGNKQResult": "<PERSON><PERSON><PERSON> quả", "@titleGNKQResult": {"type": "text", "placeholders": {}}, "titleGNKQTT": "TT", "@titleGNKQTT": {"type": "text", "placeholders": {}}, "titleGNKQStatus": "Tình Trạng", "@titleGNKQStatus": {"type": "text", "placeholders": {}}, "titleTTKHTitleSeconds": "( nhập số )", "@titleTTKHTitleSeconds": {"type": "text", "placeholders": {}}, "isValiGNKQ": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "@isValiGNKQ": {"type": "text", "placeholders": {}}, "titleNDTV": "ND TV", "@titleNDTV": {"type": "text", "placeholders": {}}, "doYouDeleteKQGH": "Bạn chắc chắn muốn xoá ghi nhận kết quả này ?", "@doYouDeleteKQGH": {"type": "text", "placeholders": {}}, "titleGNKQDelete": "<PERSON><PERSON><PERSON> ghi nhận", "@titleGNKQDelete": {"type": "text", "placeholders": {}}, "titleTabTTKH": "TTKH", "@titleTabTTKH": {"type": "text", "placeholders": {}}, "serviceEmpty": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON> vụ", "@serviceEmpty": {"type": "text", "placeholders": {}}, "treatmentHome": "<PERSON> tiết điều trị tại nhà", "@treatmentHome": {"type": "text", "placeholders": {}}, "classify": "<PERSON><PERSON> lo<PERSON>", "@classify": {"type": "text", "placeholders": {}}, "requiredSelectClassify": "<PERSON><PERSON> lòng chọn phân lo<PERSON>i", "@requiredSelectClassify": {"type": "text", "placeholders": {}}, "myTicket": "<PERSON><PERSON><PERSON> t<PERSON>i", "processingTicket": "<PERSON><PERSON> s<PERSON>ch xử lý", "@processingTicket": {"type": "text", "placeholders": {}}, "consumerId": "Mã KH", "@consumerId": {"type": "text", "placeholders": {}}, "consumerId2": "<PERSON><PERSON> kh<PERSON>ch hàng", "@consumerId2": {"type": "text", "placeholders": {}}, "hintEmployeeId": "-- <PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng --", "@hintEmployeeId": {"type": "text", "placeholders": {}}, "hintService": "-- <PERSON><PERSON><PERSON> d<PERSON> vụ --", "@hintService": {"type": "text", "placeholders": {}}, "hintStatus": "-- <PERSON><PERSON><PERSON> trạng thái --", "@hintStatus": {"type": "text", "placeholders": {}}, "updateTicket": "<PERSON><PERSON><PERSON> ticket", "@updateTicket": {"type": "text", "placeholders": {}}, "hintInputGroupTicket": "<PERSON><PERSON><PERSON> kiếm theo nhóm nhận", "@hintInputGroupTicket": {"type": "text", "placeholders": {}}, "hintInputTypeTicket": "<PERSON><PERSON><PERSON> kiếm theo tên lo<PERSON>i", "@hintInputTypeTicket": {"type": "text", "placeholders": {}}, "hintInputCustomerTicket": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>m theo tên, m<PERSON> <PERSON>", "@hintInputCustomerTicket": {"type": "text", "placeholders": {}}, "hintInputServiceTicket": "<PERSON><PERSON><PERSON> kiếm theo tên dịch vụ", "@hintInputServiceTicket": {"type": "text", "placeholders": {}}, "confirmComplete": "<PERSON><PERSON><PERSON>n hoàn tất", "@confirmComplete": {"type": "text", "placeholders": {}}, "followIssue": "<PERSON> s<PERSON> cố", "@followIssue": {"type": "text", "placeholders": {}}, "other": "K<PERSON><PERSON><PERSON>", "@other": {"type": "text", "placeholders": {}}, "giveBackTicket": "Bạn vừa bỏ tiếp nhận 1 ticket", "@giveBackTicket": {"type": "text", "placeholders": {}}, "messageUpdatedTicketSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t ticket thành công", "@messageUpdatedTicketSuccess": {"type": "text", "placeholders": {}}, "processTicketSuccess": "Xử lý ticket thành công", "@processTicketSuccess": {"type": "text", "placeholders": {}}, "titleProcess": "<PERSON><PERSON> lý", "@titleProcess": {"type": "text", "placeholders": {}}, "confirmProcess": "<PERSON><PERSON><PERSON>n x<PERSON> lý", "@confirmProcess": {"type": "text", "placeholders": {}}, "unitRequired": "<PERSON><PERSON> lòng nh<PERSON>p đơn vị t<PERSON>h", "@unitRequired": {"type": "text", "placeholders": {}}, "requiredRestart": "Ứng dụng cần khởi động lại để hoàn tất thiết lập!", "@requiredRestart": {"type": "text", "placeholders": {}}, "cc": "CC", "@cc": {"type": "text", "placeholders": {}}, "ccAction": "-- Chọn cc --", "@ccAction": {"type": "text", "placeholders": {}}, "requestEmp": "<PERSON><PERSON><PERSON><PERSON> yêu cầu", "@requestEmp": {"type": "text", "placeholders": {}}, "requestEmpAction": "-- <PERSON><PERSON><PERSON> ng<PERSON><PERSON> yêu cầu --", "@requestEmpAction": {"type": "text", "placeholders": {}}, "receptEmp": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n", "@receptEmp": {"type": "text", "placeholders": {}}, "receptEmpAction": "-- <PERSON><PERSON><PERSON> ng<PERSON><PERSON> tiế<PERSON> nhận --", "@receptEmpAction": {"type": "text", "placeholders": {}}, "deadline": "Deadline", "@deadline": {"type": "text", "placeholders": {}}, "rework": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON>c", "@rework": {"type": "text", "placeholders": {}}, "assignee": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON>c", "@assignee": {"type": "text", "placeholders": {}}, "assigneeAction": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON>c", "@assigneeAction": {"type": "text", "placeholders": {}}, "actionCancel": "vuốt để huỷ", "@actionCancel": {"type": "text", "placeholders": {}}, "assignedSuccess": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON> thành công", "@assignedSuccess": {"type": "text", "placeholders": {}}, "sendFrom": "<PERSON><PERSON><PERSON> từ", "@sendFrom": {"type": "text", "placeholders": {}}, "sendTo": "<PERSON><PERSON><PERSON>", "@sendTo": {"type": "text", "placeholders": {}}, "ccDisplay": "CC", "@ccDisplay": {"type": "text", "placeholders": {}}, "requestorDisplay": "<PERSON><PERSON><PERSON><PERSON> yêu cầu", "@requestorDisplay": {"type": "text", "placeholders": {}}, "isRecording": "<PERSON><PERSON><PERSON><PERSON> bị dang ghi âm...", "@isRecording": {"type": "text", "placeholders": {}}, "doYouDeleteAudio": "Bạn có muốn xoá ghi âm này?", "@doYouDeleteAudio": {"type": "text", "placeholders": {}}, "waitingForLoadingMessage": "<PERSON><PERSON> tải tin nh<PERSON>n", "@waitingForLoadingMessage": {"type": "text", "placeholders": {}}, "isCompleteRecord": "Bạn phải hoàn thành phần ghi âm trước", "@isCompleteRecord": {"type": "text", "placeholders": {}}, "custom": "<PERSON><PERSON><PERSON> chỉnh", "@custom": {"type": "text", "placeholders": {}}, "favoriteFeatures": "<PERSON><PERSON><PERSON> n<PERSON>ng yêu thích", "@favoriteFeatures": {"type": "text", "placeholders": {}}, "featureList": "<PERSON><PERSON> s<PERSON>ch t<PERSON>h n<PERSON>ng", "@featureList": {"type": "text", "placeholders": {}}, "searchByFeature": "<PERSON><PERSON><PERSON> theo tên t<PERSON>h năng", "@searchByFeature": {"type": "text", "placeholders": {}}, "completeEdit": "<PERSON><PERSON><PERSON> tất chỉnh sửa", "@completeEdit": {"type": "text", "placeholders": {}}, "markAsRead": "<PERSON><PERSON><PERSON> dấu đã đọc", "@markAsRead": {"type": "text", "placeholders": {}}, "download": "<PERSON><PERSON><PERSON>", "@download": {"type": "text", "placeholders": {}}, "alertHasFailedMessage": "Hiện có tin nhắn gửi không thành công, bạn vẫn muốn rời khỏi?", "@alertHasFailedMessage": {"type": "text", "placeholders": {}}, "clearCache": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "@clearCache": {"type": "text", "placeholders": {}}, "clearCacheMessage": "Bạn có chắc muốn xóa bộ nhớ đệm của ứng dụng?", "@clearCacheMessage": {"type": "text", "placeholders": {}}, "spEmp": "<PERSON>hân viên phụ", "@spEmp": {"type": "text", "placeholders": {}}, "age": "tu<PERSON>i", "@age": {"type": "text", "placeholders": {}}, "visitTime": "<PERSON><PERSON><PERSON> bó <PERSON>n", "@visitTime": {"type": "text", "placeholders": {}}, "imageBefore": "Ảnh trước", "imageAfter": "Ảnh sau", "customerImage": "<PERSON><PERSON> lý h<PERSON>nh <PERSON>nh", "@customerImage": {"type": "text", "placeholders": {}}, "emptyImage": "<PERSON><PERSON><PERSON>", "@emptyImage": {"type": "text", "placeholders": {}}, "customerImageReview": "<PERSON>em lại thông tin", "@customerImageReview": {"type": "text", "placeholders": {}}, "skinBodyF1": "<PERSON><PERSON><PERSON><PERSON> hàng quan tâm điều gì?", "@skinBodyF1": {"type": "text", "placeholders": {}}, "skinBodyF2": "Đã từng điệu trị da ở đâu? (<PERSON>i<PERSON>u trị như thế nào? Hiệu quả? Giá tiền? Nguyên nhân gây ra tình trạng da hiện tại? Bị bao lâu)?", "@skinBodyF2": {"type": "text", "placeholders": {}}, "skinBodyF3": "<PERSON><PERSON><PERSON><PERSON> hàng có bệnh lý gì đặc biệt? Hay đang điều trị bệnh gì không?", "@skinBodyF3": {"type": "text", "placeholders": {}}, "skinBodyF4": "Da có bị dị <PERSON>ng (<PERSON><PERSON><PERSON><PERSON>, thu<PERSON><PERSON> bôi, k<PERSON> bô<PERSON>, thờ<PERSON> tiết, thứ<PERSON> <PERSON>, ho<PERSON> chất)?", "@skinBodyF4": {"type": "text", "placeholders": {}}, "skinBodyF5": "<PERSON><PERSON><PERSON><PERSON> hàng có sử dụng các sản phẩm gây bong tróc như: peel da, sp <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON> kim, phi kim, laser, ti<PERSON><PERSON> ch<PERSON>ch, căng chỉ gì gần đây không?", "@skinBodyF5": {"type": "text", "placeholders": {}}, "skinBodyF6": "Thời gian khách hàng đến đư<PERSON><PERSON> với thẩm mỹ viện bao lâu 1 lần?", "@skinBodyF6": {"type": "text", "placeholders": {}}, "skinBodyF7": "<PERSON><PERSON><PERSON><PERSON> hàng có hay đi nắng không, có thói quen sử dụng kem chống nắng, có trang điểm thường xuyên?", "@skinBodyF7": {"type": "text", "placeholders": {}}, "skinBodyF8": "<PERSON><PERSON><PERSON><PERSON> hàng đang sử dụng sản phẩm, chăm sóc gì ở nhà?", "@skinBodyF8": {"type": "text", "placeholders": {}}, "skinBodyF9": "<PERSON><PERSON><PERSON><PERSON> hàng có đang uống thuốc giảm cân hay điều trị giảm cân?", "@skinBodyF9": {"type": "text", "placeholders": {}}, "titleTSKH": "<PERSON><PERSON><PERSON><PERSON> sử b<PERSON>nh về da", "@titleTSKH": {"type": "text", "placeholders": {}}, "titleTTBDKH": "TT ban đ<PERSON><PERSON>", "skinBodyTTF1": "<PERSON><PERSON><PERSON> da (<PERSON><PERSON><PERSON><PERSON>, hỗn hợp, khô, lý tưởng):", "@skinBodyTTF1": {"type": "text", "placeholders": {}}, "skinBodyTTF2": "<PERSON><PERSON> dày da (Mỏng, cực mỏng, bình thường, dà<PERSON>, sần vỏ cam):", "@skinBodyTTF2": {"type": "text", "placeholders": {}}, "skinBodyTTF3": "<PERSON><PERSON><PERSON> <PERSON>:", "@skinBodyTTF3": {"type": "text", "placeholders": {}}, "skinBodyTTF4": "Tình trạng da khách hàng:", "@skinBodyTTF4": {"type": "text", "placeholders": {}}, "skinBodyTTF5": "<PERSON>ấn đề khách hàng điều trị:", "@skinBodyTTF5": {"type": "text", "placeholders": {}}, "skinBodyTTF6": "<PERSON><PERSON><PERSON><PERSON> nhân/bệnh lý đặc biệt liên quan da:", "@skinBodyTTF6": {"type": "text", "placeholders": {}}, "skinBodyTTF7": "<PERSON><PERSON><PERSON><PERSON> hàng ký ghi rõ họ và tên", "@skinBodyTTF7": {"type": "text", "placeholders": {}}, "skinBodyTTF8": "<PERSON><PERSON><PERSON><PERSON> hàng đã đọc và đồng ý", "@skinBodyTTF8": {"type": "text", "placeholders": {}}, "resign": "<PERSON><PERSON> l<PERSON>", "@resign": {"type": "text", "placeholders": {}}, "kyten": "<PERSON><PERSON> t<PERSON>n", "@kyten": {"type": "text", "placeholders": {}}, "nextTo": "<PERSON><PERSON><PERSON><PERSON> theo", "@nextTo": {"type": "text", "placeholders": {}}, "totalProduct": "S<PERSON> l<PERSON> sản phẩm", "@totalProduct": {"type": "text", "placeholders": {}}, "caculateTime": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON>h", "@caculateTime": {"type": "text", "placeholders": {}}, "timeSave": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON>h", "@timeSave": {"type": "text", "placeholders": {}}, "totalCount": "Số lượng", "@totalCount": {"type": "text", "placeholders": {}}, "revenueEmp": "<PERSON><PERSON>o c<PERSON>o năng su<PERSON>t", "@revenueEmp": {"type": "text", "placeholders": {}}, "seen": "Đã xem", "@seen": {"type": "text", "placeholders": {}}, "react": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "@react": {"type": "text", "placeholders": {}}, "copy": "Sao chép", "@copy": {"type": "text", "placeholders": {}}, "paste": "Dán", "@paste": {"type": "text", "placeholders": {}}, "bold": "Bold", "@bold": {"type": "text", "placeholders": {}}, "italic": "Italic", "@italic": {"type": "text", "placeholders": {}}, "underline": "Underline", "@underline": {"type": "text", "placeholders": {}}, "sticker": "<PERSON>er", "@sticker": {"type": "text", "placeholders": {}}, "showInChat": "<PERSON><PERSON> tin nh<PERSON>n gốc", "@showInChat": {"type": "text", "placeholders": {}}, "sentMessage": "<PERSON><PERSON> gửi tin nh<PERSON>n", "@sentMessage": {"type": "text", "placeholders": {}}, "pleaseSelectConversation": "<PERSON><PERSON> lòng chọn hộ<PERSON> tho<PERSON>i", "@pleaseSelectConversation": {"type": "text", "placeholders": {}}, "selectionAll": "<PERSON><PERSON><PERSON> tất cả", "@selectionAll": {"type": "text", "placeholders": {}}, "emoji": "EMOJI ICONS", "@emoji": {"type": "text", "placeholders": {}}, "leadershipBoard": "Ban lãnh đạo", "@leadershipBoard": {"type": "text", "placeholders": {}}, "admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "@admin": {"type": "text", "placeholders": {}}, "removeMember": "<PERSON><PERSON><PERSON> thành viên", "@removeMember": {"type": "text", "placeholders": {}}, "customizeName": "<PERSON><PERSON><PERSON> Chỉnh Tên", "@customizeName": {"type": "text", "placeholders": {}}, "customizeNameHint": "T<PERSON><PERSON> chỉnh tên bạn muốn thay vì tên mặc định “admin”", "@customizeNameHint": {"type": "text", "placeholders": {}}, "adminCan": "<PERSON><PERSON><PERSON>n trị viên này có thể", "@adminCan": {"type": "text", "placeholders": {}}, "adminCanDetails": "Quản trị viên này sẽ có thể thêm quản trị viên mới với quyền ngang bằng hoặc ít hơn", "@adminCanDetails": {"type": "text", "placeholders": {}}, "transferOwnership": "<PERSON><PERSON><PERSON><PERSON> quyền Owner", "@transferOwnership": {"type": "text", "placeholders": {}}, "endTreatment": "<PERSON><PERSON><PERSON> th<PERSON>c li<PERSON>u trình", "@endTreatment": {"type": "text", "placeholders": {}}, "initialCondition": "<PERSON><PERSON><PERSON> trạng ban đầu", "@initialCondition": {"type": "text", "placeholders": {}}, "alertReExamDate": "<PERSON><PERSON><PERSON> tái khám phải khác ngày hiện tại", "@alertReExamDate": {"type": "text", "placeholders": {}}, "deleteConsumer": "Xoá KH", "@deleteConsumer": {"type": "text", "placeholders": {}}, "deleteCustoumerSuccess": "<PERSON><PERSON><PERSON> khách hàng thành công", "@deleteCustoumerSuccess": {"type": "text", "placeholders": {}}, "doYouDeleteCustomer": "Bạn muốn xoá khách hàng?", "@doYouDeleteCustomer": {"type": "text", "placeholders": {}}, "deleteAssign": "Xoá gán", "@deleteAssign": {"type": "text", "placeholders": {}}, "deleteAssignSuccess": "Xoá gán thành công", "@deleteAssignSuccess": {"type": "text", "placeholders": {}}, "doYouDeleteAssign": "Bạn muốn xoá thông tin gán này?", "@doYouDeleteAssign": {"type": "text", "placeholders": {}}, "isWorkingService": "<PERSON><PERSON><PERSON><PERSON> thể xoá gán, d<PERSON><PERSON> vụ đang làm", "@isWorkingService": {"type": "text", "placeholders": {}}, "deleteServiceQuestion": "Bạn có chắc muốn xóa dịch vụ này?", "@deleteServiceQuestion": {"type": "text", "placeholders": {}}, "deleteProductQuestion": "Bạn có chắc muốn xóa sản phẩm này?", "@deleteProductQuestion": {"type": "text", "placeholders": {}}, "anyoneCanJoin": "Bất kỳ ai đều có thể tham gia nhóm của bạn bằng cách nhấp vào liên kết này.", "@anyoneCanJoin": {"type": "text", "placeholders": {}}, "share": "<PERSON><PERSON> sẻ", "@share": {"type": "text", "placeholders": {}}, "groupType": "Loại nhóm", "@groupType": {"type": "text", "placeholders": {}}, "private": "<PERSON><PERSON><PERSON><PERSON> tư", "@private": {"type": "text", "placeholders": {}}, "chatHistory": "<PERSON><PERSON><PERSON> sử trò chuy<PERSON>n", "@chatHistory": {"type": "text", "placeholders": {}}, "show": "<PERSON><PERSON><PERSON>", "@show": {"type": "text", "placeholders": {}}, "permissions": "<PERSON><PERSON> quyền", "@permissions": {"type": "text", "placeholders": {}}, "setNewPhoto": "Đặt ảnh mới", "@setNewPhoto": {"type": "text", "placeholders": {}}, "addAdminHelpText": "<PERSON><PERSON>n có thể thêm người quản trị để giúp bạn quản lý nhóm của mình.", "@addAdminHelpText": {"description": "Text explaining that the user can add an admin to manage the group."}, "groupMemberActionTitle": "THÀNH VIÊN CỦA NHÓM NÀY CÓ THỂ LÀM GÌ?", "@groupMemberActionTitle": {"description": "Title asking what actions a group member can perform."}, "addPermission": "<PERSON><PERSON><PERSON><PERSON> quy<PERSON>", "@addPermission": {"description": "Button text for adding a permission."}, "addException": "<PERSON><PERSON><PERSON><PERSON> ngo<PERSON> lệ", "@addException": {"type": "text", "placeholders": {}}, "removeAdmin": "<PERSON><PERSON><PERSON> quản trị viên", "@removeAdmin": {"type": "text", "placeholders": {}}, "noSendTextPermission": "Bạn không có quyền gửi tin nhắn", "@noSendTextPermission": {"type": "text", "placeholders": {}}, "noSendImagePermission": "Bạn không có quyền gửi <PERSON>nh", "@noSendImagePermission": {"type": "text", "placeholders": {}}, "noSendVideoPermission": "<PERSON><PERSON><PERSON> không có quyền gửi video", "@noSendVideoPermission": {"type": "text", "placeholders": {}}, "noSendAudioPermission": "Bạn không có quyền gửi audio", "@noSendAudioPermission": {"type": "text", "placeholders": {}}, "noSendPollPermission": "Bạn không có quyền tạo bình chọn", "@noSendPollPermission": {"type": "text", "placeholders": {}}, "titleCompanion": "<PERSON><PERSON><PERSON><PERSON> đi cùng khách hàng", "@titleCompanion": {"type": "text", "placeholders": {}}, "titleJobCompanion": "<PERSON><PERSON><PERSON>", "@titleJobCompanion": {"type": "text", "placeholders": {}}, "titleRelationshipCompanion": "<PERSON><PERSON><PERSON> quan hệ", "@titleRelationshipCompanion": {"type": "text", "placeholders": {}}, "titleDescriptionCompanion": "Đặc điểm", "@titleDescriptionCompanion": {"type": "text", "placeholders": {}}, "selectCompanion": "<PERSON><PERSON><PERSON> ng<PERSON>ời đi cùng", "@selectCompanion": {"type": "text", "placeholders": {}}, "introAddCompanion": "<PERSON><PERSON>ấn (+) để thêm người đi cùng", "@introAddCompanion": {"type": "text", "placeholders": {}}, "addCompanion": "<PERSON><PERSON><PERSON><PERSON>", "@addCompanion": {"type": "text", "placeholders": {}}, "companion": "<PERSON><PERSON><PERSON><PERSON> đi cùng", "@companion": {"type": "text", "placeholders": {}}, "infoConsultation": "<PERSON><PERSON><PERSON><PERSON> tin tư vấn", "@infoConsultation": {"type": "text", "placeholders": {}}, "consultationEmp": "<PERSON><PERSON><PERSON> viên tư vấn", "@consultationEmp": {"type": "text", "placeholders": {}}, "consultationEmpBld": "BLD", "@consultationEmpBld": {"type": "text", "placeholders": {}}, "consultationRecept": "Tiền nhận TV", "@consultationRecept": {"type": "text", "placeholders": {}}, "moneyPt": "Tiền PT", "@moneyPt": {"type": "text", "placeholders": {}}, "debtTv": "Nợ TV", "@debtTv": {"type": "text", "placeholders": {}}, "moneyDoctor": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "@moneyDoctor": {"type": "text", "placeholders": {}}, "consultationTvcl": "TVCL", "@consultationTvcl": {"type": "text", "placeholders": {}}, "missedCall": "Cuộc gọi nhỡ", "@missedCall": {"description": "Label for missed call"}, "callEnded": "<PERSON><PERSON><PERSON> th<PERSON><PERSON>", "@callEnded": {"description": "Label for when a call ends"}, "outgoingCall": "<PERSON><PERSON><PERSON><PERSON> gọi đi", "@outgoingCall": {"description": "Label for outgoing calls"}, "incomingCall": "<PERSON><PERSON><PERSON><PERSON><PERSON> đến", "@incomingCall": {"description": "Label for incoming calls"}, "minute": "<PERSON><PERSON><PERSON>", "@minute": {"description": "Unit label for minutes"}, "second": "Giây", "@second": {"description": "Unit label for seconds"}, "callIsBlocked": "<PERSON><PERSON><PERSON> năng gọi tạm thời bị kh<PERSON>a", "@callIsBlocked": {"description": ""}, "infoDocument": "<PERSON><PERSON><PERSON>ng tin chứng từ", "@infoDocument": {"description": ""}, "numberDocument": "Số CT", "@numberDocument": {"description": ""}, "numberMoney": "<PERSON><PERSON> tiền", "@numberMoney": {"description": ""}, "dateDocument": "Ngày CT", "@dateDocument": {"description": ""}, "typeDocument": "Loại CT", "@typeDocument": {"description": ""}, "titleReason": "<PERSON><PERSON><PERSON>", "@titleReason": {"description": ""}, "reasonPaid": "<PERSON><PERSON><PERSON> to<PERSON>", "@reasonPaid": {"description": ""}, "datePaid": "<PERSON><PERSON><PERSON> c<PERSON>n chi", "@datePaid": {"description": ""}, "suggestList": "<PERSON><PERSON> s<PERSON>ch đề xuất", "@suggestList": {"description": ""}, "doYouRejectPurchase": "Bạn chắc chắn muốn từ chối phiếu này?", "@doYouRejectPurchase": {"description": ""}, "doYouConfirmPurchase": "Bạn chắc chắn muốn phê duyệt?", "@doYouConfirmPurchase": {"description": ""}, "requestApproved": "<PERSON><PERSON><PERSON> cầu đã đư<PERSON><PERSON> phê duy<PERSON>t", "@requestApproved": {"description": ""}, "gap": "<PERSON><PERSON><PERSON>", "@gap": {"description": ""}, "approvalList": "<PERSON><PERSON> s<PERSON>ch ph<PERSON>", "@approvalList": {"description": ""}, "processingHistory": "<PERSON><PERSON><PERSON> sử xử lý", "@processingHistory": {"description": ""}, "requestBill": "Báo giá", "@requestBill": {"description": ""}, "newMessage": "<PERSON> n<PERSON>ắn mới", "@newMessage": {"description": ""}, "requestRejected": "Bạn đã gửi từ chối yêu cầu này", "@requestRejected": {"description": ""}, "approval2": "<PERSON><PERSON><PERSON><PERSON>", "@approval2": {"description": ""}, "productConfirmEmpty": "<PERSON><PERSON><PERSON><PERSON> có phiếu nào cần phê duy<PERSON>t", "@productConfirmEmpty": {"description": ""}, "comfirmApprovalSuccess": "Bạn sẽ mất thông tin đã nhập nếu bỏ đi?", "@comfirmApprovalSuccess": {"description": ""}, "newUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t mới", "@newUpdate": {"description": ""}, "removeContent": "Bỏ đi", "@removeContent": {"description": ""}, "resumeContent": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> s<PERSON>a", "@resumeContent": {"description": ""}, "companies": "<PERSON><PERSON> s<PERSON>ch công ty", "@companies": {"type": "text", "placeholders": {}}, "searchById": "<PERSON><PERSON><PERSON> kiếm theo mã phiếu", "@searchById": {"type": "text", "placeholders": {}}, "byRoom": "<PERSON>", "@byRoom": {"type": "text", "placeholders": {}}, "searchRecent": "<PERSON><PERSON><PERSON> sử tìm kiếm", "@searchRecent": {"type": "text", "placeholders": {}}, "deleteAll": "<PERSON><PERSON><PERSON> tất cả", "@deleteAll": {"type": "text", "placeholders": {}}, "searchByTag": "<PERSON><PERSON><PERSON> k<PERSON>m theo tags", "@searchByTag": {"type": "text", "placeholders": {}}, "imageByRoom": "<PERSON><PERSON><PERSON>nh theo phòng", "@imageByRoom": {"type": "text", "placeholders": {}}, "setTag": "Gắn thẻ", "@setTag": {"type": "text", "placeholders": {}}, "mergedImageFailed": "<PERSON><PERSON><PERSON><PERSON>nh không thành công, vui lòng thử lại", "@mergedImageFailed": {"type": "text", "placeholders": {}}, "addTagImageSuccess": "Bạn đã hoàn tất gắn tags", "@addTagImageSuccess": {"type": "text", "placeholders": {}}, "singleImage": "Ảnh đơn", "@singleImage": {"type": "text", "placeholders": {}}, "mergedImage": "Ảnh ghép", "@mergedImage": {"type": "text", "placeholders": {}}, "sampleA": "Mẫu A", "sampleB": "Mẫu B", "selectedImage": "<PERSON><PERSON> chọn {count} <PERSON><PERSON>", "@selectedImage": {"type": "text", "placeholders": {"count": {"type": "String"}}}, "addTags": "Gắn tags", "@addTags": {"type": "text", "placeholders": {}}, "searchCustomerToAddTag": "<PERSON><PERSON><PERSON> k<PERSON>ch hàng để gắn tags", "@addTasearchCustomerToAddTagg": {"type": "text", "placeholders": {}}, "selectTags": "<PERSON><PERSON><PERSON> tags", "@selectTags": {"type": "text", "placeholders": {}}, "popularTags": "<PERSON><PERSON><PERSON> <PERSON> phổ biến", "@popularTags": {"type": "text", "placeholders": {}}, "imageIsSelected": "<PERSON><PERSON><PERSON> đ<PERSON> chọn", "@imageIsSelected": {"type": "text", "placeholders": {}}, "homePin": "<PERSON><PERSON><PERSON> n<PERSON> ghim", "@homePin": {"type": "text", "placeholders": {}}, "homeMain": "<PERSON><PERSON><PERSON> n<PERSON>ng ch<PERSON>h", "@homeMain": {"type": "text", "placeholders": {}}, "notFoundHistory": "<PERSON><PERSON><PERSON> c<PERSON> lịch sử", "@notFoundHistory": {"type": "text", "placeholders": {}}, "deleteTag": "Xoá tags", "@deleteTag": {"type": "text", "placeholders": {}}, "messageDeletedTag": "Bạn đã xoá tags thành công", "@messageDeletedTag": {"type": "text", "placeholders": {}}, "doYouDeleteTags": "Bạn chắc chắn muốn xoá tags ảnh này?", "@doYouDeleteTags": {"type": "text", "placeholders": {}}, "originalInfo": "<PERSON>hông tin ban đầu", "@originalInfo": {"type": "text", "placeholders": {}}, "requestHomeWidget": "Bạn có muốn tạo tiện ích ở màn hình chính để dễ dàng theo dõi việc chấm công hằng ngày?", "@requestHomeWidget": {"type": "text", "placeholders": {}}, "homeWidgetTip": "Bạn có thể tạo tiện ích ở màn hình chính để dễ dàng theo dõi việc chấm công hằng ngày", "@homeWidgetTip": {"type": "text", "placeholders": {}}, "loopByDay": "Lặp lại theo ngày", "@loopByDay": {"type": "text", "placeholders": {}}, "settingCheckinTime": "Cài đặt giờ chấm công", "recordScreen": "<PERSON><PERSON> màn hình", "@recordScreen": {"type": "text", "placeholders": {}}, "captionScreen": "<PERSON><PERSON><PERSON> màn hình", "@captionScreen": {"type": "text", "placeholders": {}}, "passNotMatch": "Mặt khẩu không khớp", "@passNotMatch": {"type": "text", "placeholders": {}}, "smallText": "Nhỏ", "@smallText": {"type": "text", "placeholders": {}}, "mediumText": "<PERSON>rung bình", "@mediumText": {"type": "text", "placeholders": {}}, "largeText": "Lớn", "@largeText": {"type": "text", "placeholders": {}}, "superLargeText": "Lớn n<PERSON>t", "@superLargeText": {"type": "text", "placeholders": {}}, "stopRecordScreen": "Dừng ghi", "@stopRecordScreen": {"type": "text", "placeholders": {}}}