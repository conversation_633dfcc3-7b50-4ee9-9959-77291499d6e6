name: custom_image_crop
description: An image cropper that is customizable. You can rotate, scale and translate either through gestures or a controller
version: 0.1.1
homepage: https://github.com/icapps/flutter-custom-image-crop

environment:
  sdk: '>=2.12.0 <4.0.0'
  flutter: ">=2.0.2"

dependencies:
  flutter:
    sdk: flutter
  gesture_x_detector: ^1.0.0
  vector_math: ^2.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
