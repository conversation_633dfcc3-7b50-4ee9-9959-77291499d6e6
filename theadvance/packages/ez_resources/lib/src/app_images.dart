class AppImages {
  AppImages._();

  static const String advanceLogo = 'assets/images/advance_logo.png';
  static const String androidHomeWidgetSample =
      'assets/images/android_home_widget_sample.png';
  static const String appointment = 'assets/images/appointment.png';
  static const String assignTask = 'assets/images/assign_task.png';
  static const String bgButtonHome = 'assets/images/bg_button_home.png';
  static const String bgButtonHome2 = 'assets/images/bg_button_home2.png';
  static const String bgCategoryVouchers =
      'assets/images/bg_category_vouchers.png';
  static const String bgCategoryVouchersOpacity =
      'assets/images/bg_category_vouchers_opacity.png';
  static const String bgLiveButton = 'assets/images/bg_live_button.png';
  static const String bgLoginOptions = 'assets/images/bg_login_options.png';
  static const String bgNoConnection = 'assets/images/bg_no_connection.png';
  static const String bgNotFoundGrey = 'assets/images/bg_not_found_grey.png';
  static const String bgNotFoundTicket =
      'assets/images/bg_not_found_ticket.png';
  static const String callBackground = 'assets/images/call_background.png';
  static const String card = 'assets/images/card.png';
  static const String chatBackground = 'assets/images/chat_background.png';
  static const String chatBackground_2 = 'assets/images/chat_background_2.png';
  static const String chatGreeting = 'assets/images/chat_greeting.png';
  static const String collaboratorOnboarding_01 =
      'assets/images/collaborator_onboarding_01.png';
  static const String collaboratorOnboarding_02 =
      'assets/images/collaborator_onboarding_02.png';
  static const String collaboratorOnboarding_03 =
      'assets/images/collaborator_onboarding_03.png';
  static const String consultationHeaderBg =
      'assets/images/consultation_header_bg.png';
  static const String consultationHeaderBgV2 =
      'assets/images/consultation_header_bg_v2.png';
  static const String customerBg = 'assets/images/customer_bg.png';
  static const String docExtension = 'assets/images/doc_extension.png';
  static const String error = 'assets/images/error.jpg';
  static const String excelExtension = 'assets/images/excel_extension.png';
  static const String fgLoginOptions = 'assets/images/fg_login_options.png';
  static const String file = 'assets/images/file.png';
  static const String gradientBg = 'assets/images/gradient_bg.png';
  static const String icSort = 'assets/images/ic_sort.png';
  static const String igSuccess = 'assets/images/ig_success.png';
  static const String imgBgNoConnection =
      'assets/images/img_bg_no_connection.png';
  static const String imgCustomerBookingHeaderBg =
      'assets/images/img_customer_booking_header_bg.png';
  static const String imgEvaluationResult =
      'assets/images/img_evaluation_result.png';
  static const String imgFolder = 'assets/images/img_folder.png';
  static const String iosHomeWidgetSample =
      'assets/images/ios_home_widget_sample.jpg';
  static const String nhomKH1 = 'assets/images/nhomKH1.png';
  static const String nhomKH2 = 'assets/images/nhomKH2.png';
  static const String otherExtension = 'assets/images/other_extension.png';
  static const String pdfExtension = 'assets/images/pdf_extension.jpeg';
  static const String triangle = 'assets/images/triangle.png';
  static const String vpnDetection = 'assets/images/vpn_detection.png';
  static const String warning = 'assets/images/warning.png';
  static const String yellowWarning = 'assets/images/yellow_warning.png';
}
